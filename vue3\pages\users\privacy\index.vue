<template>
  <view>
    <navbar></navbar>
    <view class="content" :style="{'margin-top': statusBarHeight + titleBarHeight + 'px'}">
      <mp-html :content="content" ref="article" :tag-style="tagStyle"></mp-html>
    </view>
  </view>
</template>

<script>
import navbar from '@/components/navbar/navbar.vue'
import { getUserAgreement } from '@/api/user.js'
import mpHtml from '@/uni_modules/mp-html/components/mp-html/mp-html.vue'

const app = getApp()

export default {
  components: {
    navbar,
    mpHtml
  },
  data() {
    return {
      statusBarHeight: app.globalData.statusBarHeight || 20,
      titleBarHeight: app.globalData.titleBarHeight || 44,
      content: "",
      tagStyle: {
        img: 'width:100%;display:block;',
        table: 'width:100%',
        video: 'width:100%'
      }
    }
  },
  onLoad(options) {
    getUserAgreement(options.type).then(res => {
				this.content = res.data.content
				uni.setNavigationBarTitle({
					title: res.data.title
				});
			}).catch(err => {
				that.$util.Tips({
					title: err
				});
			})
  },
}
</script>

<style>
page {
  background: #fff;
}

.content {
  width: calc(100% - 60rpx);
  margin: 30rpx;
  font-size: 24rpx;
  line-height: 2;
  padding: 20rpx 0;
}
</style> 