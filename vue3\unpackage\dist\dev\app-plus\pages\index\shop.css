
.uni-load-more[data-v-9245e42c] {
  display: flex;
  flex-direction: row;
  height: 1.875rem;
  align-items: center;
  justify-content: center;
}
.uni-load-more__text[data-v-9245e42c] {
  font-size: 0.75rem;
  margin-left: 0.5rem;
}
.uni-load-more__img[data-v-9245e42c] {
  width: 1.5rem;
  height: 1.5rem;
}
.uni-load-more__img--nvue[data-v-9245e42c] {
  color: #999;
}
.uni-load-more__img--android[data-v-9245e42c],
.uni-load-more__img--ios[data-v-9245e42c] {
  width: 1.5rem;
  height: 1.5rem;
  transform: rotate(0);
}
.uni-load-more__img--android[data-v-9245e42c] {
  animation: loading-ios 1s 0s linear infinite;
}
.uni-load-more__img--ios-H5[data-v-9245e42c] {
  position: relative;
  animation: loading-ios-H5-9245e42c 1s 0s step-end infinite;
}
.uni-load-more__img--ios-H5 uni-image[data-v-9245e42c] {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
}
@keyframes loading-ios-H5-9245e42c {
0% {
    transform: rotate(0);
}
8% {
    transform: rotate(30deg);
}
16% {
    transform: rotate(60deg);
}
24% {
    transform: rotate(90deg);
}
32% {
    transform: rotate(120deg);
}
40% {
    transform: rotate(150deg);
}
48% {
    transform: rotate(180deg);
}
56% {
    transform: rotate(210deg);
}
64% {
    transform: rotate(240deg);
}
73% {
    transform: rotate(270deg);
}
82% {
    transform: rotate(300deg);
}
91% {
    transform: rotate(330deg);
}
to {
    transform: rotate(360deg);
}
}
.uni-load-more__img--android-MP[data-v-9245e42c] {
  position: relative;
  width: 1.5rem;
  height: 1.5rem;
  transform: rotate(0);
  animation: loading-ios 1s 0s ease infinite;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon[data-v-9245e42c] {
  position: absolute;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: solid 0.125rem transparent;
  border-top: solid 0.125rem #999;
  transform-origin: center;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon[data-v-9245e42c]:nth-child(1) {
  animation: loading-android-MP-1-9245e42c 1s 0s linear infinite;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon[data-v-9245e42c]:nth-child(2) {
  animation: loading-android-MP-2-9245e42c 1s 0s linear infinite;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon[data-v-9245e42c]:nth-child(3) {
  animation: loading-android-MP-3-9245e42c 1s 0s linear infinite;
}
@keyframes loading-android-9245e42c {
0% {
    transform: rotate(0);
}
to {
    transform: rotate(360deg);
}
}
@keyframes loading-android-MP-1-9245e42c {
0% {
    transform: rotate(0);
}
50% {
    transform: rotate(90deg);
}
to {
    transform: rotate(360deg);
}
}
@keyframes loading-android-MP-2-9245e42c {
0% {
    transform: rotate(0);
}
50% {
    transform: rotate(180deg);
}
to {
    transform: rotate(360deg);
}
}
@keyframes loading-android-MP-3-9245e42c {
0% {
    transform: rotate(0);
}
50% {
    transform: rotate(270deg);
}
to {
    transform: rotate(360deg);
}
}


.lazy-image[data-v-337fa078]{position:relative;width:100%;height:100%;overflow:hidden;background:#f8f8f8;}
.lazy-image uni-image[data-v-337fa078]{width:100%;height:100%;}
.lazy-image .err[data-v-337fa078]{position:absolute;top:0;left:0;width:100%;height:100%;background:rgba(248,248,248,0.7) url('../../static/img/load.png') no-repeat center/50%;}


.money-box[data-v-433ff6d7] {
  display: flex;
  align-items: flex-end;
  font-weight: bolder;
}
.money-box .sale[data-v-433ff6d7] {
  margin: 0 0.0625rem;
}
.money-box .unit[data-v-433ff6d7] {
  margin-bottom: 0.0625rem;
}


/* APP端兼容性优化 - 移除CSS变量，使用具体值 */
.tabbar[data-v-e9b92a61]{
  position: fixed;
  z-index: 998;
  width: 100%;
  /* APP端兼容性：条件编译底部安全区域 */

  bottom: 1.5625rem;




  box-sizing: border-box;
  justify-content: center;
  pointer-events: none; /* 优化性能，只有子元素可点击 */
}
.tabbar-box[data-v-e9b92a61]{
  z-index: 998;
  width: calc(100% - 3.75rem);
  height: 3.125rem;
  border-radius: 1.5625rem;
  justify-content: space-around;
  /* APP端兼容性：条件编译backdrop-filter */




  pointer-events: auto; /* 恢复点击事件 */
  transition: all 0.3s ease; /* 添加过渡动画 */
}
.tb-bs[data-v-e9b92a61]{
  box-shadow: 0 0 0.75rem rgba(0, 0, 0, 0.06);
  border: 1px solid #f8f8f8;
}
.tabbar-box .tabbar-item[data-v-e9b92a61]{
  width: 20%;
  height: 3.125rem;
  justify-content: center;
  position: relative;
  transition: transform 0.2s ease; /* 添加点击反馈动画 */
  cursor: pointer;
}

/* 小程序兼容性：使用class替代:active伪类 */
.tabbar-item.active-state[data-v-e9b92a61] {
  transform: scale(0.95); /* 点击时缩放效果 */
}
.tabbar-item .icon[data-v-e9b92a61]{
  width: 1.5rem;
  height: 1.5rem;
  transition: opacity 0.2s ease; /* 图标切换动画 */
}
.tabbar-item .msg[data-v-e9b92a61]{
  position: absolute;
  top: 0.5625rem;
  left: calc(50% + 0.25rem);
  min-width: 1.0625rem;
  height: 1.0625rem;
  line-height: 1.0625rem;
  text-align: center;
  font-size: 0.5625rem;
  font-weight: 700;
  color: #fff;
  background: #fa5150;
  border-radius: 1.0625rem;
  border: 0.0625rem solid #fff;
}
.tabbar-item .add[data-v-e9b92a61]{
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 50%;
  justify-content: center;
  transition: all 0.3s ease-in-out;
}
.tabbar-item .add uni-image[data-v-e9b92a61]{
  width: 0.5rem;
  height: 0.5rem;
}
.tabbar .tabbar-add[data-v-e9b92a61]{
  position: fixed;
  z-index: 997;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  background-image: linear-gradient(to bottom, rgb(173 173 173 / 95%), rgb(25 25 25 / 95%));
  /* APP端兼容性：条件编译backdrop-filter */
}
.content-wrapper[data-v-e9b92a61] {
  width: 100%;
  padding-top: 5.625rem;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 头部标题区域 */
.add-header[data-v-e9b92a61] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 80%;
  padding-top: 2.8125rem;
}
.header-content[data-v-e9b92a61] {
  flex: 1;
}
.header-image[data-v-e9b92a61] {
  width: 9.375rem;
  height: 9.375rem;
}
.header-image uni-image[data-v-e9b92a61] {
  width: 100%;
  height: 100%;
  border-radius: 0.375rem;
}
.add-title[data-v-e9b92a61] {
  font-size: 1.25rem;
  font-weight: bold;
  color: #fff;
  display: flex;
  align-items: center;
}
.add-plus[data-v-e9b92a61] {
  margin-left: 0.25rem;
  font-weight: normal;
}
.add-subtitle[data-v-e9b92a61] {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 0.25rem;
}

/* 新增卡片样式 */
.card-container[data-v-e9b92a61] {
  width: 80%;
  display: flex;
  flex-direction: column;
  gap: 0.9375rem;
  margin-top: 7.5rem;
  pointer-events: auto; /* 确保容器可以传递点击事件 */
}

/* 卡片通用样式 */
.card[data-v-e9b92a61] {
  display: flex;
  align-items: center;
  border-radius: 0.625rem;
  padding: 0.9375rem;
  background-color: rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  pointer-events: auto; /* 确保卡片可以被点击 */
}

/* 小程序兼容性：使用class替代:active伪类 */
.card.active-state[data-v-e9b92a61] {
  transform: scale(0.98);
  background-color: rgba(255, 255, 255, 0.15);
}
.cream-card[data-v-e9b92a61] {
  background-color: rgba(255, 255, 255, 0.15);
}
.mint-card[data-v-e9b92a61] {
  background-color: rgba(255, 255, 255, 0.15);
}
.card-left[data-v-e9b92a61] {
  width: 1.875rem;
  height: 1.875rem;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.625rem;
}
.card-left uni-image[data-v-e9b92a61] {
  width: 1.25rem;
  height: 1.25rem;
}
.card-content[data-v-e9b92a61] {
  flex: 1;
}
.card-title[data-v-e9b92a61] {
  font-size: 0.875rem;
  font-weight: bold;
  color: #fff;
}
.card-subtitle[data-v-e9b92a61] {
  font-size: 0.6875rem;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 0.1875rem;
}
.card-right[data-v-e9b92a61] {
  width: 1.125rem;
  height: 1.125rem;
  transform: rotate(180deg);
}
.card-right uni-image[data-v-e9b92a61] {
  width: 100%;
  height: 100%;
}

/* 两列卡片布局 */
.two-column-container[data-v-e9b92a61] {
  display: flex;
  width: 100%;
  gap: 0.625rem;
}
.two-column-card[data-v-e9b92a61] {
  flex: 1;
  border-radius: 0.5rem;
  padding: 0.9375rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.3s ease;
  cursor: pointer;
  pointer-events: auto; /* 确保两列卡片可以被点击 */
}

/* 小程序兼容性：使用class替代:active伪类 */
.two-column-card.active-state[data-v-e9b92a61] {
  transform: scale(0.98);
}
.video-card[data-v-e9b92a61] {
  background-color: rgba(76, 130, 219, 0.3);
}
.audio-card[data-v-e9b92a61] {
  background-color: rgba(245, 166, 35, 0.3);
}
.card-content-left[data-v-e9b92a61] {
  flex: 1;
}
.two-column-card-title[data-v-e9b92a61] {
  font-size: 0.8125rem;
  font-weight: bold;
  color: #FFFFFF;
}
.two-column-card-desc[data-v-e9b92a61] {
  font-size: 0.625rem;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 0.1875rem;
}
.card-content-right[data-v-e9b92a61] {
  width: 1.5625rem;
  height: 1.5625rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
.card-content-right uni-image[data-v-e9b92a61] {
  width: 1.25rem;
  height: 1.25rem;
}
.close-btn uni-image[data-v-e9b92a61] {
  width: 0.75rem;
  height: 0.75rem;
}
.df[data-v-e9b92a61]{
  display: flex;
  align-items: center;
}
.bfh[data-v-e9b92a61]{
  background: rgba(0, 0, 0, 0.8);
}
.bfw[data-v-e9b92a61]{
  background: #fff;
}

/* 动画优化 - APP端兼容性 */
.fade-in[data-v-e9b92a61]{
  animation: fadeIn-e9b92a61 0.3s forwards;
}
.fade-out[data-v-e9b92a61]{
  animation: fadeOut-e9b92a61 0.3s forwards;
}
@keyframes fadeIn-e9b92a61{
from{
    opacity: 0;
    transform: translateY(0.3125rem);
}
to{
    opacity: 1;
    transform: translateY(0);
}
}
@keyframes fadeOut-e9b92a61{
from{
    opacity: 1;
    transform: translateY(0);
}
to{
    opacity: 0;
    transform: translateY(0.3125rem);
}
}


body{
  background:#f8f8f8
}
.nav-box{
  position:fixed;
  top:0;
  width:100%;
  z-index:99;
  box-sizing:border-box;
  background:#fff
}
.nav-box .nav-bar{
  width:calc(100% - 0.9375rem);
  padding:0 0.46875rem
}
.nav-bar .nav-icon{
  padding:0 0.46875rem;
  height:100%;
  position:relative
}
.nav-icon .msg{
  position:absolute;
  top:0;
  right:0;
  left:calc(50% + 0.25rem);
  min-width:0.9375rem;
  height:0.9375rem;
  line-height:0.9375rem;
  text-align:center;
  font-size:0.5625rem;
  font-weight:700;
  color:#fff;
  background:#000;
  border-radius:0.9375rem;
  border:0.125rem solid #fff
}
.nav-icon uni-image{
  width:1.4375rem;
  height:1.4375rem
}
.nav-box .nav-classify{
  width:100%;
  overflow:hidden;
  position:relative;
  transition:height .3s ease-in-out
}
.nav-box .classify-scroll{
  width:100%;
  white-space:nowrap
}
.classify-scroll .classify-box{
  width:100%;
  height:2.125rem
}
.classify-box .classify-item{
  flex-shrink:0;
  padding:0 0.9375rem;
  height:2.125rem;
  line-height:2.125rem;
  font-size:0.75rem;
  transition:all .3s ease-in-out
}
.nav-classify .classify-all{
  position:absolute;
  top:0;
  bottom:0;
  right:0;
  height:2.125rem;
  padding:0 0.9375rem;
  font-size:0.625rem;
  font-weight:700;
  background:linear-gradient(to right,rgba(255,255,255,0),#fff 20%);
  justify-content:center
}
.classify-all uni-image{
  margin-right:0.25rem;
  width:0.8125rem;
  height:0.8125rem
}
.content-box{
  width:100%;
  padding-bottom:5.625rem
}
.goods-box{
  width:100%;
  display:flex;
  flex-wrap:wrap
}
.goods-box .goods-banner,
.goods-box .goods-item{
  width:calc(50% - 0.46875rem);
  margin:0.3125rem 0 0 0.3125rem;
  background:#fff;
  border-radius:0.25rem;
  overflow:hidden
}
.goods-box .goods-banner{
  padding-top:calc(66.67% - 0.41656rem);
  position:relative
}
.goods-banner .banner-swiper{
  position:absolute;
  top:0;
  right:0;
  bottom:0;
  left:0;
  width:100%;
  height:100%
}
.goods-banner .swiper-idor{
  position:absolute;
  width:100%;
  bottom:0.625rem;
  justify-content:center
}
.swiper-idor .idor-item{
  margin:0 0.1875rem;
  width:0.25rem;
  height:0.25rem;
  border-radius:0.25rem;
  background:rgba(255,255,255,.3);
  transition:all .3s
}
.swiper-idor .active{
  width:0.75rem;
  background:rgba(255,255,255,.9)
}
.goods-item .goods-img{
  width:100%;
  padding-top:100%;
  position:relative
}
.goods-img .goods-img-item{
  position:absolute;
  top:0;
  right:0;
  bottom:0;
  left:0;
  width:100%;
  height:100%
}
.goods-item .goods-name{
  width:calc(100% - 1.25rem);
  margin:0.46875rem 0.625rem;
  font-size:0.8125rem;
  line-height:1.125rem;
  font-weight:500
}
.goods-item .goods-price{
  width:calc(100% - 0.9375rem);
  margin:0 0.625rem 0.625rem;
  display:flex;
  align-items:flex-end
}
.goods-price .price-h{
  margin-left:0.46875rem;
  color:#999;
  font-size:0.625rem;
  line-height:0.625rem
}
.goods-item .goods-tag{
  width:calc(100% - 0.9375rem);
  margin:0 0.46875rem 0.46875rem;
  display:flex;
  flex-wrap:wrap
}
.goods-tag .tag-item{
  margin:0 0.15625rem 0.15625rem;
  height:1.25rem;
  padding:0 0.375rem;
  line-height:1.25rem;
  font-size:0.5625rem;
  font-weight:500;
  background:#f8f8f8;
  border-radius:0.25rem
}
.df {
  display: flex;
  align-items: center;
}
.ohto {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.ohto2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.empty-box {
  flex-direction: column;
  padding: 3.75rem 0;
}
.empty-box uni-image {
  width: 8.75rem;
  height: 8.75rem;
}
.empty-box .e1 {
  margin-top: 1.25rem;
  font-size: 1rem;
  font-weight: bold;
}
.empty-box .e2 {
  margin-top: 0.625rem;
  font-size: 0.75rem;
  color: #999;
}
.heio {
  justify-content: center;
}
.microlabel {
  position: absolute;
  width: 0.5625rem;
  height: 0.5625rem;
  background: #fa5150;
  border-radius: 50%;
  border: 0.125rem solid #fff;
}
