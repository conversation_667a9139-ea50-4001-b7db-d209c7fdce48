
.uni-load-more[data-v-9245e42c] {
  display: flex;
  flex-direction: row;
  height: 1.875rem;
  align-items: center;
  justify-content: center;
}
.uni-load-more__text[data-v-9245e42c] {
  font-size: 0.75rem;
  margin-left: 0.5rem;
}
.uni-load-more__img[data-v-9245e42c] {
  width: 1.5rem;
  height: 1.5rem;
}
.uni-load-more__img--nvue[data-v-9245e42c] {
  color: #999;
}
.uni-load-more__img--android[data-v-9245e42c],
.uni-load-more__img--ios[data-v-9245e42c] {
  width: 1.5rem;
  height: 1.5rem;
  transform: rotate(0);
}
.uni-load-more__img--android[data-v-9245e42c] {
  animation: loading-ios 1s 0s linear infinite;
}
.uni-load-more__img--ios-H5[data-v-9245e42c] {
  position: relative;
  animation: loading-ios-H5-9245e42c 1s 0s step-end infinite;
}
.uni-load-more__img--ios-H5 uni-image[data-v-9245e42c] {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
}
@keyframes loading-ios-H5-9245e42c {
0% {
    transform: rotate(0);
}
8% {
    transform: rotate(30deg);
}
16% {
    transform: rotate(60deg);
}
24% {
    transform: rotate(90deg);
}
32% {
    transform: rotate(120deg);
}
40% {
    transform: rotate(150deg);
}
48% {
    transform: rotate(180deg);
}
56% {
    transform: rotate(210deg);
}
64% {
    transform: rotate(240deg);
}
73% {
    transform: rotate(270deg);
}
82% {
    transform: rotate(300deg);
}
91% {
    transform: rotate(330deg);
}
to {
    transform: rotate(360deg);
}
}
.uni-load-more__img--android-MP[data-v-9245e42c] {
  position: relative;
  width: 1.5rem;
  height: 1.5rem;
  transform: rotate(0);
  animation: loading-ios 1s 0s ease infinite;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon[data-v-9245e42c] {
  position: absolute;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: solid 0.125rem transparent;
  border-top: solid 0.125rem #999;
  transform-origin: center;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon[data-v-9245e42c]:nth-child(1) {
  animation: loading-android-MP-1-9245e42c 1s 0s linear infinite;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon[data-v-9245e42c]:nth-child(2) {
  animation: loading-android-MP-2-9245e42c 1s 0s linear infinite;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon[data-v-9245e42c]:nth-child(3) {
  animation: loading-android-MP-3-9245e42c 1s 0s linear infinite;
}
@keyframes loading-android-9245e42c {
0% {
    transform: rotate(0);
}
to {
    transform: rotate(360deg);
}
}
@keyframes loading-android-MP-1-9245e42c {
0% {
    transform: rotate(0);
}
50% {
    transform: rotate(90deg);
}
to {
    transform: rotate(360deg);
}
}
@keyframes loading-android-MP-2-9245e42c {
0% {
    transform: rotate(0);
}
50% {
    transform: rotate(180deg);
}
to {
    transform: rotate(360deg);
}
}
@keyframes loading-android-MP-3-9245e42c {
0% {
    transform: rotate(0);
}
50% {
    transform: rotate(270deg);
}
to {
    transform: rotate(360deg);
}
}


.uni-popup[data-v-4dd3c44b] {
  position: fixed;
  z-index: 99;
}
.uni-popup.top[data-v-4dd3c44b],
.uni-popup.left[data-v-4dd3c44b],
.uni-popup.right[data-v-4dd3c44b] {
  top: 0;
}
.uni-popup .uni-popup__wrapper[data-v-4dd3c44b] {
  display: block;
  position: relative;
  /* iphonex 等安全区设置，底部安全区适配 */
}
.uni-popup .uni-popup__wrapper.left[data-v-4dd3c44b],
.uni-popup .uni-popup__wrapper.right[data-v-4dd3c44b] {
  padding-top: 0;
  flex: 1;
}
.fixforpc-z-index[data-v-4dd3c44b] {

  z-index: 999;
}
.fixforpc-top[data-v-4dd3c44b] {
  top: 0;
}


.lazy-image[data-v-337fa078]{position:relative;width:100%;height:100%;overflow:hidden;background:#f8f8f8;}
.lazy-image uni-image[data-v-337fa078]{width:100%;height:100%;}
.lazy-image .err[data-v-337fa078]{position:absolute;top:0;left:0;width:100%;height:100%;background:rgba(248,248,248,0.7) url('../../static/img/load.png') no-repeat center/50%;}


.waterfall-box[data-v-8e26fd7f] {
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 0 0.25rem;
  box-sizing: border-box;
}
.waterfall-item[data-v-8e26fd7f] {
  width: calc(50% - 0.125rem);
}
.waterfall-note[data-v-8e26fd7f] {
  margin-bottom: 0.25rem;
  border-radius: 0.25rem;
  overflow: hidden;
  background: #fff;
  box-shadow: 0 0.03125rem 0.125rem rgba(0, 0, 0, 0.03);
}
.waterfall-note .waterfall-note-top[data-v-8e26fd7f] {
  width: 100%;
  position: relative;
  overflow: hidden;
  background: #f8f8f8;
}
.waterfall-note-top.text-only[data-v-8e26fd7f] {
  background: #f8f8f8;
  padding: 0.5rem;
  box-sizing: border-box;
  width: 100%;
  border-radius: 0.25rem 0.25rem 0 0;
  display: flex;
  flex-direction: column;
}
.text-content[data-v-8e26fd7f] {
  margin: 0 !important;
  font-size: 0.8125rem;
  line-height: 1.125rem;
  color: #333;
  word-break: break-word;
  white-space: pre-wrap;
}
.waterfall-note-top .xxiv[data-v-8e26fd7f],
.waterfall-note-top .xxa .xxa-icon[data-v-8e26fd7f] {
  filter: drop-shadow(0 0.0625rem 0.0625rem rgba(0, 0, 0, 0.2));
}
.waterfall-note-top .xxiv[data-v-8e26fd7f] {
  position: absolute;
  top: 0.625rem;
  right: 0.625rem;
  width: 0.875rem;
  height: 0.875rem;
}
.waterfall-note-top .xxa[data-v-8e26fd7f] {
  width: calc(100% - 2rem);
  height: calc(100% - 2rem);
  padding: 1rem;
  position: relative;
  z-index: 1;
  color: #fff;
}
.waterfall-note-top .xxa .xxa-bg[data-v-8e26fd7f] {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -2;
  object-fit: cover;
}
.waterfall-note-top .xxa .xxa-mb[data-v-8e26fd7f] {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  -webkit-backdrop-filter: saturate(150%) blur(25px);
  backdrop-filter: saturate(150%) blur(25px);
  background: rgba(0, 0, 0, 0.5);
}
.waterfall-note-top .xxa .xxa-top[data-v-8e26fd7f] {
  width: 3.75rem;
  height: 3.75rem;
  border-radius: 0.5rem;
  position: relative;
  overflow: hidden;
}
.waterfall-note-top .xxa .xxa-top-img[data-v-8e26fd7f] {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  object-fit: cover;
}
.waterfall-note-top .xxa .xxa-icon[data-v-8e26fd7f] {
  position: absolute;
  top: 1.09375rem;
  right: 1.09375rem;
  bottom: 1.09375rem;
  left: 1.09375rem;
  width: 1.5625rem;
  height: 1.5625rem;
}
.waterfall-note-top .xxa .xxa-t[data-v-8e26fd7f] {
  margin-top: 1rem;
  font-size: 0.8125rem;
  font-weight: 700;
}
.waterfall-note-top .xxa .xxa-tt[data-v-8e26fd7f] {
  margin: 0.25rem 0 1rem;
  opacity: 0.8;
  font-size: 0.625rem;
}
.waterfall-note-top .xxa .xxa-play[data-v-8e26fd7f] {
  width: 100%;
  height: 1.875rem;
  font-size: 0.5625rem;
  font-weight: 700;
  border-radius: 1.875rem;
  justify-content: center;
  background: rgba(255, 255, 255, 0.15);
}
.waterfall-note-top .xxa .xxa-play uni-image[data-v-8e26fd7f] {
  margin-right: 0.25rem;
  width: 0.5rem;
  height: 0.5rem;
}
.waterfall-note-top .xxzd[data-v-8e26fd7f] {
  position: absolute;
  top: 0.5rem;
  left: 0.5rem;
  width: 1.625rem;
  height: 1rem;
  color: #fff;
  font-size: 0.5rem;
  justify-content: center;
  background: rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.16);
  border-radius: 0.25rem;
}
.waterfall-note .waterfall-note-content[data-v-8e26fd7f] {
  width: calc(100% - 1rem);
  margin: 0.375rem 0.5rem;
  font-size: 0.8125rem;
  line-height: 1.125rem;
  word-break: break-word;
  white-space: pre-line;
  color: #333;
}
.waterfall-note .waterfall-note-bottom[data-v-8e26fd7f] {
  width: calc(100% - 1rem);
  margin: 0 0.5rem;
  height: 1.875rem;
  justify-content: space-between;
}
.waterfall-note-bottom .waterfall-note-user[data-v-8e26fd7f] {
  display: flex;
  align-items: center;
}
.waterfall-note-bottom .waterfall-note-user uni-image[data-v-8e26fd7f] {
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
}
.waterfall-note-bottom .waterfall-note-like uni-image[data-v-8e26fd7f] {
  width: 0.875rem;
  height: 0.875rem;
}
.waterfall-note .waterfall-note-top[data-v-8e26fd7f],
.waterfall-note-bottom .waterfall-note-user uni-image[data-v-8e26fd7f],
.waterfall-activity .waterfall-activity-item[data-v-8e26fd7f],
.waterfall-activity .big[data-v-8e26fd7f],
.wlc10[data-v-8e26fd7f] {
  background: #f8f8f8;
}
.waterfall-note-bottom .waterfall-note-user uni-view[data-v-8e26fd7f],
.waterfall-note-bottom .waterfall-note-like uni-text[data-v-8e26fd7f] {
  margin-left: 0.25rem;
  line-height: 1rem;
}
.waterfall-note-bottom .waterfall-note-user uni-view[data-v-8e26fd7f] {
  color: #333;
  max-width: 4.375rem;
  font-size: 0.625rem;
}
.waterfall-note-bottom .waterfall-note-like uni-text[data-v-8e26fd7f] {
  color: #999;
  font-size: 0.625rem;
}
.wlc1[data-v-8e26fd7f] {
  -webkit-line-clamp: 1 !important;
}
.ohto[data-v-8e26fd7f] {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.ohto2[data-v-8e26fd7f] {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}
.df[data-v-8e26fd7f] {
  display: flex;
  align-items: center;
}

/* 活动区域样式调整 */
.waterfall-activity[data-v-8e26fd7f] {
  border-radius: 0.25rem;
  overflow: hidden;
  margin-bottom: 0.25rem;
}
.waterfall-activity-item[data-v-8e26fd7f] {
  border-radius: 0.25rem 0.25rem 0 0;
  overflow: hidden;
}
.waterfall-activity-img[data-v-8e26fd7f] {
  position: relative;
  border-radius: 0.25rem 0.25rem 0 0;
  overflow: hidden;
}
.waterfall-activity .big[data-v-8e26fd7f] {
  height: 2.25rem;
  border-radius: 0 0 0.25rem 0.25rem;
  font-size: 0.75rem;
  font-weight: 500;
  color: #333;
  justify-content: center;
}

/* 图片类卡片的图片样式 */
.lazy-image[data-v-8e26fd7f] {
  border-radius: 0.25rem 0.25rem 0 0;
  overflow: hidden;
}

/* 活动区域更详细样式（从原始组件复制） */
.waterfall-activity .waterfall-activity-item[data-v-8e26fd7f] {
  height: 14.6875rem;
  overflow: hidden;
}
.waterfall-activity-item .waterfall-activity-img[data-v-8e26fd7f] {
  margin-bottom: 0.5rem;
  width: 100%;
  height: 9.0625rem;
  position: relative;
}
.waterfall-activity-img .zt[data-v-8e26fd7f] {
  position: absolute;
  top: 0.5rem;
  left: 0.5rem;
  width: 2.125rem;
  height: 1.1875rem;
  color: #fff;
  font-size: 0.5rem;
  font-weight: 700;
  background: rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.16);
  border-radius: 0.25rem;
  justify-content: center;
}
.waterfall-activity-img .xxbt[data-v-8e26fd7f] {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  padding: 0.75rem 0 0.25rem;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));
}
.waterfall-activity-img .waterfall-activity-name[data-v-8e26fd7f],
.waterfall-activity-item .waterfall-activity-tag[data-v-8e26fd7f] {
  width: calc(100% - 1rem);
  padding: 0 0.5rem 0.25rem;
}
.waterfall-activity-img .waterfall-activity-name[data-v-8e26fd7f] {
  color: #fff;
  font-size: 0.75rem;
  font-weight: 700;
}
.waterfall-activity-item .waterfall-activity-tag uni-image[data-v-8e26fd7f],
.waterfall-activity .waterfall-activity-btn uni-image[data-v-8e26fd7f] {
  width: 0.625rem;
  height: 0.625rem;
}
.waterfall-activity-item .waterfall-activity-tag uni-view[data-v-8e26fd7f] {
  width: calc(100% - 0.875rem);
  color: #999;
  font-size: 0.625rem;
  font-weight: 500;
}
.waterfall-activity-group[data-v-8e26fd7f] {
  margin-left: 0.96875rem;
  direction: ltr;
  unicode-bidi: bidi-override;
  display: inline-block;
}
.waterfall-activity-group .group-img[data-v-8e26fd7f] {
  width: 1rem;
  height: 1rem;
  display: inline-flex;
  position: relative;
  margin-left: -0.5rem;
  border: 0.0625rem solid #f8f8f8;
  background: #fff;
  vertical-align: middle;
  border-radius: 50%;
}
.waterfall-activity-group .group-img uni-image[data-v-8e26fd7f] {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}
.waterfall-activity-group .group-tit[data-v-8e26fd7f] {
  display: inline-flex;
  margin-left: 0.25rem;
  color: #999;
  font-size: 0.625rem;
  font-weight: 500;
}
.waterfall-activity .waterfall-activity-btn[data-v-8e26fd7f] {
  margin-top: 0.25rem;
  font-weight: 700;
  justify-content: center;
}
.waterfall-activity .big[data-v-8e26fd7f] {
  width: 100%;
  font-size: 0.6875rem;
  height: 2.1875rem;
}
.waterfall-activity .small[data-v-8e26fd7f] {
  position: absolute;
  left: 0.5rem;
  bottom: 0.5rem;
  font-size: 0.625rem;
  width: calc(100% - 1rem);
  height: 1.875rem;
  background: #fff;
}


.gg-box[data-v-feabea4d] {
  border-bottom: 1px solid #f8f8f8;
  width: calc(100% - 1.875rem);
  padding: 0.9375rem;
  display: flex;
}
.gg-box .gg-avatar[data-v-feabea4d] {
  width: 2.125rem;
  height: 2.125rem;
  border-radius: 50%;
  border: 1px solid #f8f8f8;
  position: relative;
}
.gg-avatar .top[data-v-feabea4d] {
  position: absolute;
  right: -0.125rem;
  bottom: -0.125rem;
  width: 0.8125rem;
  height: 0.8125rem;
  border-radius: 50%;
  justify-content: center;
  background: #000;
}
.gg-avatar .top uni-image[data-v-feabea4d] {
  width: 100%;
  height: 100%;
}
.gg-box .gg-item[data-v-feabea4d] {
  width: calc(100% - 2.75rem - 2px);
  margin-left: 0.625rem;
}

/* 用户名容器 */
.gg-item .gg-item-user .name-container[data-v-feabea4d] {
  align-items: center;
}
.gg-item .gg-item-user .name[data-v-feabea4d] {
  color: #000;
  font-size: 0.875rem;
  line-height: 1.0625rem;
  font-weight: 700;
}

/* VIP图标样式 - 参考center.vue */
.gg-item .gg-item-user .status-icon[data-v-feabea4d] {
  width: 2.1875rem;
  height: 0.9375rem;
  margin-right: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
.gg-item .gg-item-user .vip-icon[data-v-feabea4d] {
  border-radius: 0.1875rem;
  padding: 0.0625rem;
}
.gg-item .gg-item-user .vip-icon uni-image[data-v-feabea4d] {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
.gg-item .gg-item-user .tag[data-v-feabea4d] {
  margin-left: 0.375rem;
  padding: 0 0.1875rem;
  height: 1.0625rem;
  border-radius: 0.125rem;
  background: #f5f5f5;
}
.gg-item-user .tag uni-image[data-v-feabea4d] {
  margin: 0 0.09375rem;
  width: 0.625rem;
  height: 0.625rem;
}
.gg-item-user .tag uni-text[data-v-feabea4d] {
  margin: 0 0.09375rem;
  font-size: 0.5625rem;
}
.gg-item .gg-item-content[data-v-feabea4d] {
  margin-top: 0.375rem;
  font-size: 0.8125rem;
  font-weight: 400;
  line-height: 1.125rem;
  word-break: break-word;
  white-space: pre-line;
}
.gg-item .gg-item-file[data-v-feabea4d] {
  margin-top: 0.625rem;
  display: flex;
  position: relative;
  z-index: 1;
  flex-wrap: wrap;
}
.gg-item-file .file-h[data-v-feabea4d],
.gg-item-file .file-w[data-v-feabea4d],
.gg-item-file .file-img[data-v-feabea4d] {
  border-radius: 0.25rem;
  overflow: hidden;
}
.gg-item-file .file-h[data-v-feabea4d] {
  width: 10rem;
  height: 13.125rem;
}
.gg-item-file .file-w[data-v-feabea4d] {
  width: 13.125rem;
  height: 10rem;
}
.gg-item-file .file-img[data-v-feabea4d] {
  width: 6.125rem;
  height: 6.125rem;
  margin-right: 0.125rem;
  margin-bottom: 0.125rem;
}
.gg-item-file .file-img[data-v-feabea4d]:nth-child(3n) {
  margin-right: 0 !important;
}
.gg-item-file .file-count[data-v-feabea4d] {
  position: absolute;
  right: 0.625rem;
  bottom: 0.9375rem;
  padding: 0 0.3125rem;
  height: 1.25rem;
  color: #fff;
  font-size: 0.625rem;
  font-weight: 700;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 0.25rem;
}
.gg-item-file .file-count uni-image[data-v-feabea4d],
.gg-item-file .file-video uni-image[data-v-feabea4d] {
  width: 0.625rem;
  height: 0.625rem;
}
.gg-item-file .file-count uni-image[data-v-feabea4d] {
  margin-right: 0.3125rem;
}
.gg-item-file .file-video[data-v-feabea4d] {
  position: absolute;
  top: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  background: rgba(0, 0, 0, 0.6);
  justify-content: center;
  border-radius: 50%;
}
.gg-item-file .file-audio[data-v-feabea4d] {
  width: 100%;
  height: 4.375rem;
  border-radius: 0.25rem;
  color: #fff;
  position: relative;
  overflow: hidden;
}
.file-audio .audio-left[data-v-feabea4d] {
  margin-right: 0.9375rem;
  width: 4.375rem;
  height: 4.375rem;
  position: relative;
}
.file-audio .audio-left .icon[data-v-feabea4d] {
  position: absolute;
  top: 1.40625rem;
  right: 1.40625rem;
  bottom: 1.40625rem;
  left: 1.40625rem;
  width: 1.5625rem;
  height: 1.5625rem;
}
.file-audio .audio-bg[data-v-feabea4d],
.file-audio .audio-mb[data-v-feabea4d] {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.file-audio .audio-mb[data-v-feabea4d] {
  z-index: -1;
  -webkit-backdrop-filter: saturate(150%) blur(25px);
  backdrop-filter: saturate(150%) blur(25px);
  background: rgba(0, 0, 0, 0.5);
}
.file-audio .audio-t1[data-v-feabea4d] {
  font-size: 0.8125rem;
  font-weight: 700;
}
.file-audio .audio-t2[data-v-feabea4d] {
  margin-top: 0.3125rem;
  opacity: 0.8;
  font-size: 0.625rem;
}
.file-audio .audio-play[data-v-feabea4d] {
  margin: 0 0.9375rem;
  width: 3.125rem;
  height: 1.875rem;
  line-height: 1.875rem;
  text-align: center;
  font-size: 0.5625rem;
  font-weight: 700;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 1.875rem;
}
.gg-item .gg-item-g[data-v-feabea4d] {
  margin-top: 0.3125rem;
  width: 100%;
  display: flex;
  flex-wrap: wrap;
}
.gg-item-g .g-item[data-v-feabea4d] {
  margin: 0.3125rem 0.3125rem 0 0;
  padding: 0.25rem;
  font-size: 0.625rem;
  font-weight: 500;
  border-radius: 0.25rem;
  background: #f8f8f8;
}
.g-item .g-item-img[data-v-feabea4d] {
  width: 1.25rem;
  height: 1.25rem;
  background: #f8f8f8;
  border-radius: 0.125rem;
  overflow: hidden;
}
.gg-item .gg-item-time[data-v-feabea4d] {
  margin-top: 0.25rem;
  margin-bottom: 0.375rem;
  color: #999;
  font-size: 0.625rem;
}
.gg-item .gg-item-comment[data-v-feabea4d] {
  margin-top: 0.625rem;
  width: calc(100% - 1.25rem);
  padding: 0.625rem;
  color: #999;
  font-size: 0.75rem;
  font-weight: 400;
  word-break: break-word;
  white-space: pre-line;
  border-radius: 0.25rem;
  background: #f8f8f8;
}
.gg-item .gg-item-unm[data-v-feabea4d] {
  display: flex;
  align-items: center;
  width: 100%;
}
.gg-item-unm .unm-item[data-v-feabea4d] {
  margin-top: 0.9375rem;
  display: flex;
  align-items: center;
}
.gg-item-unm .unm-item uni-image[data-v-feabea4d] {
  width: 1.375rem;
  height: 1.375rem;
}
.gg-item-unm .unm-item uni-text[data-v-feabea4d] {
  margin: 0 0.9375rem 0 0.1875rem;
  color: #999;
  font-size: 0.5625rem;
  font-weight: 700;
}
.wlc8[data-v-feabea4d] {
  -webkit-line-clamp: 8 !important;
  line-clamp: 8 !important;
}
.df[data-v-feabea4d] {
  display: flex;
  align-items: center;
}
.ohto[data-v-feabea4d] {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.ohto2[data-v-feabea4d] {
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  line-clamp: 4;
  -webkit-box-orient: vertical;
}

/* 关注按钮样式 */
.gg-item-user .follow-btn[data-v-feabea4d] {
  margin-left: auto;
  padding: 0 0.625rem;
  height: 1.5rem;
  line-height: 1.5rem;
  border-radius: 0.25rem;
  font-size: 0.625rem;
  font-weight: 700;
  color: #000;
  background: #f8f8f8;
  text-align: center;
}
.gg-item-user .follow-btn.active[data-v-feabea4d] {
  color: #999;
  background: #f5f5f5;
}
.gg-item-user .follow-btn.mutual[data-v-feabea4d] {
  color: #576b95;
  background: rgba(87, 107, 149, 0.1);
}

/* 移除原有 .topic-tag 样式，话题和圈子统一用 g-item/g-item-img/text 结构 */
.topic-tag[data-v-feabea4d],
.topic-tag uni-text[data-v-feabea4d] {
  /* 移除样式 */
  display: none !important;
}

/* 评论样式 */
.gg-item .gg-item-comments[data-v-feabea4d] {
  margin-top: 0.625rem;
  width: calc(100% - 1.25rem);
  padding: 0.625rem;
  font-size: 0.75rem;
  border-radius: 0.25rem;
  background: #f8f8f8;
}
.gg-item-comments .comment-item[data-v-feabea4d] {
  margin-bottom: 0.46875rem;
  padding-bottom: 0.46875rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}
.gg-item-comments .comment-item[data-v-feabea4d]:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}
.gg-item-comments .comment-user[data-v-feabea4d] {
  font-size: 0.75rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.1875rem;
}
.gg-item-comments .comment-content[data-v-feabea4d] {
  font-size: 0.75rem;
  color: #666;
  line-height: 1rem;
  word-break: break-all;
}
.gg-item-comments .comment-footer[data-v-feabea4d] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 0.3125rem;
}
.gg-item-comments .comment-time[data-v-feabea4d] {
  font-size: 0.625rem;
  color: #999;
}
.gg-item-comments .comment-like[data-v-feabea4d] {
  display: flex;
  align-items: center;
}
.gg-item-comments .comment-like uni-image[data-v-feabea4d] {
  width: 0.875rem;
  height: 0.875rem;
  margin-right: 0.125rem;
}
.gg-item-comments .comment-like uni-text[data-v-feabea4d] {
  font-size: 0.625rem;
  color: #999;
}
.gg-item-comments .comment-like uni-text.active[data-v-feabea4d] {
  color: #FA5150;
}
.gg-item-comments .more-comments[data-v-feabea4d] {
  margin-top: 0.46875rem;
  text-align: center;
  font-size: 0.6875rem;
  color: #576b95;
  padding: 0.3125rem 0;
}

/* ==== 投票展示样式（复用add.vue） ==== */
.vote-box[data-v-feabea4d] {
  width: 100%;
  margin-top: 0.5rem;
}
.vote-container[data-v-feabea4d] {
  width: 100%;
  background-color: #f5f5f5;
  border-radius: 0.5rem;
  position: relative;
}
.vote-header[data-v-feabea4d] {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  padding: 0.625rem;
}
.vote-title-container[data-v-feabea4d] {
  display: flex;
  align-items: center;
}
.vote-icon[data-v-feabea4d] {
  width: 1rem;
  height: 1rem;
  margin-right: 0.3125rem;
}
.vote-title[data-v-feabea4d] {
  font-size: 0.875rem;
  font-weight: 500;
  color: #333;
  text-align: left;
  white-space: normal;
  word-break: break-word;
}
.vote-options[data-v-feabea4d] {
  display: flex;
  flex-direction: column;
  padding: 0 0;
}
.vote-option-voted[data-v-feabea4d] {
  background: #f5f5f5;
  border-radius: 1rem;
  margin-bottom: 0.5rem;
  padding: 0;
  border: none;
  position: relative;
  transition: background 0.2s, border 0.2s;
}
.vote-option-voted.selected[data-v-feabea4d] {
  background: #fff;
}
.vote-bar-bg[data-v-feabea4d] {
    border-radius: 18px;
    font-size: 17px;
    color: #333;
    margin-bottom: 10px;
    border: none;
    box-shadow: none;
    text-align: center;
    transition: background 0.2s, border 0.2s;
    margin-left: 11px;
    margin-right: 11px;
    min-height: 27px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 11px;
    box-sizing: border-box;
}
.vote-option-voted.selected .vote-bar-bg[data-v-feabea4d] {
  background: #fff;
}
.vote-bar[data-v-feabea4d] {
  height: 100%;
  border-radius: 1rem;
  transition: width 0.3s;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 1;
}
.vote-row[data-v-feabea4d] {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
  pointer-events: none;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 0.625rem;
}
.vote-left[data-v-feabea4d] {
  display: flex;
  align-items: center;
}
.vote-option-unvoted[data-v-feabea4d] {
  background: #fff;
  border-radius: 1rem;
  font-size: 0.8125rem;
  color: #333;
  margin-bottom: 0.5625rem;
  border: none;
  box-shadow: none;
  text-align: center;
  transition: background 0.2s, border 0.2s;
  margin-left: 0.625rem;
  margin-right: 0.625rem;
  position: relative;
  min-height: 2.0625rem;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 0.625rem;
  box-sizing: border-box;
}
.vote-progress-bar[data-v-feabea4d] {
  position: absolute;
  left: 0;
  bottom: 0;
  height: 0.375rem;
  border-radius: 1rem;
  z-index: 1;
  background: #ffd600;
  transition: width 0.3s;
}
.vote-option-unvoted:not(.selected) .vote-progress-bar[data-v-feabea4d] {
  background: #eaeaea;
}
.vote-checked-icon[data-v-feabea4d] {
  width: 0.875rem;
  height: 0.875rem;
  background: #ffd600;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.3125rem;
}
.vote-checked-icon uni-image[data-v-feabea4d] {
  width: 0.625rem;
  height: 0.625rem;
  display: block;
}
.vote-content[data-v-feabea4d] {
  font-size: 0.8125rem;
  color: #333;
  text-align: left;
  white-space: normal;
  word-break: break-word;
  font-weight: 500;
}
.vote-option-voted.selected .vote-content[data-v-feabea4d] {
  color: #000;
}
.vote-percent[data-v-feabea4d] {
  font-size: 0.8125rem;
  color: #000000;
  text-align: right;
  margin-left: 0.375rem;
  min-width: 1.5rem;
  flex-shrink: 0;
  white-space: nowrap;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  font-weight: 500;
  margin-right: 1.25rem;
  position: relative;
}
.vote-people[data-v-feabea4d] {
  margin-top: 0.5rem;
  color: #999;
  font-size: 0.75rem;
  text-align: left;
  padding-left: 0.625rem;
  padding-bottom: 0.625rem;
}



.nav-box{
  position: fixed;
  z-index: 99;
  top: 0;
  left: 0;
  width: 100%;
  box-sizing: border-box;
}
.nav-box .nav-back{
  padding: 0 0.9375rem;
  width: 1.0625rem;
}
.nav-box .nav-title{
  max-width: 60%;
  font-size: 1rem;
  font-weight: 700;
}
.user-box{
  width: calc(100% - 1.875rem);
  padding: 1.875rem 0.9375rem;
  color: #fff;
  position: relative;
  overflow: hidden;
}
.user-box .user-img, .user-box .user-bg{
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.user-box .user-bg{
  z-index: -1;
  background: rgba(0, 0, 0, .5);
}
.user-box .user-top{
  width: 100%;
  justify-content: space-between;
}
.user-top .avatar{
  width: 5.625rem;
  height: 5.625rem;
  border-radius: 50%;
  background: #fff;
  border: 2px solid #f5f5f5;
  overflow: hidden;
}
.user-top .btn{
  padding: 0 0.9375rem;
  height: 2rem;
  line-height: 2rem;
  border-radius: 0.25rem;
  font-size: 0.625rem;
  font-weight: 700;
  color: #000;
  background: #fff;
}
.user-top .active{
  color: rgba(255, 255, 255, .52);
  background: rgba(255, 255, 255, .1);
}
.user-top .mutual{
  color: #576b95;
  background: rgba(255, 255, 255, .2);
}
.user-box .user-name{
  margin: 0.625rem 0 0.3125rem;
  width: 100%;
  font-size: 1.0625rem;
  font-weight: 700;
}
.user-box .user-intro{
  width: 100%;
  word-break: break-word;
  white-space: pre-line;
}
.user-box .user-intro uni-text{
  color: #ccc;
  font-size: 0.75rem;
  font-weight: 400;
}
.user-box .user-tag{
  margin: 0.625rem 0;
  width: 100%;
}
.user-tag .tag-item{
  margin-right: 0.5rem;
  height: 1.375rem;
  padding: 0 0.4375rem;
  border-radius: 0.25rem;
  background: rgba(255, 255, 255, .1);
  font-weight: 500;
  font-size: 0.625rem;
  justify-content: center;
}
.user-tag .tag-item uni-image{
  width: 0.75rem;
  height: 0.75rem;
}
.user-num .num-item{
  margin-right: 0.9375rem;
  font-size: 0.625rem;
  font-weight: 300;
  color: #ccc;
}
.user-num .num-item .t1{
  color: #fff;
  font-size: 0.875rem;
  font-weight: 700;
  margin-right: 0.1875rem;
}
.content-box{
  margin-top: -0.9375rem;
  background: #fff;
  padding: 0.9375rem 0;
  border-radius: 0.9375rem 0.9375rem 0 0;
}
.block-box .block-title{
  padding: 0 0.9375rem;
  font-size: 0.8125rem;
  font-weight: 700;
}
.block-box .circle-box{
  width: calc(100% - 0.625rem);
  padding: 0.9375rem 0.3125rem;
  display: flex;
}
.circle-box .circle-item{
  flex-shrink: 0;
  margin: 0 0.3125rem;
  flex-direction: column;
  justify-content: center;
}
.circle-item .circle-avatar{
  width: 3.125rem;
  height: 3.125rem;
  border-radius: 50%;
  background: #f8f8f8;
}
.circle-item .circle-name{
  margin-top: 0.46875rem;
  width: 3.75rem;
  color: #999;
  font-size: 0.625rem;
  text-align: center;
}
.bar-box{
  position: -webkit-sticky;
  position: sticky;
  left: 0;
  z-index: 99;
  margin-top: -1px;
  width: 100%;
  height: 2.5rem;
  background: #fff;
}
.bar-box .bar-item{
  padding: 0 0.9375rem;
  height: 100%;
  flex-direction: column;
  justify-content: center;
  position: relative;
}
.bar-box .bar-item uni-text{
  font-weight: 700;
  transition: all .3s ease-in-out;
}
.bar-item .bar-line{
  position: absolute;
  bottom: 0.375rem;
  width: 0.5625rem;
  height: 0.1875rem;
  border-radius: 0.1875rem;
  background: #000;
  transition: opacity .3s ease-in-out;
}
.content-box .dynamic-box{
  width: calc(100% - 0.5rem);
  padding: 0.6875rem 0.25rem 0;
}
.loading-box {
  width: 100%;
  padding: 1.875rem 0;
  display: flex;
  align-items: center;
  justify-content: center;
}
.load-more-box {
  width: 100%;
  padding: 0.9375rem 0;
  display: flex;
  align-items: center;
  justify-content: center;
}
.empty-box{
  width: 100%;
  padding: 3.125rem 0;
  flex-direction: column;
}
.empty-box uni-image{
  width: 9.375rem;
  height: 9.375rem;
  margin-bottom: 0.9375rem;
}
.empty-box .e1{
  font-size: 0.9375rem;
  font-weight: 700;
}
.empty-box .e2{
  margin-top: 0.3125rem;
  color: #999;
  font-size: 0.8125rem;
}
.error-box{
  width: 100%;
  padding: 3.125rem 0;
  flex-direction: column;
}
.error-box uni-image{
  width: 9.375rem;
  height: 9.375rem;
  margin-bottom: 0.9375rem;
}
.error-box .e1{
  font-size: 0.9375rem;
  font-weight: 700;
  color: #333;
}
.error-box .e2{
  margin-top: 0.3125rem;
  color: #999;
  font-size: 0.8125rem;
  text-align: center;
  padding: 0 1.875rem;
}
.error-box .retry-btn{
  margin-top: 1.25rem;
  width: 6.25rem;
  height: 2.5rem;
  line-height: 2.5rem;
  text-align: center;
  font-size: 0.875rem;
  font-weight: 700;
  color: #fff;
  background: #007aff;
  border-radius: 1.25rem;
}
.tips-box{
  justify-content: center;
  width: 100%;
}
.tips-box .tips-item{
  background: #000;
  color: #fff;
  padding: 0.625rem 1.25rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 700;
}
.df{
  display: flex;
  align-items: center;
}
.ohto{
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.xwb{
  filter: invert(1);
}
.background-carousel {
  position: relative;
  width: 100%;
  height: 100%;
}
.carousel-item {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 0.5s ease-in-out;
}
.carousel-item.active {
  opacity: 1;
}
.carousel-indicators {
  position: absolute;
  bottom: 0.625rem;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
}
.indicator {
  width: 0.375rem;
  height: 0.125rem;
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 0.0625rem;
  margin: 0 0.125rem;
  cursor: pointer;
}
.indicator.active {
  background-color: #fff;
}
.default-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.like-popup{
  width: 12.5rem;
  background: #fff;
  padding: 0.9375rem;
  border-radius: 0.9375rem;
  overflow: hidden;
}
.like-popup .like-img{
  margin: 0 1.25rem;
  width: 10rem;
  height: 6.25rem;
}
.like-popup .like-content{
  margin: 0.625rem 0 1.25rem;
  width: 100%;
  color: #333;
  font-size: 0.8125rem;
  text-align: center;
}
.like-popup .like-btn{
  width: 100%;
  height: 2.5rem;
  line-height: 2.5rem;
  text-align: center;
  font-size: 0.75rem;
  font-weight: 700;
  color: #fff;
  background: #000;
  border-radius: 0.5rem;
}
