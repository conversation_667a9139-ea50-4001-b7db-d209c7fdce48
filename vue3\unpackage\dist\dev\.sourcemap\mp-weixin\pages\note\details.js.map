{"version": 3, "file": "details.js", "sources": ["pages/note/details.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvbm90ZS9kZXRhaWxzLnZ1ZQ"], "sourcesContent": ["<!-- \n  笔记详情页面 - 已优化版本\n  \n  主要优化内容：\n  1. 音频相关代码只在音频动态时执行，避免不必要的资源消耗\n  2. 使用计算属性优化条件判断，提高渲染性能\n  3. 添加页面活跃状态管理，避免后台页面执行不必要操作\n  4. 优化表情解析缓存机制，提高表情显示性能\n  5. 改进资源清理机制，防止内存泄漏\n  6. 优化评论加载和缓存策略\n  7. 添加性能监控和调试信息\n  8. 改进错误处理和用户体验\n-->\n<template>\n  <view class=\"container\">\n    <!-- 导航栏 -->\n    <view class=\"nav-box\" :style=\"{'padding-top': statusBarHeight + 'px'}\">\n      <view class=\"nav-item df\" :style=\"{'width': '100%', 'height': titleBarHeight + 'px'}\">\n        <view class=\"nav-back df\" @tap=\"navBack\">\n          <image src=\"/static/img/z.png\" style=\"width:34rpx;height:34rpx\"></image>\n        </view>\n        <view class=\"nav-user df\">\n          <view class=\"user-info df\" :data-url=\"'user/details?id='+noteInfo.uid\" @tap=\"navigateToFun\">\n            <image class=\"nav-user-avatar\" :src=\"noteInfo.user_info.avatar\" mode=\"aspectFill\"></image>\n            <view>\n              <view class=\"nav-user-name ohto\">{{noteInfo.user_info.nickname}}</view>\n              <view v-if=\"noteInfo.location_name\" class=\"nav-user-adds df\" @tap.stop=\"openLocationClick\">\n                <image src=\"/static/img/wz.png\"></image>\n                <text>{{noteInfo.location_name}}</text>\n              </view>\n            </view>\n          </view>\n          <!-- 关注按钮 -->\n          <view v-if=\"noteInfo.uid && noteInfo.uid != userId\" \n                :class=\"['follow-btn', isFollowing ? 'active' : '']\" \n                @tap.stop=\"followUser\">\n            {{isFollowing ? '已关注' : '＋关注'}}\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 内容区域 -->\n    <view class=\"content-box\" :style=\"{'margin-top': statusBarHeight + titleBarHeight + 'px', 'width': '100%'}\">\n\n      <!-- 图片类型 - 优化条件判断，使用计算属性 -->\n      <block v-if=\"isImageNote && ((noteInfo.images && noteInfo.images.length > 0) || (noteInfo.imgs && noteInfo.imgs.length > 0))\">\n        <!-- 轮播图 -->\n        <swiper \n          class=\"swiper-box\" \n          :style=\"{'height': isHigh()}\" \n          circular \n          @change=\"swiperChange\">\n          <swiper-item \n            v-for=\"(item, index) in noteInfo.type == 1 ? noteInfo.imgs : noteInfo.images\" \n            :key=\"index\" \n            class=\"swiper-item\" \n            :data-i=\"index\" \n            @tap=\"swiperClick\">\n            <image class=\"swiper-image\" mode=\"aspectFill\" :src=\"getImageSrc(item)\"></image>\n          </swiper-item>\n        </swiper>\n        \n        <!-- 图片计数器 - 兼容type=1和type=2 -->\n        <view v-if=\"(noteInfo.images && noteInfo.images.length > 1) || (noteInfo.imgs && noteInfo.imgs.length > 1)\"\n              class=\"current df\" :style=\"{'top': 'calc('+(statusBarHeight+titleBarHeight)+'px + 20rpx)'}\">\n          {{swiperIdx+1}}/{{noteInfo.type == 1 ? noteInfo.imgs.length : noteInfo.images.length}}\n        </view>\n        \n        <!-- 自定义指示器 - 兼容type=1和type=2 -->\n        <view v-if=\"(noteInfo.images && noteInfo.images.length > 1) || (noteInfo.imgs && noteInfo.imgs.length > 1)\" \n              class=\"indicator df\">\n            <view \n            v-for=\"(item, index) in noteInfo.type == 1 ? noteInfo.imgs : noteInfo.images\" \n              :key=\"index\" \n            :class=\"['indicator-item', index == swiperIdx ? 'active' : '']\"\n            :style=\"{\n              'width': 'calc(100% / ' + (noteInfo.type == 1 ? noteInfo.imgs.length : noteInfo.images.length) + ' - 5px)'\n            }\">\n            </view>\n        </view>\n      </block>\n      \n      <!-- 视频类型 - 类型3，优化条件判断 -->\n      <video v-if=\"isVideoNote && noteInfo.video\" class=\"video-box\" :src=\"noteInfo.video\" :poster=\"noteInfo.video_cover\" show-mute-btn=\"true\" autoplay custom-cache=\"false\"></video>\n      \n      <!-- 音频类型 - 类型4，只有音频动态时才渲染 -->\n      <view v-if=\"isAudioNote\" class=\"audio-box df\" :style=\"{'margin-top': '30rpx', 'margin-bottom': '30rpx'}\">\n        <image class=\"audio-bg\" :src=\"noteInfo.audio_cover || '/static/img/audio_default_cover.png'\" mode=\"aspectFill\"></image>\n        <view class=\"audio-mb\"></view>\n        <view class=\"audio-left\">\n          <image class=\"audio-left\" :src=\"noteInfo.audio_cover || '/static/img/audio_default_cover.png'\" mode=\"aspectFill\"></image>\n          <image class=\"icon\" src=\"/static/img/yw.png\"></image>\n        </view>\n        <view style=\"width:calc(100% - 338rpx)\">\n          <view class=\"audio-t1\">{{noteInfo.audio_title || '未知音频'}}</view>\n          <view class=\"audio-t2\">{{noteInfo.content || '无描述信息'}}</view>\n        </view>\n        <view v-if=\"noteInfo.audio\" class=\"audio-play\" @tap=\"audioBgClick\">\n          <view v-if=\"bgAudioStatus\" class=\"icon\">\n            <play :color=\"'#fff'\"></play>\n          </view>\n          <image v-else class=\"icon\" src=\"/static/img/a.png\"></image>\n        </view>\n        <view v-else class=\"audio-error\">\n          <text>音频不可用</text>\n        </view>\n      </view>\n      \n      <!-- 内容文本区域（所有类型都显示） -->\n      <view class=\"info-box\">\n        <!-- 文本内容 -->\n        <view class=\"info-content\">\n          <text user-select=\"true\">{{noteInfo.content}}</text>\n        </view>\n        \n        <!-- 标签区域 -->\n        <view v-if=\"noteInfo.topic_info && noteInfo.topic_info.length > 0 || hasCircle() || noteInfo.goods_info || noteInfo.activity_name\" class=\"info-tag\">\n          <!-- 位置信息 -->\n          <view v-if=\"noteInfo.location_name\" class=\"tag-item df\" @tap.stop=\"openLocationClick\">\n            <image class=\"icon\" style=\"width:32rpx;height:32rpx\" src=\"/static/img/wz.png\"></image>\n            <text style=\"padding:0 8rpx\">{{noteInfo.location_name}}</text>\n          </view>\n          \n          <!-- 话题标签 -->\n          <view \n            v-for=\"(topic, topicIndex) in noteInfo.topic_info\" \n            :key=\"'topic-'+topicIndex\"\n            class=\"tag-item df\" \n            style=\"border-radius:40rpx\" \n            :data-url=\"'topic/details?id='+topic.id\"\n            @tap=\"navigateToFun\">\n            <image class=\"icon\" style=\"border-radius:50%\" :src=\"topic.icon || '/static/img/topic_icon.png'\" mode=\"aspectFill\"></image>\n            <text>{{topic.title}}</text>\n          </view>\n          \n          <!-- 圈子标签 -->\n          <view \n            v-if=\"hasCircle()\" \n            class=\"tag-item df\" \n            style=\"border-radius:40rpx\" \n            :data-url=\"'note/circle?id='+getCircleId()\" \n            @tap=\"navigateToFun\">\n            <image class=\"icon\" style=\"border-radius:50%\" :src=\"getCircleAvatar()\" mode=\"aspectFill\"></image>\n            <text>{{getCircleName()}}</text>\n          </view>\n          \n          <!-- 活动标签 -->\n          <view \n            v-if=\"noteInfo.activity_name\" \n            class=\"tag-item df\" \n            :data-url=\"'activity/details?id='+noteInfo.activity_id\" \n            @tap=\"navigateToFun\">\n            <image class=\"icon\" style=\"border-radius:4rpx;width:50rpx\" mode=\"aspectFill\" :src=\"noteInfo.activity_img\"></image>\n            <text>{{noteInfo.activity_name}}</text>\n          </view>\n          \n          <!-- 商品标签 -->\n            <view \n            v-if=\"noteInfo.goods_info\" \n              class=\"tag-item df\" \n            :data-url=\"'goods/details?id='+noteInfo.goods_info.id\" \n              @tap=\"navigateToFun\">\n            <image class=\"icon\" style=\"border-radius:4rpx\" :src=\"noteInfo.goods_info.image\" mode=\"aspectFill\"></image>\n            <text>{{noteInfo.goods_info.name || noteInfo.goods_info.store_name}}</text>\n            </view>\n        </view>\n     <!-- 投票展示区（放在标签区域上方，内容文本下方） -->\n      <view v-if=\"noteInfo.vote_info\" class=\"vote-box\">\n        <view class=\"vote-container\">\n          <view class=\"vote-header\">\n            <view class=\"vote-title-container\">\n              <image class=\"vote-icon\" src=\"/static/img/toupiao.png\" mode=\"aspectFit\"></image>\n              <text class=\"vote-title\">{{ noteInfo.vote_info.vote.title }}</text>\n            </view>\n          </view>\n          <view class=\"vote-options\">\n            <!-- 未投票时 -->\n            <template v-if=\"!noteInfo.vote_info.user_selected\">\n              <view \n                v-for=\"(option, idx) in noteInfo.vote_info.options\" \n                :key=\"option.id || idx\" \n                class=\"vote-option vote-option-unvoted\"\n                @tap=\"onVote(option.id)\">\n                <text class=\"vote-content\">{{ option.option_text }}</text>\n              </view>\n            </template>\n            <!-- 已投票时 -->\n            <template v-else>\n              <view \n                v-for=\"(option, idx) in noteInfo.vote_info.options\" \n                :key=\"option.id || idx\" \n                class=\"vote-option vote-option-unvoted\">\n                <view class=\"vote-row\">\n                  <view class=\"vote-left\">\n                    <view v-if=\"noteInfo.vote_info.user_selected === option.id\" class=\"vote-checked-icon\">\n                      <image src=\"/static/img/c1.png\" style=\"width:30rpx;height:30rpx;\"></image>\n                    </view>\n                    <text class=\"vote-content\">{{ option.option_text }}</text>\n                  </view>\n                  <text class=\"vote-percent\">{{ option.percent }}%</text>\n                </view>\n                <view class=\"vote-bar-bg\">\n                  <view class=\"vote-bar\" :style=\"{\n                    width: option.percent + '%',\n                    background: noteInfo.vote_info.user_selected === option.id ? '#ffd600' : '#eaeaea'\n                  }\"></view>\n                </view>\n              </view>\n            </template>\n          </view>\n          <view class=\"vote-people\">{{ votePeopleText }}</view>\n        </view>\n      </view>\n        <!-- 底部信息 -->\n        <view class=\"more df\">\n          <view class=\"more-left\">{{noteInfo.create_time}} · {{noteInfo.province}} · 浏览 {{noteInfo.views}}</view>\n          <image @tap=\"shareClick(true)\" class=\"more-right\" src=\"/static/img/gd.png\"></image>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 评论区域 -->\n    <view class=\"comment-box\" id=\"commentId\">\n      <view class=\"comment-top df\">\n        <view class=\"top-title\">{{noteInfo.comments ? '评论 '+noteInfo.comments : '暂无评论'}}</view>\n        <view class=\"top-btn df\">\n          <view class=\"btn-active\" :style=\"{'left': cType == 0 ? '6rpx' : '74rpx'}\"></view>\n          <view class=\"btn-item\" :style=\"{'color': cType == 0 ? '#000' : '#999'}\" @tap=\"commentClick(0)\">默认 </view>\n          <view class=\"btn-item\" :style=\"{'color': cType == 1 ? '#000' : '#999'}\" @tap=\"commentClick(1)\">最新 </view>\n        </view>\n      </view>\n      \n      <!-- 评论为空 -->\n      <view v-if=\"isEmpty\" class=\"empty-box df\">\n        <image src=\"/static/img/empty.png\"/>\n        <view class=\"e1\">期待你的评论</view>\n        <view class=\"e2\">发条评论表达你的想法吧</view>\n      </view>\n      \n      <!-- 评论列表 -->\n      <block v-else>\n        <view v-for=\"(item, index) in commentList\" :key=\"index\" class=\"comment-item df\">\n          <!-- 评论头像 -->\n          <view class=\"comment-avatar\" :data-url=\"'user/details?id='+item.uid\" @tap=\"navigateToFun\">\n            <lazy-image :src=\"item.avatar || '/static/img/avatar_default.png'\"></lazy-image>\n          </view>\n          \n          <!-- 评论内容 -->\n          <view class=\"comment-info\">\n            <view class=\"comment-info-top df\" :data-url=\"'user/details?id='+item.uid\" @tap=\"navigateToFun\">\n              <view class=\"user-info-left\">\n                <view v-if=\"noteInfo.uid == item.uid\" class=\"zz\">作者</view>\n                <view v-else-if=\"userId == item.uid\" class=\"wo\">我</view>\n                {{item.nickname || '用户'}}\n              </view>\n              <!-- 点赞图标按钮 -->\n              <view v-if=\"!item.delete_time\" class=\"like-icon\" @tap.stop=\"toggleCommentLike(item.id, item.is_like)\">\n                <image :src=\"item.is_like == 1 ? '/static/img/dz1.png' : '/static/img/dz.png'\" mode=\"aspectFill\"></image>\n                <text v-if=\"item.likes > 0\">{{item.likes}}</text>\n              </view>\n            </view>\n            <!-- 评论内容点击回复 -->\n            <view :class=\"['comment-info-content', (item.status != 5 || item.delete_time) && 'db']\" @tap.stop=\"handleCommentClick($event, 1, item.uid, item.id, item.nickname || '用户', index, -1)\">\n              <rich-text \n                v-if=\"!item.delete_time\" \n                :nodes=\"parseEmojiContent(item.content)\" \n                user-select=\"true\"\n                @itemclick=\"onEmojiClick\"\n                class=\"comment-rich-text\"\n                :show-with-animation=\"false\"\n                :selectable=\"true\"\n              ></rich-text>\n              <text v-else class=\"deleted-comment\">(该评论已被删除)</text>\n              <!-- 显示评论图片 -->\n              <image \n                v-if=\"item.image && !item.delete_time\" \n                class=\"comment-image\" \n                mode=\"widthFix\" \n                :src=\"item.image\" \n                @tap.stop=\"previewCommentImage(item.image)\"\n                lazy-load\n              ></image>\n            </view>\n                          <view class=\"comment-info-bottom df\">\n                <text>{{item.create_time}} {{item.province || ''}}</text>\n                <!-- 回复按钮 -->\n                <view v-if=\"!item.delete_time\" @tap.stop=\"handleCommentClick($event, 1, item.uid, item.id, item.nickname || '用户', index, -1)\">回复</view>\n                <view v-if=\"userId == item.uid && item.status == 5 && !item.delete_time\" @tap.stop=\"delComment\" :data-id=\"item.id\" :data-idx=\"index\" data-i=\"-1\">删除</view>\n              </view>\n            \n            <!-- 回复列表 -->\n            <template v-if=\"item.replies && item.replies.length > 0\">\n              <view v-for=\"(v, i) in sortRepliesByTime(item.replies)\" :key=\"i\" class=\"comment-item df\">\n                <view class=\"comment-avatar-z\" :data-url=\"'user/details?id='+v.uid\" @tap=\"navigateToFun\">\n                  <lazy-image :src=\"v.avatar || '/static/img/avatar_default.png'\"></lazy-image>\n                </view>\n                \n                <view class=\"comment-info\" style=\"width:calc(100% - 68rpx)\">\n                  <view class=\"comment-info-top-z df\">\n                    <view class=\"user-info-left\">\n                      <view v-if=\"noteInfo.uid == v.uid\" class=\"zz\">作者</view>\n                      <view v-else-if=\"userId == v.uid\" class=\"wo\">我</view>\n                      <view class=\"nn ohto\" :data-url=\"'user/details?id='+v.uid\" @tap=\"navigateToFun\">\n                        {{v.nickname || '用户'}}\n                      </view>\n                      <!-- 显示回复关系 -->\n                      <template v-if=\"v.reply_id && v.reply_id !== item.id\">\n                        <text> ▶ </text>\n                        <view class=\"nn ohto\" :data-url=\"'user/details?id='+v.reply_uid\" @tap=\"navigateToFun\">\n                          {{v.reply_nickname || '用户'}}\n                        </view>\n                      </template>\n                      <text>: </text>\n                    </view>\n                    <!-- 点赞图标按钮 -->\n                    <view v-if=\"!v.delete_time\" class=\"like-icon\" @tap.stop=\"toggleCommentLike(v.id, v.is_like)\">\n                      <image :src=\"v.is_like == 1 ? '/static/img/dz1.png' : '/static/img/dz.png'\" mode=\"aspectFill\"></image>\n                      <text v-if=\"v.likes > 0\">{{v.likes}}</text>\n                    </view>\n                  </view>\n                  \n                                      <!-- 回复内容点击回复 -->\n                    <view :class=\"['comment-info-content', (v.status != 5 || v.delete_time) && 'db', v.is_system_message && 'system-message']\" @tap.stop=\"handleCommentClick($event, 1, v.uid, item.id, v.nickname || '用户', index, getReplyIndex(item.replies, v.id))\">\n                      <rich-text \n                        v-if=\"!v.delete_time && !v.is_system_message\" \n                        :nodes=\"parseEmojiContent(v.content)\" \n                        user-select=\"true\"\n                        @itemclick=\"onEmojiClick\"\n                        class=\"comment-rich-text reply-rich-text\"\n                        :show-with-animation=\"false\"\n                        :selectable=\"true\"\n                      ></rich-text>\n                      <text v-else-if=\"v.delete_time\" class=\"deleted-comment\">(该评论已被删除)</text>\n                      <text v-else-if=\"v.is_system_message\" class=\"system-message\">{{v.content}}</text>\n                      <!-- 显示评论图片 -->\n                      <image \n                        v-if=\"v.image && !v.delete_time\" \n                        class=\"comment-image reply-comment-image\" \n                        mode=\"widthFix\" \n                        :src=\"v.image\" \n                        @tap.stop=\"previewCommentImage(v.image)\"\n                        lazy-load\n                      ></image>\n                    </view>\n                  \n                  <view class=\"comment-info-bottom df\">\n                    <text>{{v.create_time}} {{v.province || ''}}</text>\n                    <!-- 回复按钮 -->\n                    <view v-if=\"!v.delete_time\" @tap.stop=\"handleCommentClick($event, 1, v.uid, item.id, v.nickname || '用户', index, getReplyIndex(item.replies, v.id))\">回复</view>\n                    <view v-if=\"userId == v.uid && v.status == 5 && !v.delete_time\" \n                          @tap.stop=\"delComment\" :data-id=\"v.id\" :data-idx=\"index\" :data-i=\"getReplyIndex(item.replies, v.id)\">\n                      删除\n                    </view>\n                  </view>\n                </view>\n              </view>\n            </template>\n              \n            <!-- 展开/加载回复按钮 -->\n            <view v-if=\"item.reply_count > (item.replies ? item.replies.length : 0)\" class=\"unfold\" \n                  :data-id=\"item.id\" :data-idx=\"index\" @tap=\"loadAllReplies\">\n              <view v-if=\"item.loading_replies\" class=\"loading-text\">\n                <image class=\"loading-icon\" src=\"/static/img/loading.gif\"></image>\n                加载中...\n              </view>\n              <text v-else>\n                <template v-if=\"!item.replies || item.replies.length === 0\">\n                  查看 {{item.reply_count}} 条回复\n                </template>\n                <template v-else-if=\"item.has_more_replies\">\n                  加载更多回复 ({{item.replies.length}}/{{item.reply_count}})\n                </template>\n                <template v-else>\n                  已加载全部回复\n                </template>\n              </text>\n            </view>\n          </view>\n        </view>\n      </block>\n      \n      <!-- 加载更多 -->\n      <uni-load-more v-if=\"loadStatus != 'no-more'\" :status=\"loadStatus\"></uni-load-more>\n      <view v-else class=\"no-more\">- THE END -</view>\n    </view>\n    \n    <!-- 底部操作栏 -->\n    <view class=\"footer-box bUp\">\n      <view class=\"footer-item df\">\n        <view v-if=\"!isUser\" class=\"footer-means df\" data-url=\"center/means\" @tap=\"navigateToFun\">\n          <text>完善账号资料后即可评论</text>\n        </view>\n        <view v-else class=\"footer-comment df\" @tap=\"handleCommentClick($event)\">\n          <image :src=\"userAvatar || '/static/img/avatar_default.png'\" mode=\"aspectFill\"></image>\n          <view class=\"ohto\">{{comtext ? comtext : comtips}}</view>\n        </view>\n        <view class=\"df\">\n          <!-- 底部评论按钮 -->\n          <view class=\"footer-icon\" @tap=\"handleCommentClick($event)\">\n            <image src=\"/static/img/pl.png\"></image>\n            <text>{{noteInfo.comments ? noteInfo.comments : ''}} 评论</text>\n          </view>\n          <view class=\"footer-icon\" @tap=\"likeDynamic\">\n            <image v-if=\"noteInfo.is_like == 1\" class=\"hi\" src=\"/static/img/dz1.png\"></image>\n            <image v-else class=\"hi\" src=\"/static/img/dz.png\"></image>\n            <text v-if=\"noteInfo.likes < 10000\">{{noteInfo.likes ? noteInfo.likes : ''}} 赞</text>\n            <text v-else>{{noteInfo.like_count_str}} 赞</text>\n          </view>\n          <view class=\"footer-icon\" @tap=\"shareClick(true)\">\n            <image src=\"/static/img/fx.png\"></image>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 评论弹窗 -->\n    <view v-if=\"isComment\" class=\"popup-comment-mask\" @tap=\"closeComment\"></view>\n    \n    <!-- 评论输入组件 -->\n    <comment-input \n      :show=\"isComment\" \n      :placeholder=\"comtips\"\n      :focus=\"isFocus\"\n      ref=\"commentInput\"\n      @blur=\"closeComment\"\n      @send=\"handleCommentSubmit\"\n    ></comment-input>\n    \n    <!-- 更多弹窗功能已集成到分享组件中 -->\n    \n    <!-- 分享组件 -->\n    <SharePanel \n      :show=\"isShareVisible\" \n      :noteInfo=\"noteInfo\"\n      :userId=\"userId\"\n      @close=\"closeShare\"\n      @edit=\"handleEdit\"\n      @delete=\"handleDelete\"\n      @report=\"handleReport\"\n      @dislike=\"handleDislike\"\n    ></SharePanel>\n    \n    <!-- 提示弹窗 -->\n    <uni-popup ref=\"tipsPopup\" type=\"top\" :mask-background-color=\"'rgba(0, 0, 0, 0)'\">\n      <view class=\"tips-box df\">\n        <view class=\"tips-item\">{{tipsTitle}}</view>\n      </view>\n    </uni-popup>\n    \n    <!-- 表情预览弹窗 -->\n    <view v-if=\"previewEmojiData\" class=\"emoji-preview-popup\" @tap=\"previewEmojiData = null\">\n      <image \n        class=\"emoji-preview-image\" \n        :src=\"previewEmojiData.url\" \n        mode=\"aspectFit\"\n      ></image>\n    </view>\n  </view>\n</template>\n\n<script>\nimport lazyImage from '@/components/lazyImage/lazyImage'\nimport play from '@/components/play/play'\nimport uniLoadMore from '@/uni_modules/uni-load-more/components/uni-load-more/uni-load-more.vue'\nimport SharePanel from '@/components/share/index.vue'\nimport CommentInput from '@/components/comment-input/comment-input.vue'\nimport sinaEmoji from '@/components/emoji-panel/sina.js'\nimport {\n  getDynamicDetail,\n  likeDynamic,\n  deleteDynamic,\n  getCommentsList,\n  getCommentReplies,\n  addComment,\n  deleteComment,\n  likeComment,\n  unlikeComment,\n  followUser,\n  reportDynamic,\n  vote\n} from '@/api/social.js'\n\nexport default {\n  name: 'NoteDetails',\n  components: {\n    lazyImage,\n    play,\n    uniLoadMore,\n    SharePanel,\n    CommentInput\n  },\n  watch: {\n    // 监听swiperIdx的变化\n    swiperIdx(newVal, oldVal) {\n      if (newVal !== oldVal) {\n        // 强制重新渲染指示器\n        this.$forceUpdate()\n      }\n    }\n  },\n  data() {\n    return {\n      statusBarHeight: this.$store.state.statusBarHeight || 20,\n      titleBarHeight: this.$store.state.titleBarHeight || 44,\n      keyboardHeight: 0,\n      userId: 0,\n      isUser: false,\n      userName: \"\",\n      userNickname: \"\",\n      userAvatar: \"/static/img/avatar_default.png\", // 设置默认头像\n      isFollowing: false,\n      followChecked: false,\n      isLoadingChildReplies: false,\n      commentsCache: {}, // 初始化评论缓存对象\n      commentCache: {}, // 初始化回复缓存对象\n      noteInfo: {\n        id: 0,\n        uid: 0,\n        user_id: 0,\n        type: 0,\n        user_info: {\n          nickname: \"昵称加载中\",\n          avatar: \"/static/img/avatar_default.png\" // 设置默认头像\n        },\n        content: \"内容加载中\",\n        comments: 0,\n        likes: 0,\n        views: 0,\n        is_like: false,\n        create_time: \"日期\",\n        location_name: \"\",\n        province: \"IP属地\",\n        images: [],\n        video: \"\",\n        video_cover: \"\",\n        audio: null\n      },\n      swiperIdx: 0,\n      isEmpty: false,\n      commentView: false,\n      shareView: false,\n      commentList: [],\n      cType: 0,\n      isThrottling: true,\n      cIdx: -1,\n      cI: -1,\n      page: 1,\n      sonPage: 1,\n      limit: 10,\n      loadStatus: \"loading\",\n      cCId: 0,\n      cUId: 0,\n      isComment: false,           // 评论框是否显示\n      isFocus: false,             // 是否有焦点\n      comtips: \"说点什么...\",     // 提示文字\n      comtext: \"\",                // 评论内容\n      commentActioning: false,    // 评论操作进行中\n      commentSource: null,        // 评论来源\n      commentBlurTimer: null,     // 控制失焦关闭的定时器\n      \n      // 音频相关状态 - 只有音频动态时才初始化\n      bgAudioStatus: false,\n      bgAudioManager: null,\n      audioRetryCount: 0,\n      audioPlayingId: '',\n      \n      tipsTitle: \"\",\n      isSubmittingComment: false, // 是否正在提交评论\n      isOpeningComment: false,    // 是否正在打开评论框\n      isLoadingReplies: false,    // 是否正在加载回复\n      isDeletingComment: false,   // 是否正在删除评论\n      likeThrottling: false,      // 点赞防抖\n      isShareVisible: false,      // 分享面板显示状态\n      replyIndices: new Map(),    // 存储回复ID到索引的映射\n      imagesLoaded: false,        // 图片是否已加载\n      actionInProgress: false,\n      debounceTimer: null,\n      lastScrollTop: 0,\n      \n      commentImage: null,\n      atUserList: [],\n      \n      // 表情相关优化\n      emojiMap: new Map(), // 表情映射缓存，提高查找性能\n      parsedContentCache: new Map(), // 解析内容缓存\n      emojiClickTimer: null, // 表情点击防抖定时器\n      isEmojiLoading: false, // 表情加载状态\n      previewEmojiData: null, // 预览表情数据\n      maxCacheSize: 200, // 最大缓存条目数\n      \n      // 性能优化相关\n      isPageActive: true, // 页面是否活跃\n      performanceTimer: null, // 性能监控定时器\n      lazyLoadObserver: null, // 懒加载观察器\n      debug: true, // 是否开启调试日志\n      voting: false,\n    }\n  },\n  computed: {\n    // 空状态判断\n    showEmptyState() {\n      return this.isEmpty && this.page === 1;\n    },\n    \n    // 处理头像展示\n    formattedUserAvatar() {\n      return this.userAvatar || '/static/img/avatar_default.png';\n    },\n    \n    // 发送按钮图标\n    sendButtonSrc() {\n      return this.comtext.length ? '/static/img/fs1.png' : '/static/img/fs.png';\n    },\n    \n    // 是否为音频动态\n    isAudioNote() {\n      return this.noteInfo.type === 4;\n    },\n    \n    // 是否为视频动态\n    isVideoNote() {\n      return this.noteInfo.type === 3;\n    },\n    \n    // 是否为图片动态\n    isImageNote() {\n      return this.noteInfo.type === 1 || this.noteInfo.type === 2;\n    },\n    \n    // 表情缓存统计（用于性能监控）\n    emojiCacheInfo() {\n      return {\n        emojiMapSize: this.emojiMap.size,\n        parsedContentCacheSize: this.parsedContentCache.size,\n        cacheHitRate: this.parsedContentCache.size > 0 ? \n          (this.parsedContentCache.size / (this.parsedContentCache.size + 10)).toFixed(2) : '0.00'\n      };\n    },\n    \n    // 页面性能统计\n    pagePerformanceInfo() {\n      return {\n        commentCacheSize: Object.keys(this.commentCache).length,\n        replyIndicesSize: this.replyIndices.size,\n        isPageActive: this.isPageActive\n      };\n    },\n    votePeopleText() {\n      if (!this.noteInfo.vote_info) return '';\n      const total = this.noteInfo.vote_info.total || 0;\n      if (total >= 10000) return (total/10000).toFixed(1) + '万人参与了投票';\n      if (total >= 1000) return (total/1000).toFixed(1) + 'K人参与了投票';\n      return total + '人参与了投票';\n    }\n  },\n  async onLoad(options) {\n    console.log('页面加载参数:', options);\n    \n    // 标记页面为活跃状态\n    this.isPageActive = true;\n    \n    // 判断平台兼容性，只在支持分享菜单的平台调用\n    if (uni.showShareMenu && typeof uni.showShareMenu === 'function') {\n      uni.showShareMenu();\n    }\n    \n    // 等待小程序初始化完成\n    await this.$onLaunched;\n    console.log('小程序初始化完成');\n    \n    // 初始化表情映射缓存\n    this.initEmojiMap();\n    \n    // 加载最近使用的表情\n    this.loadRecentEmojis();\n    \n    if (options.id) {\n      console.log('获取到笔记ID:', options.id);\n      this.noteInfo.id = options.id;\n      \n      // 是否从评论跳转\n      if (options.comment) {\n        this.commentView = !!options.comment;\n        console.log('从评论跳转');\n      }\n      \n      // 是否分享来源\n      if (options.share) {\n        this.shareView = !!options.share;\n        console.log('从分享跳转');\n      }\n      \n      // 先获取用户信息，再获取笔记详情\n      console.log('开始获取用户信息');\n      this.userInfoHandle();\n      \n      // 获取笔记详情\n      console.log('开始获取笔记详情');\n      this.dynamicDetails();\n    } else {\n      console.error('未提供笔记ID');\n      this.opTipsPopup(\"笔记异常或已被删除，请稍后重试！\", true);\n    }\n  },\n  \n  onShow() {\n    // 标记页面为活跃状态\n    this.isPageActive = true;\n    \n    // 更新用户信息（静默更新，避免重复请求）\n    this.userInfoHandle();\n    \n    // 如果是音频动态且之前正在播放，恢复播放状态检查\n    if (this.isAudioNote && this.bgAudioManager) {\n      this.checkAudioStatus();\n    }\n  },\n  \n  onHide() {\n    // 标记页面为非活跃状态\n    this.isPageActive = false;\n    \n    // 只有音频类型才处理音频暂停\n    if (this.isAudioNote && this.bgAudioManager && this.bgAudioStatus) {\n      this.pauseAudio();\n    }\n  },\n  \n  mounted() {\n    // 判断平台兼容性，只在支持页面滚动监听的平台添加监听\n    if (uni.onPageScroll && typeof uni.onPageScroll === 'function') {\n      uni.onPageScroll(this.handlePageScroll);\n    }\n    \n    // 初始化懒加载\n    this.initLazyLoad();\n  },\n  \n  beforeUnmount() { // Vue3: beforeDestroy改为beforeUnmount\n    // 标记页面为非活跃状态\n    this.isPageActive = false;\n\n    // 清理所有资源\n    this.cleanupResources();\n  },\n  methods: {\n    // 添加调试日志函数\n    debugLog(...args) {\n      if (this.debug) {\n        console.log('[Details]', ...args);\n      }\n    },\n    \n    // 加载最近使用的表情\n    loadRecentEmojis() {\n      try {\n        const recentEmojisStr = uni.getStorageSync('recent_emojis');\n        if (recentEmojisStr) {\n          this.recentEmojis = JSON.parse(recentEmojisStr);\n        }\n      } catch (e) {\n        console.error('加载表情记录失败', e);\n      }\n    },\n    \n    // 初始化懒加载\n    initLazyLoad() {\n      // 这里可以添加图片懒加载逻辑\n      console.log('初始化懒加载');\n    },\n    \n    // 清理所有资源\n    cleanupResources() {\n      console.log('清理页面资源');\n      \n    // 判断平台兼容性，只在支持页面滚动监听的平台移除监听\n    if (uni.offPageScroll && typeof uni.offPageScroll === 'function') {\n      uni.offPageScroll(this.handlePageScroll);\n    }\n    \n      // 销毁音频实例（只有音频动态时才执行）\n      if (this.isAudioNote) {\n    this.destroyAudioInstance();\n      }\n      \n      // 清除所有定时器\n      this.clearAllTimers();\n      \n      // 清除表情相关定时器和缓存\n      this.cleanupEmojiResources();\n      \n      // 清理懒加载观察器\n      if (this.lazyLoadObserver) {\n        this.lazyLoadObserver.disconnect();\n        this.lazyLoadObserver = null;\n      }\n    },\n    \n    // 清除所有定时器\n    clearAllTimers() {\n    if (this.debounceTimer) {\n      clearTimeout(this.debounceTimer);\n      this.debounceTimer = null;\n    }\n    \n      if (this.commentBlurTimer) {\n        clearTimeout(this.commentBlurTimer);\n        this.commentBlurTimer = null;\n      }\n      \n      if (this.performanceTimer) {\n        clearTimeout(this.performanceTimer);\n        this.performanceTimer = null;\n      }\n      \n    if (this.imageCheckInterval) {\n      clearInterval(this.imageCheckInterval);\n      this.imageCheckInterval = null;\n    }\n    },\n    \n    // 清理表情相关资源\n    cleanupEmojiResources() {\n    if (this.emojiClickTimer) {\n      clearTimeout(this.emojiClickTimer);\n      this.emojiClickTimer = null;\n    }\n    \n    // 清理表情缓存以释放内存\n    this.clearEmojiCache();\n  },\n    \n    // 检查是否有圈子\n    hasCircle() {\n      // 新的 circle_info 结构\n      if (this.noteInfo.circle_info && this.noteInfo.circle_info.circle_id > 0) {\n        return true;\n      }\n      // 兼容旧的直接字段结构\n      if (this.noteInfo.circle_id && this.noteInfo.circle_id > 0) {\n        return true;\n      }\n      return false;\n    },\n    \n    // 获取圈子ID\n    getCircleId() {\n      if (this.noteInfo.circle_info && this.noteInfo.circle_info.circle_id) {\n        return this.noteInfo.circle_info.circle_id;\n      }\n      return this.noteInfo.circle_id || 0;\n    },\n    \n    // 获取圈子名称\n    getCircleName() {\n      if (this.noteInfo.circle_info && this.noteInfo.circle_info.circle_name) {\n        return this.noteInfo.circle_info.circle_name;\n      }\n      return this.noteInfo.circle_name || '';\n    },\n    \n    // 获取圈子头像\n    getCircleAvatar() {\n      if (this.noteInfo.circle_info && this.noteInfo.circle_info.circle_avatar) {\n        return this.noteInfo.circle_info.circle_avatar;\n      }\n      return this.noteInfo.circle_avatar || '/static/img/qz1.png';\n    },\n    \n    // 计算图片高度\n    isHigh() {\n      let height = \"750rpx\"; // 默认高度\n      \n      // 处理不同情况下的图片数组，兼容两种格式\n      const imageArray = this.noteInfo.type === 1 ? this.noteInfo.imgs : this.noteInfo.images;\n      \n      // 确保有图片数组且索引有效\n      if (imageArray && imageArray.length > 0 && imageArray[this.swiperIdx]) {\n        const currentImage = imageArray[this.swiperIdx];\n        let wide = 750; // 默认宽度\n        let high = 750; // 默认高度\n        \n        // 获取宽高\n        if (typeof currentImage === 'object') {\n          wide = currentImage.wide || currentImage.width || 750;\n          high = currentImage.high || currentImage.height || 750;\n        }\n        \n        // 计算合适的高度\n        if (wide && high) {\n          // 计算高宽比例\n          let ratio = high / wide;\n          let calcHeight = Math.floor(750 * ratio);\n          \n          // 限制高度在合理范围内\n          if (calcHeight > 1200) calcHeight = 1200;\n          if (calcHeight < 500) calcHeight = 500;\n          \n          height = calcHeight + \"rpx\";\n        }\n      }\n      \n      return height;\n    },\n    \n    // 获取用户信息\n    userInfoHandle() {\n      try {\n        // 统一从本地存储获取用户信息，保持一致性\n      let userInfo = uni.getStorageSync(\"USER_INFO\") || {};\n      console.log('本地存储用户数据类型:', typeof userInfo);\n      \n      // 确保userInfo是对象类型\n      if (typeof userInfo === 'string') {\n        try {\n          userInfo = JSON.parse(userInfo);\n          console.log('已将字符串解析为对象');\n        } catch (e) {\n          console.error('解析用户数据失败:', e);\n          userInfo = {};\n        }\n      }\n      \n        // 从Vuex获取用户ID作为备用\n        const storeUid = this.$store.state.app?.uid || 0;\n      \n        // 优先使用本地缓存中的用户信息，Vuex作为备用\n      this.userId = userInfo.uid || userInfo.id || storeUid || 0;\n      this.userAvatar = userInfo.avatar || '';\n      this.userNickname = userInfo.nickname || '';\n      \n      // 判断用户是否完善资料 - 需要有用户ID和手机号\n      const hasUserId = this.userId > 0;\n      const hasPhone = !!userInfo.phone;\n      this.isUser = hasUserId && hasPhone;\n      \n        console.log('用户信息处理完成:', {\n        userId: this.userId,\n        userAvatar: this.userAvatar,\n        userNickname: this.userNickname,\n        hasUserId: hasUserId,\n        hasPhone: hasPhone,\n        isUser: this.isUser\n      });\n      } catch (error) {\n        console.error('获取用户信息失败:', error);\n        // 设置默认值\n        this.userId = 0;\n        this.userAvatar = '';\n        this.userNickname = '';\n        this.isUser = false;\n      }\n    },\n    \n    \n    // 获取笔记详情\n    dynamicDetails() {\n      // 显示加载状态\n      uni.showLoading({\n        title: '加载中',\n        mask: true\n      });\n      \n      this.debugLog('请求笔记详情，笔记ID:', this.noteInfo.id);\n      \n      getDynamicDetail(this.noteInfo.id)\n        .then(res => {\n          uni.hideLoading();\n          this.debugLog('获取到笔记详情数据:', res);\n          \n          if (res.status === 200 && res.data && res.data.detail) {\n            const detail = res.data.detail;\n            \n            // 检查笔记状态\n            if ((detail.status == 2 || detail.status == 3) && this.userId != detail.uid) {\n              return this.opTipsPopup(\"笔记审核中或已被删除，请稍后重试！\", true);\n            }\n            \n            // 处理话题信息\n            if (detail.topic_id) {\n              try {\n                detail.topic_id = JSON.parse(detail.topic_id);\n              } catch (e) {\n                detail.topic_id = [];\n              }\n            }\n            \n            // 处理话题详情\n            if (detail.topic_info && Array.isArray(detail.topic_info)) {\n              detail.topic_info = detail.topic_info.map(topic => ({\n                id: topic.id,\n                title: topic.title\n              }));\n            } else {\n              detail.topic_info = [];\n            }\n            \n            // 处理商品信息\n            if (detail.goods_info) {\n              detail.goods_info = {\n                id: detail.goods_info.id,\n                image: detail.goods_info.image,\n                store_name: detail.goods_info.store_name,\n                price: detail.goods_info.price\n              };\n            }\n            \n            // 处理圈子信息\n            if (detail.circle_info) {\n              detail.circle_info = {\n                circle_id: detail.circle_info.circle_id || 0,\n                circle_name: detail.circle_info.circle_name || '',\n                circle_avatar: detail.circle_info.circle_avatar || ''\n              };\n            }\n            \n            // 处理用户信息\n            if (detail.user_info) {\n              detail.user_info = {\n                uid: detail.user_info.uid,\n                nickname: detail.user_info.nickname || '用户',\n                avatar: detail.user_info.avatar || '/static/img/avatar_default.png',\n                is_follow: !!detail.user_info.is_follow,\n                is_mutual_follow: !!detail.user_info.is_mutual_follow\n              };\n            } else {\n              detail.user_info = {\n                uid: detail.uid,\n                nickname: '用户',\n                avatar: '/static/img/avatar_default.png',\n                is_follow: false,\n                is_mutual_follow: false\n              };\n            }\n            \n            // 处理图片数据\n            if (detail.images) {\n              if (typeof detail.images === 'string') {\n                try {\n                  detail.images = JSON.parse(detail.images);\n                } catch (e) {\n                  detail.images = detail.images ? [detail.images] : [];\n                }\n              }\n              // 确保images是数组\n              if (!Array.isArray(detail.images)) {\n                detail.images = [];\n              }\n            } else {\n              detail.images = [];\n            }\n            \n            // 更新笔记数据\n            this.noteInfo = {\n              ...this.noteInfo,\n              ...detail,\n              // 确保基础字段存在\n              id: detail.id,\n              uid: detail.uid,\n              type: detail.type || 0,\n              content: detail.content || '',\n              location_name: detail.location_name || '',\n              latitude: detail.latitude || '',\n              longitude: detail.longitude || '',\n              create_time: detail.create_time || '',\n              update_time: detail.update_time || '',\n              likes: parseInt(detail.likes) || 0,\n              comments: parseInt(detail.comments) || 0,\n              views: parseInt(detail.views) || 0,\n              shares: parseInt(detail.shares) || 0,\n              is_like: !!detail.is_like,\n              status: detail.status || 0,\n              is_show: detail.is_show || 0,\n              visibility: detail.visibility || 0,\n              is_top: detail.is_top || 0,\n              video: detail.video || '',\n              video_cover: detail.video_cover || '',\n              audio: detail.audio || '',\n              audio_cover: detail.audio_cover || '',\n              audio_title: detail.audio_title || '',\n              product_id: detail.product_id || 0,\n              circle_id: detail.circle_id || 0\n            };\n            \n            this.debugLog('更新后的笔记数据:', this.noteInfo);\n            \n            // 处理媒体数据\n            this.processMediaData();\n            \n            // 加载评论列表\n            this.getCommentList();\n            \n            // 处理分享\n            if (this.shareView) {\n              this.shareClick(true);\n            }\n            \n            // 同步关注状态\n            if (this.noteInfo.user_info) {\n              this.isFollowing = !!this.noteInfo.user_info.is_follow;\n              this.followChecked = true;\n              this.debugLog('关注状态:', this.isFollowing);\n            }\n          } else {\n            this.debugLog('获取笔记失败:', res.msg);\n            this.opTipsPopup(res.msg || \"获取笔记失败\", true);\n          }\n        })\n        .catch(err => {\n          uni.hideLoading();\n          this.debugLog('获取笔记失败:', err);\n          this.handleError(err, \"获取笔记失败，请稍后重试！\");\n          // 延迟返回上一页\n          setTimeout(() => {\n            uni.navigateBack();\n          }, 2000);\n        });\n    },\n    \n        // 抽取媒体处理逻辑\n    processMediaData() {\n      try {\n        this.debugLog('开始处理媒体数据，笔记类型:', this.noteInfo.type);\n      \n      // 根据类型处理不同的媒体数据\n      switch (this.noteInfo.type) {\n          case 1: // 单图\n          case 2: // 多图\n          this.processImageData();\n          break;\n          case 3: // 视频\n          this.processVideoData();\n          break;\n          case 4: // 音频\n          this.processAudioData();\n          break;\n        default:\n            this.debugLog('未知的媒体类型:', this.noteInfo.type);\n            // 尝试自动判断类型\n            if (this.noteInfo.video) {\n              this.noteInfo.type = 3;\n              this.processVideoData();\n            } else if (this.noteInfo.audio) {\n              this.noteInfo.type = 4;\n              this.processAudioData();\n            } else if (this.noteInfo.images && this.noteInfo.images.length) {\n              this.noteInfo.type = this.noteInfo.images.length > 1 ? 2 : 1;\n              this.processImageData();\n            }\n        }\n      } catch (error) {\n        this.debugLog('处理媒体数据失败:', error);\n      }\n    },\n    \n    // 处理图片数据\n    processImageData() {\n      this.debugLog('处理图片数据');\n      \n      try {\n        // 确保images是数组\n        let images = this.noteInfo.images;\n        if (!Array.isArray(images)) {\n          images = [];\n        }\n        \n        // 处理每张图片\n        this.noteInfo.images = images.map(img => {\n          if (typeof img === 'string') {\n            return {\n              url: img,\n              wide: 750,\n              high: 750\n            };\n          }\n          return img;\n        });\n        \n        this.debugLog('图片数据处理完成，数量:', this.noteInfo.images.length);\n      } catch (e) {\n        this.debugLog('处理图片数据失败:', e);\n        this.noteInfo.images = [];\n      }\n    },\n    \n    // 处理视频数据\n    processVideoData() {\n      console.log('处理视频数据:', this.noteInfo.video);\n      \n      // 确保视频封面存在\n      if (!this.noteInfo.video_cover && this.noteInfo.video && this.noteInfo.video.cover) {\n        this.noteInfo.video_cover = this.noteInfo.video.cover;\n      }\n      \n      // 确保视频URL存在\n      if (!this.noteInfo.video && this.noteInfo.video_url) {\n        this.noteInfo.video = this.noteInfo.video_url;\n      }\n    },\n    \n    // 处理音频数据 - 只有音频动态时才执行\n    processAudioData() {\n      if (!this.isAudioNote) {\n        console.log('非音频动态，跳过音频数据处理');\n        return;\n      }\n      \n      console.log('处理音频数据:', this.noteInfo.audio);\n      \n      // 确保封面图存在\n      if (!this.noteInfo.audio_cover) {\n        this.noteInfo.audio_cover = '/static/img/audio_default_cover.png';\n      }\n      \n      // 确保标题存在\n      if (!this.noteInfo.audio_title) {\n        this.noteInfo.audio_title = '音频';\n      }\n      \n      // 初始化音频状态\n      this.initAudioState();\n    },\n    \n    // 初始化音频状态 - 只有音频动态时才调用\n    initAudioState() {\n      if (!this.isAudioNote) return;\n      \n      console.log('初始化音频状态');\n      \n      // 重置音频相关状态\n      this.bgAudioStatus = false;\n      this.bgAudioManager = null;\n      this.audioRetryCount = 0;\n      this.audioPlayingId = '';\n    },\n    \n    // 标准化图片数组\n    normalizeImageArray(originalImages) {\n          // 确保图片字段是数组\n          if (!originalImages) {\n            console.log('图片数据为空，初始化为空数组');\n        return [];\n          } \n      \n          // 解析JSON字符串\n      if (typeof originalImages === 'string') {\n            console.log('图片数据是字符串，尝试解析JSON');\n            if (originalImages.startsWith('[')) {\n              try {\n                originalImages = JSON.parse(originalImages);\n                console.log('JSON解析成功:', originalImages);\n              } catch (parseErr) {\n                console.error('JSON解析失败:', parseErr);\n                originalImages = [originalImages];\n              }\n            } else {\n              console.log('图片数据是单个字符串，转换为数组');\n              originalImages = [originalImages];\n            }\n          }\n          \n          // 确保是数组类型并标准化每个图片对象\n          if (Array.isArray(originalImages)) {\n            console.log('处理图片数组，数量:', originalImages.length);\n        return originalImages.map((img, index) => {\n              console.log(`处理第${index+1}张图片:`, img);\n              if (typeof img === 'string') {\n                return { url: img, wide: 750, high: 750 };\n              } else if (img && typeof img === 'object') {\n                // 确保即使对象中缺少某些属性，也能正常工作\n                const imgObj = {\n                  url: img.url || img.path || img.src || img.image || '',\n                  wide: parseInt(img.wide || img.width || 750),\n                  high: parseInt(img.high || img.height || 750)\n                };\n                console.log(`图片${index+1}处理结果:`, imgObj);\n                return imgObj;\n              }\n              return { url: '', wide: 750, high: 750 };\n            }).filter(img => !!img.url); // 过滤掉没有URL的图片\n          } else {\n            console.log('图片数据不是数组，初始化为空数组');\n        return [];\n        }\n    },\n    \n    // 处理通用数据\n    processCommonData() {\n      // 确保user_info对象存在\n      if (!this.noteInfo.user_info) {\n        this.noteInfo.user_info = {\n          nickname: \"用户\",\n          avatar: \"/static/img/avatar_default.png\"\n        };\n      }\n      \n      // 确保avatar字段存在\n      if (!this.noteInfo.user_info.avatar) {\n        this.noteInfo.user_info.avatar = \"/static/img/avatar_default.png\";\n      }\n      \n      // 确保comments字段存在且为数字\n      if (this.noteInfo.comments === undefined || this.noteInfo.comments === null) {\n        this.noteInfo.comments = 0;\n      } else if (typeof this.noteInfo.comments === 'string') {\n        this.noteInfo.comments = parseInt(this.noteInfo.comments) || 0;\n      }\n      \n      // 确保uid字段存在\n      if (!this.noteInfo.uid && this.noteInfo.user_id) {\n        this.noteInfo.uid = this.noteInfo.user_id;\n      }\n      \n      console.log('通用数据处理完成');\n    },\n    \n    // 获取评论列表 - 优化版本\n    getCommentList() {\n      // 防止重复加载\n      if (this.isLoadingComments) {\n        this.debugLog('正在加载评论，跳过重复请求');\n        return;\n      }\n      \n      // 如果已经加载完所有评论，不再请求\n      if (this.loadStatus === \"no-more\" && this.page > 1) {\n        this.debugLog('已无更多评论，跳过请求');\n        return;\n      }\n      \n      this.isLoadingComments = true;\n      \n      // 第一页加载时显示加载状态，滚动加载更多时不显示全屏加载\n      if (this.page === 1) {\n        uni.showLoading({\n          title: '加载中',\n          mask: true\n        });\n        } else {\n        // 设置加载中状态\n          this.loadStatus = \"loading\";\n      }\n      \n      // 准备请求参数\n      const params = {\n        type: 0, // 动态评论类型\n        page: this.page || 1,\n        sort_type: this.cType || 0 // 0-热门，1-最新\n      };\n      \n      this.debugLog('获取评论列表', {\n        动态ID: this.noteInfo.id,\n        页码: params.page,\n        排序方式: params.sort_type === 0 ? '热门' : '最新'\n      });\n      \n      // 调用评论列表API\n      getCommentsList(this.noteInfo.id, params)\n        .then(res => {\n          if (this.page === 1) {\n            uni.hideLoading();\n          }\n          \n          this.isLoadingComments = false;\n          \n          if (res.status === 200) {\n            this.debugLog('评论列表获取成功', res.data);\n            \n            const list = res.data.list || [];\n            \n            // 更新评论总数\n            if (res.data.total !== undefined && res.data.total !== null) {\n              this.noteInfo.comments = parseInt(res.data.total) || 0;\n              this.debugLog('从API获取总评论数', this.noteInfo.comments);\n            } else if (res.data.count !== undefined && res.data.count !== null) {\n              this.noteInfo.comments = parseInt(res.data.count) || 0;\n              this.debugLog('从API获取总评论数(count字段)', this.noteInfo.comments);\n            }\n            \n            // 处理评论列表数据\n            const processedList = list.map(item => {\n              return {\n                ...item,\n                user_info: {\n                  uid: item.uid || 0,\n                  nickname: item.user_info?.nickname || '用户',\n                  avatar: item.user_info?.avatar || '/static/img/avatar_default.png'\n                },\n                reply_count: parseInt(item.reply_count) || 0,\n                like_count: parseInt(item.like_count) || 0,\n                is_like: !!item.is_like,\n                create_time: item.create_time || '',\n                content: item.content || '',\n                images: Array.isArray(item.images) ? item.images : [],\n                replies: Array.isArray(item.replies) ? item.replies.map(reply => ({\n                  ...reply,\n                  user_info: {\n                    uid: reply.uid || 0,\n                    nickname: reply.user_info?.nickname || '用户',\n                    avatar: reply.user_info?.avatar || '/static/img/avatar_default.png'\n                  },\n                  reply_user_info: reply.reply_user_info ? {\n                    uid: reply.reply_user_info.uid || 0,\n                    nickname: reply.reply_user_info.nickname || '用户'\n                  } : null,\n                  like_count: parseInt(reply.like_count) || 0,\n                  is_like: !!reply.is_like,\n                  create_time: reply.create_time || '',\n                  content: reply.content || ''\n                })) : []\n              };\n            });\n            \n            // 更新评论列表\n            if (this.page === 1) {\n              // 第一页：直接替换列表\n              this.commentList = processedList;\n              // 设置是否为空状态\n              this.isEmpty = processedList.length === 0;\n            } else {\n              // 加载更多：追加到列表\n              this.commentList = [...this.commentList, ...processedList];\n            }\n            \n            // 更新分页状态\n            if (list.length < 10) { // 本页不足10条，说明没有更多了\n              this.loadStatus = \"no-more\";\n            } else {\n              this.loadStatus = \"more\";\n            }\n            \n            // 缓存第一页数据\n            if (this.page === 1) {\n              const cacheKey = `comments_${this.noteInfo.id}_${this.cType}_${this.page}`;\n              this.commentsCache[cacheKey] = {\n                list: processedList,\n                isEmpty: this.isEmpty,\n                loadStatus: this.loadStatus,\n                totalComments: this.noteInfo.comments\n              };\n            }\n            \n            this.debugLog('评论列表更新完成', {\n              当前页码: this.page,\n              评论总数: this.commentList.length,\n              加载状态: this.loadStatus,\n              是否为空: this.isEmpty\n            });\n          } else {\n            this.debugLog('评论列表获取失败', res);\n            this.loadStatus = \"no-more\";\n            uni.showToast({\n              title: res.msg || '获取评论失败',\n              icon: 'none'\n            });\n          }\n        })\n        .catch(err => {\n          if (this.page === 1) {\n            uni.hideLoading();\n          }\n\n          this.isLoadingComments = false;\n          this.loadStatus = \"no-more\";\n          this.debugLog('获取评论列表异常', err);\n\n          // 使用统一错误处理\n          const errorMsg = this.handleError(err, '获取评论失败');\n          uni.showToast({\n            title: errorMsg,\n            icon: 'none'\n          });\n        });\n    },\n    async onVote(optionId) {\n    if (this.voting || this.noteInfo.vote_info.user_selected) return;\n    this.voting = true;\n    try {\n      // 假设有voteId\n      const voteId = this.noteInfo.vote_info.vote.id;\n      // 调用投票API\n      await vote({ vote_id: voteId, option_id: optionId });\n      // 重新拉取详情\n      this.dynamicDetails();\n    } catch (e) {\n      this.handleError(e, '投票失败');\n    } finally {\n      this.voting = false;\n    }\n  },\n    // 加载评论回复（分页方式）\nloadAllReplies(e) {\n  let id = parseInt(e.currentTarget.dataset.id) || 0\n  let idx = parseInt(e.currentTarget.dataset.idx) || 0\n  \n  console.log('加载评论回复:', {\n    commentId: id,\n    commentIndex: idx\n  });\n  \n  // 防止重复点击\n  if (this.isLoadingReplies) return\n  this.isLoadingReplies = true\n  \n  // 显示加载中状态\n  const commentItem = this.commentList[idx]\n  if (commentItem) {\n    this.$set(commentItem, 'loading_replies', true)\n  }\n  \n  // 获取当前评论的回复页码\n  const currentPage = parseInt(commentItem.replyPage) || 1;\n  \n  // 生成缓存键\n  const cacheKey = `replies_${id}_${currentPage}`;\n  \n  // 检查缓存\n  if (this.commentCache[cacheKey]) {\n    console.log('使用缓存中的回复数据');\n    this.handleAllRepliesData(this.commentCache[cacheKey], idx, currentPage);\n    return;\n  }\n  \n  // 请求参数\n  const params = {\n    parent_id: parseInt(id) || 0,\n    page: currentPage,\n    limit: 3, // 每次只加载少量数据\n    sort_type: 1 // 按时间最新排序\n  };\n  \n  console.log('获取回复请求参数:', params);\n  \n  // 发起请求\n  getCommentReplies(params)\n    .then(res => {\n      if (res.status === 200) {\n        console.log('获取到回复数据:', res.data);\n        \n        // 缓存结果\n        this.commentCache[cacheKey] = res.data;\n        \n        // 处理回复数据\n        this.handleAllRepliesData(res.data, idx, currentPage);\n      } else {\n        // 移除加载状态\n        if (commentItem) {\n          this.$set(commentItem, 'loading_replies', false);\n        }\n        this.isLoadingReplies = false;\n        \n        uni.showToast({\n          title: res.msg || '获取回复失败',\n          icon: 'none'\n        });\n      }\n    })\n    .catch(err => {\n      console.error('获取回复失败:', err);\n      \n      // 移除加载状态\n      if (commentItem) {\n        this.$set(commentItem, 'loading_replies', false);\n      }\n      this.isLoadingReplies = false;\n      \n      uni.showToast({\n        title: '获取回复失败',\n        icon: 'none'\n      });\n    });\n},\n    \n    // 加载子回复列表 - 扁平化处理\n    loadChildrenReplies(e) {\n      let parentId = parseInt(e.currentTarget.dataset.parentId) || 0\n      let replyId = parseInt(e.currentTarget.dataset.replyId) || 0\n      let idx = parseInt(e.currentTarget.dataset.idx) || 0\n      let replyIndex = parseInt(e.currentTarget.dataset.replyIndex) || 0\n      \n      console.log('加载子回复:', {\n        parentId,\n        replyId,\n        commentIndex: idx,\n        replyIndex\n      });\n      \n      // 防止重复点击\n      if (this.isLoadingChildReplies) return\n      this.isLoadingChildReplies = true\n      \n      // 获取当前回复的子回复页码\n      const commentItem = this.commentList[idx];\n      const replyItem = commentItem.replies[replyIndex];\n      const currentPage = parseInt(replyItem.childrenPage) || 1;\n      \n      // 显示加载中状态\n      this.$set(replyItem, 'loading_children', true);\n      \n      // 生成缓存键\n      const cacheKey = `replies_${parentId}_${replyId}_${currentPage}_2`;\n      \n      // 检查是否有缓存\n      if (this.commentCache[cacheKey]) {\n        console.log('使用缓存中的子回复数据');\n        this.handleChildrenRepliesData(this.commentCache[cacheKey], idx, replyIndex, currentPage);\n        return;\n      }\n      \n      // 请求参数 - 使用统一接口\n      const params = {\n        parent_id: parseInt(parentId) || 0,     // 父评论ID\n        reply_id: parseInt(replyId) || 0,       // 回复ID\n        reply_type: parseInt(2),           // 回复类型：2=子回复\n        page: parseInt(currentPage) || 1,       // 页码\n        limit: parseInt(10),               // 每页数量\n        sort_type: parseInt(0)             // 默认热门排序\n      };\n      \n      console.log('获取子回复请求参数:', params);\n      \n      // 发起请求\n      getCommentReplies(params)\n        .then(res => {\n          // 移除加载状态\n          this.$set(replyItem, 'loading_children', false);\n          this.isLoadingChildReplies = false;\n          \n          console.log('获取子回复响应:', res);\n          \n          if (res.status === 200) {\n            // 缓存结果\n            this.commentCache[cacheKey] = res.data;\n            \n            // 处理子回复数据\n            this.handleChildrenRepliesData(res.data, idx, replyIndex, currentPage);\n          } else {\n            uni.showToast({\n              title: res.msg || '获取子回复失败',\n              icon: 'none'\n            });\n          }\n        })\n        .catch(err => {\n          // 移除加载状态\n          this.$set(replyItem, 'loading_children', false);\n          this.isLoadingChildReplies = false;\n          \n          console.error('获取子回复失败:', err)\n          uni.showToast({\n            title: '获取子回复失败',\n            icon: 'none'\n          })\n        });\n    },\n    \n          // 处理回复数据（分页方式）\n    handleAllRepliesData(data, idx, page) {\n      if (this.commentList[idx]) {\n        const commentItem = this.commentList[idx];\n        const replies = data.list || [];\n        \n        console.log('获取到回复数据:', replies);\n        \n        // 如果是第一页，直接替换现有回复列表\n        if (page === 1) {\n          this.$set(commentItem, 'replies', replies);\n        } else {\n          // 否则追加到现有列表\n          // 避免重复添加已加载的回复\n          const existingIds = (commentItem.replies || []).map(r => r.id);\n          const newReplies = replies.filter(r => !existingIds.includes(r.id));\n          \n          this.$set(commentItem, 'replies', [...(commentItem.replies || []), ...newReplies]);\n        }\n        \n        // 更新回复页码\n        this.$set(commentItem, 'replyPage', page + 1);\n        \n        // 标记是否有更多回复\n        // 如果已加载的回复数量等于或超过总回复数，设置为无更多回复\n        const replyCount = parseInt(data.count || 0);\n        const currentLoadedCount = commentItem.replies ? commentItem.replies.length : 0;\n        \n        // 判断是否已加载全部回复\n        const hasNoMoreReplies = replies.length < 3 || currentLoadedCount >= replyCount;\n        this.$set(commentItem, 'has_more_replies', !hasNoMoreReplies);\n        \n        // 更新总回复数\n        if (data.count !== undefined) {\n          this.$set(commentItem, 'reply_count', replyCount);\n        }\n        \n        // 更新回复索引映射\n        this.updateReplyIndices(commentItem.replies);\n        \n        // 移除加载状态\n        this.$set(commentItem, 'loading_replies', false);\n        this.isLoadingReplies = false;\n      }\n    },\n    \n    // 处理子回复数据\n    handleChildrenRepliesData(data, idx, replyIndex, page) {\n      if (this.commentList[idx] && this.commentList[idx].replies[replyIndex]) {\n        const replyItem = this.commentList[idx].replies[replyIndex];\n        \n        // 获取原始子回复列表\n        const childReplies = data.list || [];\n        \n        // 处理子回复数据，设置正确的回复关系\n        childReplies.forEach(child => {\n          // 设置回复目标信息，保存回复对象的昵称\n          child.reply_nickname = replyItem.nickname || '用户';\n          \n          // 将所有子回复直接添加到一级评论的replies中，扁平化处理\n          this.commentList[idx].replies.push(child);\n        });\n        \n        // 更新回复索引映射\n        this.updateReplyIndices(this.commentList[idx].replies);\n        \n        // 更新子回复已加载标记\n        this.$set(replyItem, 'has_more_children', false);\n        \n        // 移除加载状态\n        this.$set(replyItem, 'loading_children', false);\n        this.isLoadingChildReplies = false;\n      }\n    },\n    \n    // 递归更新回复索引映射\n    updateReplyIndices(replies) {\n      if (!replies || !replies.length) return;\n      \n      replies.forEach((reply, index) => {\n        this.replyIndices.set(reply.id, index);\n        \n        // 递归处理子回复\n        if (reply.children && reply.children.length) {\n          this.updateReplyIndices(reply.children);\n        }\n      });\n    },\n    \n    // 获取子评论（分页加载，已被loadAllReplies替代，保留作为备用）\n    sonComment(e) {\n      let id = e.currentTarget.dataset.id\n      let idx = e.currentTarget.dataset.idx\n      \n      console.log('获取子评论:', {\n        commentId: id,\n        commentIndex: idx,\n        currentPage: this.sonPage\n      });\n      \n      // 防止重复点击\n      if (this.isLoadingReplies) return\n      this.isLoadingReplies = true\n      \n      // 显示加载中状态\n      const commentItem = this.commentList[idx]\n      if (commentItem) {\n        this.$set(commentItem, 'loading_replies', true)\n      }\n      \n      // 更新子评论加载页码\n      this.sonPage = this.sonPage + 1\n      \n      // 生成缓存键\n      const cacheKey = `reply_${id}_${this.sonPage}`;\n      \n      // 检查是否有缓存\n      if (this.commentCache[cacheKey]) {\n        console.log('使用缓存中的回复数据');\n        this.handleReplyData(this.commentCache[cacheKey], idx);\n        return;\n      }\n      \n      // 请求参数 - 与CommentController.php中的getReplies方法参数匹配\n      const params = {\n        parent_id: id,           // 父评论ID\n        page: this.sonPage,      // 页码\n        limit: 10,               // 每页数量\n        sort_type: 1             // 排序类型：1=最新(创建时间)\n      };\n      \n      console.log('回复请求参数:', params);\n      \n      // 发起请求\n      getCommentReplies(params)\n        .then(res => {\n          // 移除加载状态\n          if (commentItem) {\n            this.$set(commentItem, 'loading_replies', false)\n          }\n          this.isLoadingReplies = false\n          \n          console.log('获取回复响应:', res);\n          \n          if (res.status === 200) {\n            // 缓存结果\n            this.commentCache[cacheKey] = res.data;\n            \n            // 处理回复数据\n            this.handleReplyData(res.data, idx);\n          } else {\n            uni.showToast({\n              title: res.msg || '获取回复失败',\n              icon: 'none'\n            });\n          }\n        })\n        .catch(err => {\n          // 移除加载状态\n          if (commentItem) {\n            this.$set(commentItem, 'loading_replies', false)\n          }\n          this.isLoadingReplies = false\n          \n          console.error('获取回复失败:', err)\n          uni.showToast({\n            title: '获取回复失败',\n            icon: 'none'\n          })\n        })\n    },\n    \n    // 处理回复数据\n    handleReplyData(data, idx) {\n      const list = data.list || [];\n      \n      if (this.commentList[idx]) {\n        // 确保replies数组存在\n        if (!this.commentList[idx].replies) {\n          this.commentList[idx].replies = [];\n        }\n        \n        // 记录之前的回复数量\n        const previousReplyCount = this.commentList[idx].replies.length;\n        \n        // 追加到现有列表\n        this.commentList[idx].replies = [...this.commentList[idx].replies, ...list];\n        \n        // 更新回复索引映射\n        list.forEach((reply, i) => {\n          this.replyIndices.set(reply.id, previousReplyCount + i);\n        });\n        \n        // 更新子评论数量\n        if (data.count !== undefined && data.count !== null) {\n          this.commentList[idx].reply_count = parseInt(data.count) || 0;\n        } else {\n          this.commentList[idx].reply_count = this.commentList[idx].replies.length;\n        }\n        \n        // 判断是否已加载全部回复\n        if (list.length < 10 || this.commentList[idx].replies.length >= this.commentList[idx].reply_count) {\n          this.$set(this.commentList[idx], 'all_replies_loaded', true);\n        }\n      }\n    },\n    \n    // 切换评论排序\n    commentClick(type) {\n      if (!this.isThrottling || this.actionInProgress) return\n      \n      // 如果切换到相同类型，不重复加载\n      if (this.cType === type) return\n      \n      this.isThrottling = false\n      this.cType = type\n      this.page = 1\n      this.loadStatus = \"loading\"\n      \n      // 重置评论列表\n      this.commentList = []\n      this.isEmpty = false\n      \n      this.getCommentList()\n      \n      // 设置延时，允许在一段时间后再次切换\n      setTimeout(() => {\n        this.isThrottling = true\n      }, 500)\n    },\n    \n    // 打开评论框\n    openComment(e) {\n      // 如果没有事件对象，创建一个空对象模拟\n      e = e || { currentTarget: { dataset: { type: 0 } } };\n      \n      // 阻止事件冒泡\n      e.stopPropagation && e.stopPropagation();\n      \n      console.log('尝试打开评论框，用户状态:', {\n        isUser: this.isUser,\n        userId: this.userId,\n        userInfo: {\n          avatar: this.userAvatar,\n          nickname: this.userNickname\n        }\n      });\n      \n      if (!this.isUser) {\n        console.log('用户未完善资料，无法评论');\n        this.opTipsPopup(\"完善账号资料后即可评论！\")\n        setTimeout(() => {\n          uni.navigateTo({\n            url: \"/pages/center/means\"\n          })\n        }, 1000)\n        return\n      }\n      \n      let dataset = e.currentTarget.dataset || {};\n      let type = dataset.type || 0;\n      let uid = dataset.uid || 0;\n      let cid = dataset.cid || 0;\n      let name = dataset.name || \"\";\n      \n      this.cIdx = dataset.idx !== undefined ? dataset.idx : -1;\n      this.cI = dataset.i !== undefined ? dataset.i : -1;\n      \n      // 强制关闭再打开，解决偶尔无法弹出的问题\n      this.isComment = false;\n      \n      // 重置状态变量\n      this.isSubmittingComment = false;\n      \n      // 设置评论目标信息\n      if (type == 1) {\n        this.cCId = cid;\n        this.cUId = uid;\n        this.comtips = \"回复：\" + name;\n      } else {\n        this.cCId = 0;\n        this.cUId = 0;\n        this.comtips = \"说点什么...\";\n      }\n      \n      // 使用nextTick确保DOM已更新\n      this.$nextTick(() => {\n        // 先显示评论框，再延迟聚焦\n        this.isComment = true;\n        \n        // 延迟聚焦，确保键盘能弹出\n        setTimeout(() => {\n          this.isFocus = true;\n          \n          // 再次确认评论框已显示\n          if (!this.isComment) {\n            this.isComment = true;\n          }\n        }, 150);\n      });\n    },\n    \n    // 保存评论\n    saveComment() {\n      // 立即设置标记，防止textareaBlur的延时关闭\n      this.isSubmittingComment = true;\n      \n      let self = this;\n      \n      if (!self.comtext.trim()) {\n        self.isSubmittingComment = false; // 重置状态\n        return self.opTipsPopup(\"表达你的态度再评论吧！\");\n      }\n      \n      // 立即将isComment设为false，避免延时关闭的影响\n      self.isComment = false;\n      self.isFocus = false;\n      \n      uni.showLoading({\n        title: '发布中',\n        mask: true\n      });\n      \n      // 准备评论参数 - 根据API文档定义的参数格式\n      const commentParams = {\n        type: 0,                  // 评论类型：0-动态\n        target_id: self.noteInfo.id, // 目标ID\n        content: self.comtext,    // 评论内容 - 保留原始表情格式如[微笑]\n        reply_id: self.cCId || 0,   // 父评论ID（后端会自动处理parent_id）\n        image: ''                   // 图片字段\n      };\n      \n      console.log('提交评论参数:', commentParams);\n      \n      // 使用对象参数调用方式\n      addComment(commentParams).then(res => {\n        uni.hideLoading()\n        self.isSubmittingComment = false\n        \n        console.log('评论提交响应:', res);\n        \n        if (res.status === 200) {\n            // 增加评论总数\n            self.noteInfo.comments = (self.noteInfo.comments || 0) + 1\n            \n          // 优先使用服务器返回的评论数据，如果没有则使用默认数据\n          let commentData = res.data || self.createDefaultCommentData(self.comtext, '');\n          \n          console.log('使用的评论数据:', commentData);\n            \n            // 回复评论\n            if (self.cIdx >= 0 && self.cI == -1) {\n              console.log('添加回复到主评论:', self.cIdx);\n              // 确保replies数组存在\n              if (!self.commentList[self.cIdx].replies) {\n                self.commentList[self.cIdx].replies = [];\n              }\n              if (!self.commentList[self.cIdx].reply_count) {\n                self.commentList[self.cIdx].reply_count = 0;\n              }\n              \n              // 将新回复添加到回复列表（按时间正序，所以应该添加到最后）\n              self.commentList[self.cIdx].replies.push(commentData);\n              self.commentList[self.cIdx].reply_count += 1;\n              \n              // 更新回复索引映射\n              self.replyIndices.set(commentData.id, self.commentList[self.cIdx].replies.length - 1);\n              \n              // 强制更新视图\n              self.$forceUpdate();\n            }\n            // 回复回复\n            else if (self.cIdx >= 0 && self.cI >= 0) {\n              console.log('添加回复到子评论:', self.cIdx, self.cI);\n              // 确保replies数组存在\n              if (!self.commentList[self.cIdx].replies) {\n                self.commentList[self.cIdx].replies = [];\n              }\n              if (!self.commentList[self.cIdx].reply_count) {\n                self.commentList[self.cIdx].reply_count = 0;\n              }\n              \n              // 将新回复添加到回复列表末尾（按时间正序）\n              self.commentList[self.cIdx].replies.push(commentData);\n              self.commentList[self.cIdx].reply_count += 1;\n              \n              // 更新回复索引映射\n              self.replyIndices.set(commentData.id, self.commentList[self.cIdx].replies.length - 1);\n              \n              // 强制更新视图\n              self.$forceUpdate();\n            }\n            // 新评论\n            else {\n              console.log('添加新评论');\n              if (self.commentList.length <= 0) {\n                self.isEmpty = false\n                self.commentList = []\n              }\n              \n              // 初始化replies字段\n              commentData.replies = []\n              commentData.reply_count = 0\n              \n              self.commentList.unshift(commentData)\n              \n              // 强制更新视图\n              self.$forceUpdate();\n          }\n          \n          self.comtext = \"\"\n          self.cCId = 0\n          self.cUId = 0\n          self.comtips = \"说点什么...\"\n          self.opTipsPopup(\"评论成功\")\n        } else {\n          self.opTipsPopup(res.msg || \"评论失败\")\n        }\n        \n        self.isComment = false\n      }).catch(err => {\n        uni.hideLoading()\n        self.isSubmittingComment = false\n        console.error('评论提交失败:', err);\n        self.handleError(err, \"评论失败\")\n        self.isComment = false\n      })\n    },\n    \n    // 删除评论\n    delComment(e) {\n      let self = this\n      let idx = e.currentTarget.dataset.idx\n      let i = e.currentTarget.dataset.i\n      let commentId = e.currentTarget.dataset.id\n      \n      // 防止重复点击\n      if (self.isDeletingComment) return\n      \n      uni.showModal({\n        content: \"确定要永久删除该评论吗？\",\n        confirmColor: \"#FA5150\",\n        success: function(res) {\n          if (res.confirm) {\n            self.isDeletingComment = true\n            \n            uni.showLoading({\n              title: '删除中',\n              mask: true\n            })\n            \n            // 使用新的删除评论API接口\n            deleteComment(commentId).then(res => {\n              uni.hideLoading()\n              self.isDeletingComment = false\n              \n              if (res.status === 200) {\n                // 减少评论总数\n                if (self.noteInfo.comments > 0) {\n                  self.noteInfo.comments--\n                }\n              \n              // 更新评论状态\n              if (i == -1) {\n                  // 主评论被删除\n                self.commentList[idx].delete_time = new Date().toISOString() // 设置删除时间\n                self.commentList[idx].status = 0 // 设置删除状态\n              } else {\n                  // 确保replies数组存在\n                  if (self.commentList[idx].replies && self.commentList[idx].replies.length > i) {\n                    self.commentList[idx].replies[i].delete_time = new Date().toISOString() // 设置删除时间\n                    self.commentList[idx].replies[i].status = 0 // 设置删除状态\n                    \n                    // 子评论被删除，也要减少对应的计数\n                    if (self.commentList[idx].reply_count > 0) {\n                      // 减少回复计数\n                      self.commentList[idx].reply_count--\n                    }\n                  }\n                }\n                self.opTipsPopup(\"删除成功\")\n              } else {\n                self.opTipsPopup(res.msg || \"删除失败\")\n              }\n            }).catch(err => {\n              uni.hideLoading()\n              self.isDeletingComment = false\n              self.handleError(err, \"删除失败\")\n            })\n          }\n        }\n      })\n    },\n    \n    // 点赞/取消点赞评论\n    toggleCommentLike(commentId, isCurrentlyLiked) {\n      if (!this.isUser) {\n        this.opTipsPopup(\"请先完善账号资料\")\n        return\n      }\n      \n      // 防止频繁点击\n      if (this.likeThrottling) return\n      this.likeThrottling = true\n      \n      // 500ms后恢复\n      setTimeout(() => {\n        this.likeThrottling = false\n      }, 500)\n      \n      // 将布尔值转换为0/1格式\n      const currentLikeState = isCurrentlyLiked ? 1 : 0;\n      const newLikeState = currentLikeState ? 0 : 1;\n      \n      // 优先更新UI状态，让用户感觉响应迅速\n      // 遍历更新评论列表中的点赞状态\n      for (let i = 0; i < this.commentList.length; i++) {\n        const comment = this.commentList[i]\n        \n        // 更新主评论\n        if (comment.id === commentId) {\n          comment.is_like = newLikeState\n          if (newLikeState) {\n            comment.likes = (comment.likes || 0) + 1\n          } else if (comment.likes > 0) {\n            comment.likes--\n          }\n          break // 找到了主评论，可以跳出循环\n        }\n        \n        // 更新回复评论\n        if (comment.replies && comment.replies.length > 0) {\n          for (let j = 0; j < comment.replies.length; j++) {\n            const reply = comment.replies[j]\n            if (reply.id === commentId) {\n              reply.is_like = newLikeState\n              if (newLikeState) {\n                reply.likes = (reply.likes || 0) + 1\n              } else if (reply.likes > 0) {\n                reply.likes--\n              }\n              break // 找到了回复，可以跳出内部循环\n            }\n          }\n        }\n      }\n      \n      // 后台执行API调用\n      if (currentLikeState) {\n        // 如果当前已点赞，则取消点赞\n        unlikeComment(commentId)\n          .then(res => {\n            // 点赞成功，不需要操作\n          })\n          .catch(err => {\n            // 恢复状态\n            this.restoreCommentLikeStatus(commentId, currentLikeState)\n            this.handleError(err, \"操作失败，请重试\")\n          })\n      } else {\n        // 如果当前未点赞，则点赞\n        likeComment(commentId)\n          .then(res => {\n            // 点赞成功，不需要操作\n          })\n          .catch(err => {\n            // 恢复状态\n            this.restoreCommentLikeStatus(commentId, currentLikeState)\n            this.handleError(err, \"操作失败，请重试\")\n          })\n      }\n    },\n    \n    // 恢复评论点赞状态（操作失败时）\n    restoreCommentLikeStatus(commentId, originalLikeState) {\n      // 操作失败时恢复状态\n      for (let i = 0; i < this.commentList.length; i++) {\n        const comment = this.commentList[i]\n        \n        // 还原主评论\n        if (comment.id === commentId) {\n          comment.is_like = originalLikeState\n          if (originalLikeState === 1) {\n            if (comment.likes > 0) comment.likes--\n          } else {\n            comment.likes++\n          }\n          break // 找到后跳出循环\n        }\n        \n        // 还原回复评论\n        if (comment.replies && comment.replies.length > 0) {\n          for (let j = 0; j < comment.replies.length; j++) {\n            const reply = comment.replies[j]\n            if (reply.id === commentId) {\n              reply.is_like = originalLikeState\n              if (originalLikeState === 1) {\n                if (reply.likes > 0) reply.likes--\n              } else {\n                reply.likes++\n              }\n              break // 找到后跳出循环\n            }\n          }\n        }\n      }\n    },\n    \n    // 删除笔记\n    delDynamic() {\n      let self = this\n      \n      uni.showModal({\n        content: \"确认要永久删除这篇笔记吗？\",\n        confirmColor: \"#FA5150\",\n        success: function(res) {\n          if (res.confirm) {\n            uni.showLoading({\n              mask: true\n            })\n            \n            // 使用导入的API方法\n            deleteDynamic(self.noteInfo.id).then(res => {\n              uni.hideLoading()\n              getApp().globalData.isCenterPage = true\n              \n              if (res.status === 200) {\n                self.opTipsPopup(\"删除成功\", true)\n              } else {\n                self.opTipsPopup(res.msg || \"删除失败\")\n              }\n            }).catch(err => {\n              uni.hideLoading()\n              self.handleError(err, \"删除失败\")\n            })\n          }\n        }\n      })\n    },\n    \n    // 举报笔记\n    reasonClick(reason) {\n      let self = this\n      \n      uni.showLoading({\n        mask: true\n      })\n      \n      // 获取合适的图片URL\n      let imageUrl = ''\n      if (self.noteInfo.type == 2 && self.noteInfo.images && self.noteInfo.images.length > 0) {\n        imageUrl = self.noteInfo.images[0].url\n      } else if (self.noteInfo.type == 3 && self.noteInfo.video && self.noteInfo.video_cover) {\n        imageUrl = self.noteInfo.video_cover\n      } else if (self.noteInfo.type == 4 && self.noteInfo.audio && self.noteInfo.audio.cover) {\n        imageUrl = self.noteInfo.audio.cover\n      }\n      \n      // 使用新的API调用方式\n      reportDynamic(\n        reason,\n        self.noteInfo.id,\n        self.noteInfo.uid,\n        self.noteInfo.content,\n        imageUrl\n      ).then(res => {\n        uni.hideLoading()\n        self.opTipsPopup(res.msg || \"举报成功\")\n        self.menuPopupClick(false)\n      }).catch(err => {\n        uni.hideLoading()\n        self.handleError(err, '举报失败')\n      })\n    },\n    \n    // 点赞笔记\n    likeDynamic() {\n      if (this.actionInProgress) return;\n      this.actionInProgress = true;\n      \n      // 更新点赞状态 - 使用0和1替代布尔值，与后端一致\n      const currentLikeState = this.noteInfo.is_like ? 1 : 0;\n      const newLikeState = currentLikeState ? 0 : 1;\n      const oldLikes = this.noteInfo.likes;\n      \n      // 立即更新UI - 保存为0或1\n      this.noteInfo.is_like = newLikeState;\n      this.noteInfo.likes = newLikeState ? oldLikes + 1 : oldLikes - 1;\n      \n      // API调用\n      likeDynamic({\n        id: this.noteInfo.id,\n        is_like: newLikeState // 直接使用0或1\n      })\n      .then(res => {\n        // 成功，无需处理\n      })\n      .catch(err => {\n        // 失败，恢复状态\n        this.noteInfo.is_like = currentLikeState;\n        this.noteInfo.likes = oldLikes;\n        this.handleError(err, '操作失败，请重试');\n      })\n      .finally(() => {\n        // 设置短时间防抖\n        setTimeout(() => {\n          this.actionInProgress = false;\n        }, 500);\n      });\n    },\n    \n    // 关注/取消关注用户\n    followUser() {\n      if (!this.noteInfo.uid || this.actionInProgress) return;\n      \n      this.actionInProgress = true;\n      \n      // 立即更新UI状态\n      const newFollowState = !this.isFollowing;\n      const oldFollowState = this.isFollowing;\n      \n      this.isFollowing = newFollowState;\n      \n      // 同步更新用户信息\n      if (this.noteInfo.user_info) {\n        this.noteInfo.user_info.is_follow = newFollowState;\n      }\n      \n      // API调用\n      followUser({\n        follow_uid: this.noteInfo.uid,\n        is_follow: newFollowState ? 1 : 0\n      })\n      .then(res => {\n        // 成功无需处理\n      })\n      .catch(err => {\n        // 失败恢复状态\n        this.isFollowing = oldFollowState;\n        if (this.noteInfo.user_info) {\n          this.noteInfo.user_info.is_follow = oldFollowState;\n        }\n        this.handleError(err, \"操作失败，请重试\");\n      })\n      .finally(() => {\n        // 设置短时间防抖\n        setTimeout(() => {\n          this.actionInProgress = false;\n        }, 500);\n      });\n    },\n    \n    // 点击图片预览\n    swiperClick(e) {\n      // 确保是图片类型 (兼容两种图片类型)\n      if (this.noteInfo.type !== 2 && this.noteInfo.type !== 1) return\n      \n      let i = e.currentTarget.dataset.i\n      let urls = []\n      \n      // 根据类型确定使用的图片数组\n      const imageArray = this.noteInfo.type === 1 ? this.noteInfo.imgs : this.noteInfo.images\n      \n      // 处理不同格式的图片数组\n      if (imageArray && imageArray.length > 0) {\n        if (typeof imageArray[0] === 'string') {\n          // 如果是字符串数组，直接使用\n          urls = imageArray\n        } else {\n          // 如果是对象数组，提取url字段\n          for (let item of imageArray) {\n            const imageUrl = this.getImageSrc(item);\n            if (imageUrl) {\n              urls.push(imageUrl)\n            }\n          }\n        }\n      }\n      \n      // 确保有可预览的图片URL\n      if (urls.length === 0) {\n        console.error('没有可预览的图片URL');\n        return;\n      }\n      \n      // 调用预览API\n      uni.previewImage({\n        current: urls[i] || urls[0],\n        urls: urls\n      })\n    },\n    \n    // 关闭评论框\n    closeComment(e) {\n      // 阻止事件冒泡\n      e && e.stopPropagation && e.stopPropagation();\n      \n      console.log('手动关闭评论框');\n      \n      // 如果正在提交评论，不关闭\n      if (this.isSubmittingComment) {\n        return;\n      }\n      \n      // 清除blur定时器\n      if (this.commentBlurTimer) {\n        clearTimeout(this.commentBlurTimer);\n        this.commentBlurTimer = null;\n      }\n      \n      // 设置为非评论操作状态\n      this.commentActioning = false;\n      \n      // 关闭评论框和焦点\n      this.isComment = false;\n      this.isFocus = false;\n      \n      // 清空输入内容\n      this.comtext = \"\";\n    },\n    \n    // 轮播图切换\n    swiperChange(e) {\n      // 立即更新索引，确保指示器更新\n      setTimeout(() => {\n      this.swiperIdx = e.detail.current\n        // 强制视图更新\n        this.$forceUpdate()\n      }, 0)\n    },\n    \n    // 打开位置\n    openLocationClick() {\n      uni.openLocation({\n        latitude: parseFloat(this.noteInfo.latitude),\n        longitude: parseFloat(this.noteInfo.longitude),\n        name: this.noteInfo.location_name\n      })\n    },\n    \n    // 更多弹窗功能已移至分享组件\n    \n    // 分享\n    shareClick(show) {\n      this.isShareVisible = show;\n    },\n    \n    // 关闭分享面板\n    closeShare() {\n      this.isShareVisible = false;\n    },\n    \n    // 处理编辑笔记\n    handleEdit(noteId) {\n      uni.navigateTo({\n        url: `/pages/note/add?id=${noteId}`\n      });\n    },\n    \n    // 处理删除笔记\n    handleDelete(noteId) {\n      this.delDynamic();\n    },\n    \n    // 处理举报笔记\n    handleReport(reportData) {\n      this.reasonClick(reportData.reason);\n    },\n    \n    // 处理不感兴趣\n    handleDislike(noteId) {\n      console.log('标记不感兴趣:', noteId);\n      // 这里可以添加具体的不感兴趣处理逻辑\n    },\n    \n        // 页面跳转\n    navigateToFun(e) {\n      uni.navigateTo({\n        url: \"/pages/\" + e.currentTarget.dataset.url\n      })\n    },\n    \n    // 初始化表情映射缓存\n    initEmojiMap() {\n      if (this.emojiMap.size > 0) return; // 避免重复初始化\n      \n      try {\n        // 将表情数组转换为Map，提高查找性能\n        sinaEmoji.forEach(emoji => {\n          if (emoji && emoji.phrase) {\n            this.emojiMap.set(emoji.phrase, emoji);\n          }\n        });\n        console.log('表情映射缓存初始化完成，共', this.emojiMap.size, '个表情');\n      } catch (error) {\n        console.error('初始化表情映射失败:', error);\n      }\n    },\n\n    // 优化后的表情内容解析\n    parseEmojiContent(text) {\n      if (!text || typeof text !== 'string') return [];\n      \n      // 检查缓存\n      const cacheKey = text;\n      if (this.parsedContentCache.has(cacheKey)) {\n        return this.parsedContentCache.get(cacheKey);\n      }\n      \n      const nodes = [];\n      let lastIndex = 0;\n      \n      // 优化后的正则表达式，更精确匹配表情格式\n      const regex = /\\[([^\\[\\]]{1,10})\\]/g;\n      let match;\n      \n      try {\n      while ((match = regex.exec(text)) !== null) {\n        // 添加表情前的文本\n        if (match.index > lastIndex) {\n            const textContent = text.substring(lastIndex, match.index);\n            if (textContent) {\n          nodes.push({\n            type: 'text',\n                text: textContent\n          });\n            }\n        }\n        \n          // 使用Map快速查找表情\n          const emojiPhrase = match[0];\n          const emoji = this.emojiMap.get(emojiPhrase);\n        \n                      if (emoji && emoji.url) {\n              // 添加表情图片，强制固定尺寸\n          nodes.push({\n            type: 'image',\n            attrs: {\n              src: emoji.url,\n                  class: 'emoji-img',\n                  style: 'width: 32rpx !important; height: 32rpx !important; max-width: 32rpx !important; max-height: 32rpx !important; min-width: 32rpx !important; min-height: 32rpx !important; vertical-align: middle; margin: 0 4rpx; display: inline-block; border-radius: 4rpx; object-fit: cover;',\n                  'data-emoji': emojiPhrase,\n                  'data-url': emoji.url\n            }\n          });\n        } else {\n            // 如果没找到对应表情，保留原文本\n          nodes.push({\n            type: 'text',\n              text: emojiPhrase\n          });\n        }\n        \n        lastIndex = regex.lastIndex;\n      }\n      \n      // 添加剩余的文本\n      if (lastIndex < text.length) {\n          const remainingText = text.substring(lastIndex);\n          if (remainingText) {\n        nodes.push({\n          type: 'text',\n              text: remainingText\n            });\n          }\n        }\n        \n        // 缓存解析结果，控制缓存大小\n        this.cacheEmojiParseResult(cacheKey, nodes);\n      \n      return nodes;\n      } catch (error) {\n        console.error('解析表情内容出错:', error);\n        // 出错时返回纯文本\n        return [{\n          type: 'text',\n          text: text\n        }];\n      }\n    },\n    \n    // 缓存表情解析结果\n    cacheEmojiParseResult(cacheKey, nodes) {\n      if (this.parsedContentCache.size >= this.maxCacheSize) {\n        // 删除最旧的缓存项\n        const firstKey = this.parsedContentCache.keys().next().value;\n        this.parsedContentCache.delete(firstKey);\n      }\n      this.parsedContentCache.set(cacheKey, nodes);\n    },\n\n    // 优化后的表情内容解析（用于rich-text组件）\n    parseEmojiContentForRichText(text) {\n      if (!text || typeof text !== 'string') return text;\n      \n      // 检查缓存\n      const cacheKey = `richtext_${text}`;\n      if (this.parsedContentCache.has(cacheKey)) {\n        return this.parsedContentCache.get(cacheKey);\n      }\n      \n      try {\n                 // 直接替换文本中的表情标记为HTML图片标签\n         let processedText = text.replace(/\\[([^\\[\\]]{1,10})\\]/g, (match) => {\n           const emoji = this.emojiMap.get(match);\n           if (emoji && emoji.url) {\n             return `<img src=\"${emoji.url}\" class=\"emoji-img-inline\" style=\"width: 32rpx !important; height: 32rpx !important; max-width: 32rpx !important; max-height: 32rpx !important; min-width: 32rpx !important; min-height: 32rpx !important; vertical-align: middle; margin: 0 4rpx; display: inline-block; border-radius: 4rpx; object-fit: cover;\" data-emoji=\"${match}\" />`;\n           }\n           return match;\n         });\n        \n        // 缓存结果\n        if (this.parsedContentCache.size >= this.maxCacheSize) {\n          const firstKey = this.parsedContentCache.keys().next().value;\n          this.parsedContentCache.delete(firstKey);\n        }\n        this.parsedContentCache.set(cacheKey, processedText);\n        \n        return processedText;\n      } catch (error) {\n        console.error('解析表情内容出错:', error);\n        return text;\n      }\n    },\n\n    // 表情点击预览（可选功能）\n    onEmojiClick(event) {\n      // 防抖处理\n      if (this.emojiClickTimer) {\n        clearTimeout(this.emojiClickTimer);\n      }\n      \n      this.emojiClickTimer = setTimeout(() => {\n        const target = event.target || event.currentTarget;\n        const emojiPhrase = target.getAttribute('data-emoji');\n        const emojiUrl = target.getAttribute('data-url');\n        \n        if (emojiPhrase && emojiUrl) {\n          // 显示表情预览或复制功能\n          this.showEmojiPreview(emojiPhrase, emojiUrl);\n        }\n      }, 300);\n    },\n\n    // 显示表情预览\n    showEmojiPreview(phrase, url) {\n      this.previewEmojiData = {\n        phrase,\n        url,\n        timestamp: Date.now()\n      };\n      \n      // 可以实现长按复制表情等功能\n      uni.showActionSheet({\n        itemList: ['复制表情', '查看大图'],\n        success: (res) => {\n          if (res.tapIndex === 0) {\n            // 复制表情文字\n            uni.setClipboardData({\n              data: phrase,\n              success: () => {\n                uni.showToast({\n                  title: '表情已复制',\n                  icon: 'success'\n                });\n              }\n            });\n          } else if (res.tapIndex === 1) {\n            // 预览表情大图\n            uni.previewImage({\n              urls: [url],\n              current: url\n            });\n          }\n        }\n      });\n    },\n\n    // 清理表情缓存\n    clearEmojiCache() {\n      this.parsedContentCache.clear();\n      console.log('表情缓存已清理');\n    },\n\n    // 获取表情缓存统计\n    getEmojiCacheStats() {\n      return {\n        emojiMapSize: this.emojiMap.size,\n        parsedContentCacheSize: this.parsedContentCache.size,\n        maxCacheSize: this.maxCacheSize\n      };\n    },\n\n    // 强制应用表情样式（运行时修复）\n    forceApplyEmojiStyles() {\n      this.$nextTick(() => {\n        try {\n          // 获取所有表情图片元素\n          const emojiImages = uni.createSelectorQuery().in(this)\n            .selectAll('image[data-emoji], img[data-emoji]')\n            .exec((res) => {\n              if (res && res[0]) {\n                res[0].forEach((node, index) => {\n                  // 通过选择器强制设置样式\n                  uni.createSelectorQuery().in(this)\n                    .select(`image[data-emoji]:nth-child(${index + 1}), img[data-emoji]:nth-child(${index + 1})`)\n                    .fields({\n                      node: true,\n                      size: true\n                    })\n                    .exec((nodeRes) => {\n                      if (nodeRes && nodeRes[0] && nodeRes[0].node) {\n                        const node = nodeRes[0].node;\n                        // 强制设置样式\n                        node.style.width = '32rpx';\n                        node.style.height = '32rpx';\n                        node.style.maxWidth = '32rpx';\n                        node.style.maxHeight = '32rpx';\n                        node.style.minWidth = '32rpx';\n                        node.style.minHeight = '32rpx';\n                        node.style.objectFit = 'cover';\n                      }\n                    });\n                });\n              }\n            });\n        } catch (error) {\n          console.warn('强制应用表情样式失败:', error);\n        }\n      });\n    },\n\n    // 聚焦评论框\n    focusClick(e) {\n      console.log(\"评论框获得焦点\", e.detail);\n      \n      // 记录键盘高度\n      if (e.detail && e.detail.height !== undefined) {\n        this.keyboardHeight = e.detail.height;\n      }\n    },\n    \n    // 返回上一页\n    navBack() {\n      if (getCurrentPages().length > 1) {\n        uni.navigateBack()\n      } else {\n        uni.switchTab({\n          url: \"//pages/index/index\"\n        })\n      }\n    },\n    \n    // 处理音频URL\n    formatAudioUrl(url) {\n      if (!url) return '';\n      \n      // 如果URL不是以http开头，尝试添加协议\n      if (!url.startsWith('http')) {\n        // 如果以//开头，添加https:\n        if (url.startsWith('//')) {\n          return 'https:' + url;\n        }\n        // 如果以/开头，可能是相对路径，尝试添加完整域名\n        if (url.startsWith('/')) {\n          return 'https://yourdomain.com' + url; // 替换为实际的域名\n        }\n        // 其他情况，假设是相对路径\n        return 'https://yourdomain.com/' + url; // 替换为实际的域名\n      }\n      \n      return url;\n    },\n    \n    // 音频播放 - 只有音频动态时才执行\n    audioBgClick() {\n      // 确保是音频类型\n      if (!this.isAudioNote) {\n        console.log('非音频动态，不执行音频播放逻辑');\n        return;\n      }\n      \n      try {\n        // 处理音频播放状态\n        if (this.bgAudioStatus) {\n          // 当前正在播放，需要暂停\n          this.pauseAudio();\n        } else {\n          // 当前已暂停，需要播放\n          this.playAudio();\n        }\n      } catch (e) {\n        console.error('音频控制异常:', e);\n        this.handleAudioError();\n      }\n    },\n    \n    // 暂停音频\n    pauseAudio() {\n      if (!this.isAudioNote || !this.bgAudioManager) return;\n      \n      try {\n            this.bgAudioManager.pause();\n            this.bgAudioStatus = false;\n            console.log('音频已暂停');\n      } catch (e) {\n        console.error('暂停音频失败:', e);\n        this.handleAudioError();\n          }\n    },\n    \n    // 播放音频 - 只有音频动态时才执行\n    playAudio() {\n      // 确保是音频类型才执行播放逻辑\n      if (!this.isAudioNote) {\n        console.log('非音频动态，不执行音频播放逻辑');\n        return;\n      }\n      \n          // 检查音频URL是否存在\n          if (!this.noteInfo.audio) {\n            return this.opTipsPopup(\"音频资源不可用\");\n          }\n          \n      // 如果已有实例，尝试继续播放\n      if (this.bgAudioManager) {\n            try {\n              console.log('继续播放现有音频');\n              this.bgAudioManager.play();\n              this.bgAudioStatus = true;\n          return;\n            } catch (e) {\n              console.error('播放现有音频失败，重新创建:', e);\n              // 播放失败则重新创建\n          this.createAudioInstance();\n        }\n      } else {\n        // 创建新的音频实例\n        this.createAudioInstance();\n      }\n    },\n    \n    // 创建音频实例 - 只有音频动态时才执行\n    createAudioInstance() {\n      if (!this.isAudioNote) return;\n      \n      try {\n        // 显示加载中提示\n        uni.showToast({\n          title: '加载音频中...',\n          icon: 'loading',\n          mask: true\n        });\n        \n        // 根据平台使用不同的音频播放方式\n        // #ifdef APP-PLUS || MP\n        // 获取全局唯一的背景音频管理器（仅小程序和APP支持）\n        this.bgAudioManager = uni.getBackgroundAudioManager();\n        \n        // 设置音频属性（这些属性是backgroundAudioManager必须设置的）\n        this.bgAudioManager.title = this.noteInfo.audio_title || '音频';\n        this.bgAudioManager.singer = this.noteInfo.user_info.nickname || '未知作者';\n        this.bgAudioManager.coverImgUrl = this.noteInfo.audio_cover || '/static/img/audio_default_cover.png';\n        \n        // 可选属性\n        this.bgAudioManager.epname = '笔记音频';\n        // #endif\n        \n        // #ifdef H5\n        // H5平台使用普通音频上下文\n        this.bgAudioManager = uni.createInnerAudioContext();\n        this.bgAudioManager.autoplay = true;\n        // #endif\n        \n        // 记录当前播放的音频ID，用于对比检查\n        this.audioPlayingId = this.noteInfo.id + '_' + Date.now();\n        const currentAudioId = this.audioPlayingId;\n        \n        // 设置事件监听\n        this.setupAudioListeners(currentAudioId);\n        \n        // 设置音频源必须放在最后，因为设置src后会自动开始播放\n        const audioUrl = this.formatAudioUrl(this.noteInfo.audio);\n        this.bgAudioManager.src = audioUrl;\n        \n      } catch (e) {\n        console.error('创建音频实例异常:', e);\n        this.handleAudioError();\n      }\n    },\n    \n    // 设置音频事件监听 - 只有音频动态时才执行\n    setupAudioListeners(currentAudioId) {\n      if (!this.isAudioNote || !this.bgAudioManager) return;\n      \n      try {\n        this.bgAudioManager.onPlay(() => {\n          // 检查是否仍是当前音频\n          if (this.audioPlayingId !== currentAudioId) return;\n          \n          uni.hideToast();\n          this.bgAudioStatus = true;\n          console.log('音频开始播放');\n        });\n        \n        this.bgAudioManager.onError((err) => {\n          // 检查是否仍是当前音频\n          if (this.audioPlayingId !== currentAudioId) return;\n          \n          console.error('音频播放错误:', err);\n          this.handleAudioError(err);\n        });\n        \n        this.bgAudioManager.onEnded(() => {\n          if (this.audioPlayingId !== currentAudioId) return;\n          console.log('音频播放结束');\n          this.bgAudioStatus = false;\n        });\n        \n        this.bgAudioManager.onStop(() => {\n          if (this.audioPlayingId !== currentAudioId) return;\n          console.log('音频播放停止');\n          this.bgAudioStatus = false;\n        });\n        \n        this.bgAudioManager.onPause(() => {\n          if (this.audioPlayingId !== currentAudioId) return;\n          console.log('音频播放暂停');\n          this.bgAudioStatus = false;\n        });\n        \n        this.bgAudioManager.onWaiting(() => {\n          if (this.audioPlayingId !== currentAudioId) return;\n          console.log('音频加载中');\n        });\n        \n        this.bgAudioManager.onCanplay(() => {\n          if (this.audioPlayingId !== currentAudioId) return;\n          console.log('音频可以播放');\n          uni.hideToast();\n        });\n        \n      } catch (e) {\n        console.error('设置音频监听器失败:', e);\n        this.handleAudioError();\n      }\n    },\n    \n    // 处理音频错误\n    handleAudioError(err = null) {\n      if (!this.isAudioNote) return;\n      \n          uni.hideToast();\n          this.bgAudioStatus = false;\n      \n      // 根据错误码显示不同提示\n      let errorMsg = \"音频播放失败，请稍后重试\";\n      if (err && err.errCode) {\n        switch(err.errCode) {\n          case 10001: errorMsg = \"系统错误，请重启应用\"; break;\n          case 10002: errorMsg = \"网络错误，请检查网络连接\"; break;\n          case 10003: errorMsg = \"音频文件错误，请更换音频\"; break;\n          case 10004: errorMsg = \"音频格式不支持\"; break;\n          default: errorMsg = \"音频播放失败，错误码: \" + err.errCode;\n        }\n      }\n      this.opTipsPopup(errorMsg);\n      \n      // 重置音频相关状态\n      this.bgAudioManager = null;\n      this.audioPlayingId = '';\n    },\n    \n    // 检查音频状态 - 只有音频动态时才执行\n    checkAudioStatus() {\n      if (!this.isAudioNote || !this.bgAudioManager) return;\n      \n      try {\n        // 检查音频管理器状态并同步到页面状态\n        // 这里可以添加具体的状态检查逻辑\n        console.log('检查音频状态');\n      } catch (e) {\n        console.error('检查音频状态失败:', e);\n        }\n    },\n    \n    // 销毁音频实例 - 只有音频动态时才执行\n    destroyAudioInstance() {\n      if (!this.isAudioNote) {\n        console.log('非音频动态，不执行音频销毁逻辑');\n        return;\n      }\n      \n      console.log('销毁音频实例');\n      \n      if (this.bgAudioManager) {\n        try {\n          // 停止音频播放\n          if (this.bgAudioStatus) {\n            this.bgAudioManager.stop();\n          }\n          \n          // #ifdef H5\n          // H5平台需要销毁音频实例\n          if (typeof this.bgAudioManager.destroy === 'function') {\n            this.bgAudioManager.destroy();\n          }\n          // #endif\n          \n          // #ifdef MP-WEIXIN\n          // 微信小程序需要先取消监听事件再销毁\n          try {\n            if (this.bgAudioManager.offPlay) {\n              this.bgAudioManager.offPlay();\n              this.bgAudioManager.offPause();\n              this.bgAudioManager.offStop();\n              this.bgAudioManager.offEnded();\n              this.bgAudioManager.offTimeUpdate();\n              this.bgAudioManager.offWaiting();\n              this.bgAudioManager.offCanplay();\n              this.bgAudioManager.offError();\n            }\n          } catch (e) {\n            console.error('微信小程序取消音频事件监听失败:', e);\n          }\n          // #endif\n          \n          // 将引用置为null\n          this.bgAudioManager = null;\n          this.bgAudioStatus = false;\n          this.audioPlayingId = '';\n          \n          console.log('音频实例销毁完成');\n        } catch (e) {\n          console.error('处理音频实例销毁过程中出错:', e);\n          this.bgAudioManager = null;\n          this.bgAudioStatus = false;\n          this.audioPlayingId = '';\n        }\n      }\n    },\n    \n    // 输入框失焦处理\n    textareaBlur(e) {\n      // 标记当前坐标，用于判断是否点击了发送按钮\n      this.lastTouchX = e && e.detail && e.detail.x;\n      this.lastTouchY = e && e.detail && e.detail.y;\n      \n      // 延时关闭时间延长，确保有足够时间点击发送按钮\n      setTimeout(() => {\n        // 如果此时没有点击发送按钮，则关闭评论框\n        if (this.isComment && !this.isSubmittingComment) {\n          this.isComment = false;\n          this.isFocus = false;\n        }\n      }, 500); // 延长到500ms，给用户更多时间点击发送按钮\n    },\n    openFooterComment() {\n      if (!this.isUser) {\n        this.opTipsPopup(\"完善账号资料后即可评论！\")\n        setTimeout(() => {\n          uni.navigateTo({\n            url: \"/pages/center/means\"\n          })\n        }, 1000)\n        return\n      }\n      \n      // 清除回复目标信息\n      this.cCId = 0\n      this.cUId = 0\n      this.cIdx = -1\n      this.cI = -1\n      this.comtips = \"说点什么...\"\n      \n      // 重置状态变量\n      this.isSubmittingComment = false\n      \n      // 直接显示评论框，不设置焦点\n      this.isComment = true\n      console.log(\"底部评论框已显示\")\n      \n      // 不再主动设置焦点，避免微信小程序的focus问题\n      // 用户可以点击输入框手动获取焦点\n    },\n    // 统一处理评论点击\n    handleCommentClick(event, type = 0, uid = 0, cid = 0, name = \"\", idx = -1, i = -1) {\n      console.log('点击评论/回复', {type, uid, cid, name, idx, i});\n      \n      // 阻止事件冒泡\n      if (event && event.stopPropagation) {\n        event.stopPropagation();\n      }\n      \n      // 检查登录状态\n      if (!this.isUser) {\n        console.log('用户未完善资料，检查原因:', {\n          userId: this.userId,\n          hasToken: !!this.$store.state.app.token,\n          hasPhone: this.$store.state.app.userInfo ? !!this.$store.state.app.userInfo.phone : false\n        });\n        \n        this.opTipsPopup(\"完善账号资料后即可评论！\");\n        setTimeout(() => {\n          uni.navigateTo({\n            url: \"/pages/center/means\"\n          });\n        }, 1000);\n        return;\n      }\n      \n      // 防止重复点击\n      if (this.isOpeningComment) return;\n      this.isOpeningComment = true;\n      \n      // 设置数据\n      this.cIdx = idx;\n      this.cI = i;\n      this.cCId = cid;\n      this.cUId = uid;\n      \n      // 设置文字提示\n      if (type === 1 && name) {\n        this.comtips = \"回复：\" + name;\n      } else {\n        this.comtips = \"说点什么...\";\n      }\n      \n      // 清空内容\n      this.comtext = \"\";\n      \n      // 重置提交状态\n      this.isSubmittingComment = false;\n      \n      // 显示评论框\n      this.isComment = true;\n      console.log(\"评论框已显示\");\n      \n      // 延时重置状态，防止多次点击\n      setTimeout(() => {\n        this.isOpeningComment = false;\n      }, 500);\n    },\n    // 处理评论框失焦事件\n    handleBlur() {\n      console.log(\"评论框失焦\");\n      \n      // 不会自动关闭，需要用户手动关闭或发送评论\n    },\n    \n    // 预览评论图片\n    previewCommentImage(image) {\n      uni.previewImage({\n        urls: [image],\n        current: image\n      });\n    },\n    // 处理发送评论\n    handleSendComment(commentData) {\n      if (this.isSubmittingComment) return;\n      this.isSubmittingComment = true;\n      \n      // 获取评论内容和图片\n      const content = commentData.content;\n      const image = commentData.image;\n      \n      // 如果没有内容和图片，不提交\n      if (!content && !image) {\n        this.isSubmittingComment = false;\n        return this.opTipsPopup(\"表达你的态度再评论吧！\");\n      }\n      \n      // 显示加载状态\n      uni.showLoading({\n        title: '发布中',\n        mask: true\n      });\n      \n      // 先关闭评论框，避免延迟\n      this.isComment = false;\n      this.isFocus = false;\n      this.showEmoji = false;\n      \n      // 准备评论数据\n      const params = {\n        dynamic_id: this.noteInfo.id,\n        content: content,\n        pid: this.cCId || 0,\n        to_uid: this.cUId || 0\n      };\n      \n      // 如果有图片，添加到参数\n      if (image) {\n        params.image = image;\n      }\n      \n      // 提交评论\n      addComment(params)\n        .then(res => {\n          uni.hideLoading();\n          \n          if (res.status === 200) {\n            // 处理评论数据\n            const commentData = res.data || this.createDefaultCommentData(content, image);\n            \n            // 更新评论计数和列表\n            this.processCommentSuccess(commentData);\n            \n            // 显示成功提示\n            this.opTipsPopup('评论成功');\n            \n            // 如果评论列表为空，刷新页面\n            if (this.isEmpty) {\n              this.isEmpty = false;\n            }\n          } else {\n            this.opTipsPopup(res.msg || '评论失败');\n          }\n        })\n        .catch(err => {\n          uni.hideLoading();\n          this.opTipsPopup('评论失败，请重试');\n          console.error('评论请求异常', err);\n        })\n        .finally(() => {\n          // 重置状态\n          this.isSubmittingComment = false;\n          \n          // 重置评论目标信息\n          this.cCId = 0;\n          this.cUId = 0;\n          this.comtips = \"说点什么...\";\n          this.comtext = \"\";\n        });\n    },\n    \n    // 创建默认评论数据（当API返回为空时）\n    createDefaultCommentData(content, imageUrl) {\n      // 生成更安全的临时ID，避免冲突\n      const tempId = `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n      \n      // 获取用户地理位置信息（如果有的话）\n      const userProvince = this.noteInfo?.province || this.$store.state.app?.userInfo?.province || '';\n      \n      // 创建完整的评论数据结构\n      const commentData = {\n        id: tempId, // 使用更安全的临时ID\n        uid: this.userId,\n        nickname: this.userNickname || '用户',\n        avatar: this.userAvatar || '/static/img/avatar_default.png',\n        content: content || '',\n        image: imageUrl || '', // 评论图片URL\n        create_time: this.formatDate(new Date()),\n        likes: 0,\n        like_count: 0, // 兼容不同字段名\n        is_like: false,\n        status: 5, // 正常状态：5\n        province: userProvince,\n        delete_time: null, // 删除时间，用于标记是否被删除\n        replies: [], // 初始化回复数组\n        reply_count: 0, // 回复数量\n        has_more_replies: false, // 是否有更多回复\n        replyPage: 1, // 回复页码\n        loading_replies: false // 是否正在加载回复\n      };\n      \n      console.log('创建默认评论数据:', commentData);\n      return commentData;\n    },\n    \n    // 格式化日期为字符串\n    formatDate(date) {\n      const year = date.getFullYear();\n      const month = String(date.getMonth() + 1).padStart(2, '0');\n      const day = String(date.getDate()).padStart(2, '0');\n      const hours = String(date.getHours()).padStart(2, '0');\n      const minutes = String(date.getMinutes()).padStart(2, '0');\n      \n      return `${year}-${month}-${day} ${hours}:${minutes}`;\n    },\n    \n    // 处理评论成功\n    processCommentSuccess(commentData) {\n      console.log(\"处理评论成功\", {\n        commentData,\n        cIdx: this.cIdx,\n        cI: this.cI,\n        noteInfoComments: this.noteInfo.comments\n      });\n      \n      // 确保commentData存在\n      if (!commentData) {\n        console.error(\"评论数据为空，无法处理\");\n        return;\n      }\n      \n      // 增加评论总数\n      this.noteInfo.comments = (this.noteInfo.comments || 0) + 1;\n      \n      // 处理回复评论\n      if (this.cIdx >= 0) {\n        console.log(\"处理回复评论\", this.cIdx);\n        \n        if (!this.commentList[this.cIdx].replies) {\n          this.commentList[this.cIdx].replies = [];\n        }\n        if (this.commentList[this.cIdx].reply_count === undefined) {\n          this.commentList[this.cIdx].reply_count = 0;\n        }\n        \n        // 设置回复目标信息\n        if (this.cUId) {\n          // 获取被回复用户的昵称\n          const nickname = this.comtips.replace(\"回复：\", \"\");\n          commentData.reply_uid = this.cUId;\n          commentData.reply_nickname = nickname;\n        }\n        \n        // 添加回复\n        this.commentList[this.cIdx].replies.push(commentData);\n        this.commentList[this.cIdx].reply_count++;\n        \n        // 更新回复索引映射\n        this.replyIndices.set(commentData.id, this.commentList[this.cIdx].replies.length - 1);\n        \n        // 强制更新视图\n        this.$forceUpdate();\n        \n        console.log(\"回复评论处理完成\", {\n          回复数量: this.commentList[this.cIdx].reply_count,\n          回复列表: this.commentList[this.cIdx].replies.length\n        });\n      }\n      // 处理新评论\n      else {\n        console.log(\"处理新评论\");\n        \n        if (this.isEmpty || this.commentList.length === 0) {\n          this.isEmpty = false;\n          this.commentList = [];\n          console.log(\"重置评论列表\");\n        }\n        \n        // 初始化回复数据\n        commentData.replies = [];\n        commentData.reply_count = 0;\n        \n        // 添加到列表头部\n        this.commentList.unshift(commentData);\n        \n        // 强制更新视图\n        this.$forceUpdate();\n        \n        console.log(\"新评论处理完成\", {\n          评论列表长度: this.commentList.length,\n          评论总数: this.noteInfo.comments\n        });\n      }\n    },\n    // 优化页面滚动事件处理\n    handlePageScroll(e) {\n      // 保存滚动位置以优化性能\n      if (!e || !this.isPageActive) return; // 确保有事件对象且页面活跃\n      \n      const scrollTop = e.scrollTop;\n      const direction = scrollTop > this.lastScrollTop ? 'down' : 'up';\n      this.lastScrollTop = scrollTop;\n      \n      // 只在滚动方向为向下且不在加载状态时预加载评论\n      if (direction === 'down' && !this.actionInProgress && \n          scrollTop > 300 && this.loadStatus === 'more') {\n        this.preloadComments();\n      }\n    },\n    \n    // 预加载评论 - 性能优化\n    preloadComments() {\n      // 使用防抖减少请求频率\n      if (this.debounceTimer) clearTimeout(this.debounceTimer);\n      \n      this.debounceTimer = setTimeout(() => {\n        // 判断是否需要加载更多\n        if (this.page > 1 && !this.commentCache[this.page + 1]) {\n          // 预加载下一页评论但不显示\n          this.fetchCommentsForPage(this.page + 1, true);\n        }\n      }, 300);\n    },\n    // 统一错误处理方法\n    handleError(error, defaultMessage = '操作失败') {\n      console.error('错误处理:', error);\n\n      let message = defaultMessage;\n\n      // 如果是字符串，直接使用\n      if (typeof error === 'string') {\n        message = error;\n      }\n      // 如果是对象，尝试获取错误信息\n      else if (error && typeof error === 'object') {\n        message = error.msg || error.message || error.data?.msg || defaultMessage;\n      }\n\n      this.opTipsPopup(message);\n      return message;\n    },\n\n    // 显示提示\n    opTipsPopup(msg, back = false) {\n      let self = this\n\n      console.log(\"显示提示\", msg, back);\n\n      self.tipsTitle = msg\n      self.$refs.tipsPopup.open()\n\n      setTimeout(() => {\n        self.$refs.tipsPopup.close()\n        if (back) {\n          self.navBack()\n        }\n      }, 2000)\n    },\n    \n    // 获取回复索引 - 使用Map提高效率\n    getReplyIndex(replies, replyId) {\n      // 优先使用Map快速查找\n      if (this.replyIndices.has(replyId)) {\n        return this.replyIndices.get(replyId);\n      }\n      \n      // 兜底使用遍历方式\n      for (let i = 0; i < replies.length; i++) {\n        if (replies[i].id === replyId) {\n          // 更新Map以便下次快速查找\n          this.replyIndices.set(replyId, i);\n          return i;\n        }\n      }\n      return -1;\n    },\n    \n    // 排序回复 - 改进排序算法，处理可能的日期格式问题和嵌套回复\n    sortRepliesByTime(replies) {\n      if (!replies || !replies.length) return [];\n      \n      // 避免修改原数组\n      const sorted = [...replies].sort((a, b) => {\n        // 根据回复层级对回复进行分组排序\n        // 先判断是否为嵌套回复\n        if (a.is_nested && !b.is_nested) return 1; // 非嵌套回复排在前面\n        if (!a.is_nested && b.is_nested) return -1; // 非嵌套回复排在前面\n        \n        // 如果是同一类型的回复，再按时间排序\n        \n        // 尝试转换日期字符串为时间戳\n        let timeA, timeB;\n        \n        try {\n          // 后端改为datetime格式，不需要替换-为/\n          timeA = new Date(a.create_time).getTime();\n        } catch(e) {\n          timeA = 0;\n        }\n        \n        try {\n          // 后端改为datetime格式，不需要替换-为/\n          timeB = new Date(b.create_time).getTime();\n        } catch(e) {\n          timeB = 0;\n        }\n        \n        // 如果转换成功则比较时间戳，否则按原始字符串比较\n        if (!isNaN(timeA) && !isNaN(timeB)) {\n          return timeA - timeB;\n        } else {\n          // 兜底比较方案\n          return (a.create_time || '').localeCompare(b.create_time || '');\n        }\n      });\n      \n      return sorted;\n    },\n    \n    // 销毁音频实例\n    destroyAudioInstance() {\n      console.log('销毁音频实例');\n      if (this.bgAudioManager) {\n        try {\n          // 停止音频播放\n          if (this.bgAudioStatus) {\n            this.bgAudioManager.stop();\n          }\n          \n          // #ifdef H5\n          // H5平台需要销毁音频实例\n          if (typeof this.bgAudioManager.destroy === 'function') {\n            this.bgAudioManager.destroy();\n          }\n          // #endif\n          \n          // 将引用置为null\n          this.bgAudioManager = null;\n          this.bgAudioStatus = false;\n        } catch (e) {\n          console.error('处理音频实例销毁过程中出错:', e);\n          this.bgAudioManager = null;\n          this.bgAudioStatus = false;\n        }\n      }\n    },\n    // 获取图片路径\n    getImageSrc(item) {\n      // 处理不同格式的图片数据\n      let url = '';\n      \n      // 如果传入了字符串，直接使用\n      if (typeof item === 'string') {\n        url = item;\n        console.log('图片URL是字符串:', url);\n      } \n      // 如果传入对象，尝试获取url属性\n      else if (item && typeof item === 'object') {\n        // 尝试多种可能的属性名\n        url = item.url || item.path || item.src || item.image || '';\n        console.log('图片URL从对象中提取:', url, '原对象:', item);\n      }\n      \n      // URL格式优化处理\n      if (url) {\n        // 处理缺少协议的URL (如 //example.com/image.jpg)\n        if (url.startsWith('//')) {\n          console.log('处理无协议URL:', url);\n          url = 'https:' + url;\n        }\n        \n        // 处理相对路径 (确保以/开头的相对路径)\n        if (!url.startsWith('http') && !url.startsWith('data:')) {\n          // 确保静态资源路径正确\n          if (url.startsWith('/static')) {\n            console.log('使用静态资源路径:', url);\n            return url;\n          }\n          \n          // 添加前缀斜杠\n          if (!url.startsWith('/')) {\n            console.log('添加前缀斜杠:', url);\n            url = '/' + url;\n          }\n        }\n      }\n      \n      // 提供默认图片作为备选\n      const finalUrl = url || '/static/img/default_img.png';\n      console.log('最终图片URL:', finalUrl);\n      return finalUrl;\n    },\n    // 获取分享图片URL\n    getShareImageUrl() {\n      if (this.noteInfo.type == 2) {\n        if (this.noteInfo.images && this.noteInfo.images.length > 0) {\n          const firstImage = this.noteInfo.images[0];\n          if (typeof firstImage === 'string') {\n            return firstImage;\n          } else if (firstImage.url) {\n            return firstImage.url;\n          }\n        }\n        return '';\n      } else if (this.noteInfo.type == 3) {\n        return this.noteInfo.video_cover || '';\n      } else if (this.noteInfo.type == 4) {\n        return this.noteInfo.audio_cover || '';\n      }\n      return '';\n    },\n    // 显示表情面板\n    toggleEmoji() {\n      this.isFocus = false;\n      setTimeout(() => {\n        this.showEmoji = !this.showEmoji;\n        // 加载最近使用的表情\n        if(this.showEmoji) {\n          try {\n            const recentEmojiStr = uni.getStorageSync('recent_emojis');\n            if(recentEmojiStr) {\n              this.recentEmojis = JSON.parse(recentEmojiStr);\n            }\n          } catch(e) {\n            console.error('读取最近使用表情失败', e);\n          }\n        }\n      }, 100);\n    },\n    // 选择表情\n    selectEmoji(emoji) {\n      // 将表情添加到文本内容中\n      this.comtext += emoji.alt;\n      \n      // 添加到最近使用的表情\n      if (!this.recentEmojis.find(item => item.url === emoji.url)) {\n        this.recentEmojis.unshift(emoji);\n        if (this.recentEmojis.length > 20) {\n          this.recentEmojis.pop();\n        }\n      }\n      \n      // 保存到本地存储\n      try {\n        uni.setStorageSync('recent_emojis', JSON.stringify(this.recentEmojis));\n      } catch (e) {\n        console.error('保存表情记录失败', e);\n      }\n    },\n    // 选择GIF表情\n    selectGif(gif) {\n      // 将选择的GIF表情插入到评论中\n      console.log('选择了GIF表情', gif);\n      // TODO: 实现GIF表情功能\n      uni.showToast({\n        title: 'GIF表情功能开发中',\n        icon: 'none'\n      });\n    },\n          // 处理评论提交\n    handleCommentSubmit(commentData) {\n      if (this.isSubmittingComment) return;\n      this.isSubmittingComment = true;\n      \n      // 获取评论内容和图片\n      const content = commentData.content.trim();\n      const image = commentData.image;\n      \n      // 如果没有内容和图片，不提交\n      if (!content && !image) {\n        this.isSubmittingComment = false;\n        return;\n      }\n      \n      // 准备评论数据\n      const commentParams = {\n        type: 0,                 // 评论类型：0-动态\n        target_id: this.noteInfo.id,\n        content: content,        // 保留原始表情格式如[微笑]\n        reply_id: this.cCId || 0, // 父评论ID（后端会自动处理parent_id）\n        image: image || ''        // 图片字段\n      };\n      \n      console.log('提交评论参数:', commentParams);\n      \n      // 提交评论\n      addComment(commentParams)\n        .then(res => {\n          console.log('评论提交响应:', res);\n          \n          try {\n            if (res.status === 200) {\n              // 更新评论计数\n              this.noteInfo.comments = (this.noteInfo.comments || 0) + 1;\n              \n              // 优先使用服务器返回的评论数据，如果没有则创建默认数据\n              let commentData = res.data || this.createDefaultCommentData(content, image);\n              \n              console.log('使用的评论数据:', commentData);\n              \n              // 如果是回复评论\n              if (this.cCId) {\n                // 查找父评论\n                const parentComment = this.commentList.find(item => item.id === this.cCId);\n                if (parentComment) {\n                  // 确保replies数组存在\n                  if (!parentComment.replies) {\n                    parentComment.replies = [];\n                  }\n                  \n                  // 添加回复到父评论的回复列表\n                  parentComment.replies.unshift({\n                    id: commentData.id,\n                    pid: this.cCId,\n                    uid: this.userId,\n                    nickname: this.userNickname,\n                    avatar: this.userAvatar,\n                    content: content,\n                    image: image, // 修改为image字段，与后端接口一致\n                    create_time: '刚刚',\n                    province: this.noteInfo.province || '',\n                    likes: 0,\n                    is_like: false,\n                    status: 5,\n                    reply_uid: this.cUId,\n                    reply_content: this.commentSource && this.commentSource.nickname ? this.commentSource.nickname + ': ' + (this.commentSource.content || '') : ''\n                  });\n                  \n                  // 更新回复计数\n                  parentComment.reply_count = (parentComment.reply_count || 0) + 1;\n                }\n              } else {\n                // 添加新评论到列表顶部\n                this.commentList.unshift({\n                  id: commentData.id,\n                  uid: this.userId,\n                  nickname: this.userNickname,\n                  avatar: this.userAvatar,\n                  content: content,\n                  image: image, // 修改为image字段，与后端接口一致\n                  create_time: '刚刚',\n                  province: this.noteInfo.province || '',\n                  likes: 0,\n                  is_like: false,\n                  status: 5,\n                  replies: [],\n                  reply_count: 0\n                });\n              }\n              \n              // 关闭评论框\n              this.closeComment();\n              \n              // 显示成功提示\n              this.opTipsPopup('评论成功');\n              \n              // 如果评论列表为空，刷新页面\n              if (this.isEmpty) {\n                this.isEmpty = false;\n              }\n            } else {\n              // 评论失败，显示服务器返回的错误信息\n              this.opTipsPopup(res.msg || '评论失败，请重试');\n            }\n          } catch (error) {\n            console.error('处理评论响应时出错:', error);\n            this.opTipsPopup('评论处理失败，请重试');\n          }\n        })\n        .catch(err => {\n          // 评论失败\n          console.error('评论提交失败:', err);\n          this.handleError(err, '评论失败，请重试');\n        })\n        .finally(() => {\n          this.isSubmittingComment = false;\n        });\n    },\n    \n    // 关闭评论输入框\n    closeComment() {\n      // 如果正在提交评论，不关闭\n      if (this.isSubmittingComment) {\n        return;\n      }\n      \n      // 清除blur定时器\n      if (this.commentBlurTimer) {\n        clearTimeout(this.commentBlurTimer);\n        this.commentBlurTimer = null;\n      }\n      \n      // 关闭评论框和焦点\n      this.isComment = false;\n      this.isFocus = false;\n      \n      // 重置评论相关状态\n      this.comtext = \"\";\n      this.commentImage = null;\n    },\n    // 关闭@用户选择弹窗\n    closeAtUsers() {\n      this.$refs.atUserPopup.close();\n    },\n    \n    // 这些方法已移至评论输入组件中\n    \n      // 处理表情解析\n  parseEmojiContent(content) {\n    if (!content) return '';\n    \n    try {\n      // 替换所有表情标记为HTML图片标签\n      let replacedStr = content.replace(/\\[([^(\\]|\\[)]*)\\]/g, (item, index) => {\n        // 查找对应的表情\n        const emoji = sinaEmoji.find(e => e.phrase === item);\n        \n        if (emoji) {\n          // 使用表情的url属性\n          return `<img src=\"${emoji.url}\" style=\"width: 28rpx; height: 28rpx; vertical-align: middle; display:inline-block;\">`;\n        }\n        \n        // 如果没有找到对应表情,返回原字符串\n        return item;\n      });\n      \n      // 将文本包装在span中，确保文本和表情在同一行显示\n      return '<span style=\"display:inline;\">' + replacedStr + '</span>';\n    } catch (error) {\n      console.error('解析表情出错:', error);\n      // 发生错误时返回原内容，确保至少显示文本\n      return '<span style=\"display:inline;\">' + content + '</span>';\n    }\n  },\n    \n    // 解析评论中的表情\n    parseCommentEmoji(comment) {\n      if(!comment || !comment.content) return comment;\n      \n      // 创建一个新的对象避免直接修改原对象\n      let newComment = JSON.parse(JSON.stringify(comment));\n      \n      // 直接使用parseEmojiContent处理内容\n      newComment.parsedContent = this.parseEmojiContent(newComment.content);\n      \n      return newComment;\n    },\n    \n  },\n  // 触底加载更多\n  onReachBottom() {\n    // 检查是否可以加载更多\n    if (!this.isEmpty && \n        this.commentList.length > 0 && \n        this.loadStatus === \"more\" && \n        this.isThrottling && \n        !this.actionInProgress) {\n      \n      this.page = this.page + 1\n      this.loadStatus = \"loading\"\n      this.getCommentList()\n    }\n  },\n  // 分享设置\n  onShareAppMessage: function() {\n    return {\n      title: this.noteInfo.content,\n      imageUrl: this.getShareImageUrl(),\n      path: \"/pages/note/details?id=\" + this.noteInfo.id\n    }\n  },\n  // 分享到朋友圈\n  onShareTimeline() {\n    return {\n      title: this.noteInfo.content,\n      imageUrl: this.getShareImageUrl(),\n      query: \"id=\" + this.noteInfo.id\n    }\n  },\n  // 微信小程序页面卸载\n  onUnload() {\n    console.log('页面卸载，释放资源');\n    this.cleanupResources();\n  },\n  \n  // 微信小程序页面隐藏\n  onHide() {\n    console.log('页面隐藏');\n    \n    // 标记页面为非活跃状态\n    this.isPageActive = false;\n    \n    // 只有音频动态才处理音频暂停\n    if (this.isAudioNote && this.bgAudioManager && this.bgAudioStatus) {\n      this.pauseAudio();\n    }\n  },\n  \n\n\n}\n</script>\n\n<style>\n/* 全局表情样式重置 - 确保尺寸一致 */\npage image[data-emoji],\npage img[data-emoji],\n.container image[data-emoji],\n.container img[data-emoji] {\n  width: 32rpx !important;\n  height: 32rpx !important;\n  max-width: 32rpx !important;\n  max-height: 32rpx !important;\n  min-width: 32rpx !important;\n  min-height: 32rpx !important;\n  object-fit: cover !important;\n  display: inline-block !important;\n  vertical-align: middle !important;\n  margin: 0 4rpx !important;\n  border-radius: 4rpx !important;\n}\n\n.nav-box{\n  position: fixed;\n  z-index: 99;\n  top: 0;\n  left: 0;\n  width: 100%;\n  background: #fff;\n  box-sizing: border-box;\n}\n.nav-item .nav-back{\n  padding: 0 30rpx;\n  width: 34rpx;\n  height: 100%;\n  border-radius: 50%;\n}\n.nav-item{\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n.nav-user .nav-user-avatar{\n  margin-right: 15rpx;\n  width: 60rpx;\n  height: 60rpx;\n  border-radius: 50%;\n  background: #f8f8f8;\n  border: 1px solid #f5f5f5;\n}\n.nav-user .nav-user-name{\n  max-width: 150rpx;\n  font-size: 26rpx;\n  font-weight: 700;\n}\n.nav-user .nav-user-adds image{\n  width: 18rpx;\n  height: 18rpx;\n}\n.nav-user .nav-user-adds text{\n  margin-left: 4rpx;\n  color: #999;\n  font-size: 18rpx;\n  font-weight: 500;\n}\n.nav-user{\n  flex: 1;\n  justify-content: flex-start;\n  padding-right: 10rpx;\n}\n.nav-user .user-info{\n  display: flex;\n  align-items: center;\n}\n.nav-user .follow-btn{\n  margin-left: 20rpx;\n  padding: 0 20rpx;\n  height: 48rpx;\n  line-height: 48rpx;\n  border-radius: 48rpx;\n  font-size: 20rpx;\n  font-weight: 700;\n  color: #f8f8f8;\n  background: #555555;\n  text-align: center;\n  transition: all 0.3s;\n}\n.nav-user .follow-btn.active{\n  color: #999;\n  background: rgba(0, 0, 0, 0.08);\n}\n.avatar-info{\n  margin-left: 20rpx;\n}\n.content-box .swiper-box{\n  z-index: 1;\n  width: 100%;\n  transition: height .3s;\n  overflow: hidden;\n  background-color: #f8f8f8;\n}\n\n.swiper-box .swiper-item{\n  width: 100%;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  overflow: hidden;\n}\n\n/* 图片样式 */\n.swiper-image {\n  width: 100%;\n  height: 100%;\n  display: block;\n}\n.content-box .current{\n  position: absolute;\n  z-index: 1;\n  right: 15rpx;\n  padding: 0 15rpx;\n  height: 40rpx;\n  color: #fff;\n  font-size: 20rpx;\n  font-weight: 700;\n  background: rgba(0, 0, 0, .4);\n  border: 1px solid rgba(255, 255, 255, .16);\n  border-radius: 40rpx;\n}\n.content-box .indicator{\n  margin: -40rpx 30rpx 0;\n  width: calc(100% - 60rpx);\n  height: 40rpx;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  position: relative;\n  z-index: 10;\n}\n.indicator .indicator-item{\n  z-index: 1;\n  margin: 0 2.5px;\n  height: 3px;\n  border-radius: 3px;\n  background: rgba(255, 255, 255, .3);\n  transition: background-color 0.3s;\n}\n\n.indicator .indicator-item.active{\n  background: rgba(255, 255, 255, .9);\n}\n.content-box .video-box{\n  width: 100%;\n  height: 421.875rpx;\n}\n.content-box .audio-box{\n  width: calc(100% - 60rpx);\n  margin: 0 30rpx;\n  height: 180rpx;\n  color: #fff;\n  border-radius: 8rpx;\n  position: relative;\n  overflow: hidden;\n}\n.audio-box .audio-left{\n  margin-right: 30rpx;\n  width: 180rpx;\n  height: 180rpx;\n  position: relative;\n}\n.audio-box .audio-left .icon{\n  position: absolute;\n  top: 65rpx;\n  right: 65rpx;\n  bottom: 65rpx;\n  left: 65rpx;\n  width: 50rpx;\n  height: 50rpx;\n}\n.audio-box .audio-bg{\n  position: absolute;\n  top: 0;\n  left: 0;\n  z-index: -2;\n  width: 100%;\n  height: 100%;\n}\n.audio-box .audio-mb{\n  position: absolute;\n  top: 0;\n  left: 0;\n  z-index: -1;\n  width: 100%;\n  height: 100%;\n  -webkit-backdrop-filter: saturate(150%) blur(25px);\n  backdrop-filter: saturate(150%) blur(25px);\n  background: rgba(0, 0, 0, .5);\n}\n.audio-box .audio-t1{\n  font-size: 32rpx;\n  font-weight: 700;\n}\n.audio-box .audio-t2{\n  margin-top: 12rpx;\n  opacity: .8;\n  font-size: 24rpx;\n}\n.audio-box .audio-play{\n  margin: 0 30rpx;\n  width: 68rpx;\n  height: 68rpx;\n  background: rgba(255, 255, 255, .15);\n  border-radius: 50%;\n}\n.audio-box .audio-play .icon{\n  margin: 20rpx;\n  width: 28rpx;\n  height: 28rpx;\n}\n.content-box .info-box{\n  width: calc(100% - 60rpx);\n  padding: 30rpx;\n}\n.info-box .info-content{\n  width: 100%;\n  word-break: break-word;\n  white-space: pre-line;\n}\n.info-box .info-content text{\n  font-size: 28rpx;\n  font-weight: 400;\n}\n.info-box .info-tag{\n  margin-top: 20rpx;\n  width: 100%;\n  display: flex;\n  flex-wrap: wrap;\n}\n.info-tag .tag-item{\n  margin: 10rpx 10rpx 0 0;\n  padding: 8rpx;\n  height: 40rpx;\n  border-radius: 8rpx;\n  background: #f8f8f8;\n}\n.info-tag .tag-item .icon{\n  width: 40rpx;\n  height: 40rpx;\n}\n.info-tag .tag-item text{\n  font-size: 20rpx;\n  padding: 0 8rpx 0 12rpx;\n}\n.info-box .more{\n  padding: 30rpx 0;\n  width: 100%;\n  justify-content: space-between;\n  border-bottom: 1px solid #f8f8f8;\n  position: relative;\n}\n.info-box .more .more-left{\n  color: #999;\n  font-size: 20rpx;\n}\n.info-box .more .more-right{\n  position: absolute;\n  right: -30rpx;\n  padding: 20rpx 30rpx;\n  width: 24rpx;\n  height: 24rpx;\n}\n.comment-box{\n  width: calc(100% - 60rpx);\n  padding: 0 30rpx 120px;\n}\n.comment-box .comment-top{\n  width: 100%;\n  justify-content: space-between;\n}\n.comment-top .top-title{\n  font-size: 26rpx;\n  font-weight: 700;\n}\n.comment-top .top-btn{\n  width: 136rpx;\n  padding: 6rpx;\n  background: #f8f8f8;\n  border-radius: 8rpx;\n  position: relative;\n}\n.comment-top .top-btn .btn-item, .comment-top .top-btn .btn-active{\n  width: 68rpx;\n  height: 44rpx;\n}\n.comment-top .top-btn .btn-item{\n  z-index: 2;\n  line-height: 44rpx;\n  text-align: center;\n  font-size: 20rpx;\n  font-weight: 500;\n  transition: color .3s;\n}\n.comment-top .top-btn .btn-active{\n  position: absolute;\n  z-index: 1;\n  background: #fff;\n  border-radius: 4rpx;\n  transition: left .3s;\n}\n.comment-box .comment-item{\n  width: 100%;\n  margin-top: 30rpx;\n  justify-content: space-between;\n  align-items: flex-start !important;\n}\n.comment-item .comment-avatar, .comment-item .comment-avatar-z{\n  background: #f8f8f8;\n  border: 1px solid #f5f5f5;\n  border-radius: 50%;\n  overflow: hidden;\n}\n.comment-item .comment-avatar{\n  width: 64rpx;\n  height: 64rpx;\n}\n.comment-item .comment-avatar-z{\n  width: 44rpx;\n  height: 44rpx;\n}\n.comment-item .comment-info{\n  width: calc(100% - 88rpx);\n}\n.unfold{\n  padding: 20rpx 68rpx;\n  color: #4e6ef2;\n  font-size: 24rpx;\n  font-weight: 700;\n  transition: opacity 0.3s;\n}\n.unfold:active {\n  opacity: 0.7;\n}\n.comment-info .comment-info-top{\n  font-size: 24rpx;\n  color: #999;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  width: 100%;\n}\n\n.comment-info .user-info-left {\n  display: flex;\n  align-items: center;\n}\n\n.comment-info .like-icon {\n  display: flex;\n  align-items: center;\n  margin-left: auto;\n}\n\n.comment-info .like-icon image {\n  width: 32rpx;\n  height: 32rpx;\n  margin-right: 4rpx;\n}\n\n.comment-info .like-icon text {\n  font-size: 20rpx;\n  color: #999;\n}\n.comment-info .comment-info-top-z {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  width: 100%;\n}\n\n.comment-info .comment-info-top-z view{\n  max-width: 230rpx;\n  font-size: 22rpx;\n  color: #999;\n}\n.comment-info .comment-info-top-z text{\n  margin-right: 8rpx;\n  color: #333;\n  font-size: 22rpx;\n  font-weight: 500;\n}\n.comment-info .nn, .comment-info .zz, .comment-info .wo{\n  margin-right: 8rpx;\n}\n.comment-info .zz{\n  color: #FA5150 !important;\n  font-weight: 700;\n}\n.comment-info .wo{\n  color: #000 !important;\n  font-weight: 700;\n}\n.comment-info .db{\n  color: #ccc !important;\n}\n.comment-info .comment-info-content{\n  word-break: break-word;\n  white-space: pre-wrap;\n}\n.comment-info .comment-info-content text{\n  color: #333;\n  font-size: 26rpx;\n  font-weight: 400;\n  display: inline;\n}\n.comment-info .comment-info-bottom{\n  margin-top: 15rpx;\n  color: #999;\n  font-size: 20rpx;\n}\n.comment-info .comment-info-bottom view{\n  margin-left: 30rpx;\n  font-weight: 700;\n}\n\n/* 嵌套回复样式 */\n/* 嵌套回复样式已移除，使用扁平化结构 */\n\n.comment-box .no-more{\n  width: 100%;\n  height: 90rpx;\n  line-height: 90rpx;\n  text-align: center;\n  color: #999;\n  font-size: 20rpx;\n}\n.footer-box{\n  position: fixed;\n  z-index: 99;\n  left: 0;\n  bottom: 0;\n  width: calc(100% - 20rpx);\n  padding: 20rpx 10rpx;\n  background: #fff;\n  border-top: 1px solid #f8f8f8;\n  padding-bottom: max(env(safe-area-inset-bottom), 20rpx);\n}\n.footer-box .footer-item{\n  width: 100%;\n  height: 80rpx;\n  justify-content: space-between;\n}\n.footer-means{\n  margin-left: 20rpx;\n  padding: 0 30rpx;\n  height: 80rpx;\n  font-size: 20rpx;\n  font-weight: 700;\n  background: #f8f8f8;\n  border-radius: 40rpx;\n}\n.footer-means image{\n  margin-left: 10rpx;\n  width: 20rpx;\n  height: 20rpx;\n}\n.footer-comment{\n  margin-left: 20rpx;\n  width: 280rpx;\n  height: 80rpx;\n  line-height: 80rpx;\n  background: #f8f8f8;\n  border-radius: 40rpx;\n}\n.footer-comment image{\n  margin: 0 20rpx 0 10rpx;\n  width: 60rpx;\n  height: 60rpx;\n  border-radius: 50%;\n}\n.footer-comment view{\n  max-width: calc(100% - 120rpx);\n  color: #999;\n  font-size: 24rpx;\n  font-weight: 400;\n  word-break: break-word;\n  white-space: pre-line;\n  display: block;\n  display: -webkit-box;\n  overflow: hidden;\n  -webkit-line-clamp: 1;\n  text-overflow: ellipsis;\n  -webkit-box-orient: vertical;\n}\n.footer-item .footer-icon{\n  padding: 16rpx 20rpx;\n  display: flex;\n}\n.footer-item .footer-icon image{\n  width: 48rpx;\n  height: 48rpx;\n}\n.footer-item .footer-icon text{\n  margin-left: 10rpx;\n  color: #999;\n  font-size: 18rpx;\n  font-weight: 700;\n}\n.popup-comment-mask{\n  position: fixed;\n  z-index: 998;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  width: 100%;\n  height: 100vh;\n  background-color: rgba(0, 0, 0, 0.01); /* 几乎透明的背景，只为捕获点击事件 */\n}\n.popup-comment{\n  position: fixed;\n  z-index: 999;\n  left: 0;\n  bottom: 0;\n  width: 100%;\n  background: #fff;\n  border-top: 1px solid #f1f1f1;\n  display: flex;\n  flex-direction: column;\n}\n.popup-comment .send{\n  margin: 0 0 15rpx 30rpx;\n  width: 48rpx;\n  height: 48rpx;\n}\n.popup-comment .comment-textarea{\n  width: calc(100% - 98rpx);\n  padding: 10rpx 20rpx;\n  background: #f8f8f8;\n  border-radius: 30rpx;\n}\n.popup-comment .comment-textarea textarea{\n  width: 100%;\n  line-height: 32rpx;\n  min-height: 96rpx;\n  max-height: 320rpx;\n  font-size: 26rpx;\n}\n.popup-comment .comment-icon{\n  width: calc(100% - 20rpx);\n  padding: 30rpx 10rpx;\n}\n.share-popup{\n  background: #fff;\n  border-radius: 30rpx;\n  padding: 30rpx;\n  overflow: hidden;\n}\n.share-popup .share-img{\n  width: 473rpx;\n  height: 237.5rpx;\n  background: #f8f8f8;\n  border-radius: 8rpx;\n  display: block;\n}\n.share-popup .share-tips{\n  margin: 30rpx 0;\n  width: 473rpx;\n  font-size: 26rpx;\n  line-height: 48rpx;\n  position: relative;\n}\n.share-popup .share-tips image{\n  position: absolute;\n  top: 0;\n  width: 48rpx;\n  height: 48rpx;\n  margin: 0 15rpx;\n}\n.share-popup .share-btn{\n  width: 100%;\n  height: 80rpx;\n  line-height: 80rpx;\n  text-align: center;\n  font-size: 24rpx;\n  font-weight: 700;\n  color: #fff;\n  background: #000;\n  border-radius: 16rpx;\n}\n.more-popup{\n  width: 100%;\n  background: #fff;\n  border-radius: 30rpx 30rpx 0 0;\n  padding-bottom: env(safe-area-inset-bottom);\n  overflow: hidden;\n}\n.more-popup .more-tips{\n  width: calc(100% - 60rpx);\n  padding: 20rpx 30rpx;\n  font-size: 20rpx;\n  color: #999;\n  background: #f8f8f8;\n}\n.more-popup .more-item{\n  width: calc(100% - 60rpx);\n  padding: 30rpx;\n  font-size: 26rpx;\n  font-weight: 700;\n  justify-content: space-between;\n}\n.more-popup .more-item:first-child{\n  padding-top: 40rpx;\n}\n.more-popup .more-item:last-child{\n  padding-bottom: 40rpx;\n}\n.more-popup .more-item:hover{\n  background: #f8f8f8;\n}\n.more-popup .more-item image{\n  width: 36rpx;\n  height: 36rpx;\n}\n.empty-box{\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 100rpx 0;\n}\n.empty-box image{\n  width: 200rpx;\n  height: 200rpx;\n  margin-bottom: 30rpx;\n}\n.empty-box .e1{\n  font-size: 28rpx;\n  font-weight: 700;\n  margin-bottom: 10rpx;\n}\n.empty-box .e2{\n  font-size: 24rpx;\n  color: #999;\n}\n.tips-box{\n  justify-content: center;\n  width: 100%;\n}\n.tips-box .tips-item{\n  background: #000;\n  color: #fff;\n  padding: 20rpx 40rpx;\n  border-radius: 12rpx;\n  font-size: 24rpx;\n  font-weight: 700;\n}\n.df{\n  display: flex;\n  align-items: center;\n}\n.ohto{\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n.bUp{\n  transition: bottom .3s;\n}\n\n/* 点赞按钮样式 */\n.like-action {\n  margin-left: 24rpx;\n  font-size: 24rpx;\n  color: #999;\n}\n\n.like-text {\n  color: #999;\n}\n\n.like-text.liked {\n  color: #FA5150;\n}\n\n.like-icon text {\n  color: #999;\n}\n\n.like-icon image {\n  width: 32rpx;\n  height: 32rpx;\n}\n\n.loading-text {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: #999;\n  font-size: 20rpx;\n}\n\n.loading-icon {\n  width: 40rpx;\n  height: 40rpx;\n  margin-right: 10rpx;\n}\n\n.load-more-children {\n  padding: 10rpx 0 10rpx 60rpx;\n  font-size: 24rpx;\n  color: #4e6ef2;\n}\n\n\n\n.audio-error {\n  margin: 0 30rpx;\n  width: 68rpx;\n  height: 68rpx;\n  background: rgba(255, 255, 255, .15);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.audio-error text {\n  color: #ff4d4f;\n  font-size: 20rpx;\n  text-align: center;\n}\n\n.send-button-wrapper {\n  padding: 8rpx;\n  margin: 0 0 15rpx 30rpx;\n  width: 60rpx;\n  height: 60rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.send-button-wrapper {\n  padding: 8rpx;\n  margin: 0 0 15rpx 30rpx;\n  width: 60rpx;\n  height: 60rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: all 0.3s;\n}\n\n.send-button-wrapper.active {\n  background-color: #ff4081;\n  border-radius: 50%;\n}\n\n.send-button-wrapper .send {\n  width: 48rpx;\n  height: 48rpx;\n}\n\n\n\n\n/* 图片容器样式 */\n/* 删除多余样式，使用上面的样式统一处理 */\n\n/* 评论输入框样式已移至组件中 */\n\n/* 表情面板样式已移至组件中 */\n\n.at-user-popup {\n  position: fixed;\n  z-index: 999;\n  left: 0;\n  width: 100%;\n  background: #fff;\n  border-radius: 16rpx;\n  padding: 20rpx;\n}\n\n.popup-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 10rpx;\n}\n\n.close-btn {\n  width: 80rpx;\n  height: 48rpx;\n  line-height: 48rpx;\n  text-align: center;\n  color: #999;\n  font-size: 24rpx;\n  font-weight: 700;\n  border-radius: 48rpx;\n  border: 1px solid #f5f5f5;\n}\n\n.user-list {\n  margin-top: 10rpx;\n}\n\n.user-item {\n  display: flex;\n  align-items: center;\n  margin-bottom: 10rpx;\n}\n\n.user-avatar {\n  width: 60rpx;\n  height: 60rpx;\n  border-radius: 50%;\n  margin-right: 10rpx;\n}\n\n.user-name {\n  max-width: 200rpx;\n  font-size: 26rpx;\n  font-weight: 700;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.empty-users {\n  width: 100%;\n  height: 90rpx;\n  line-height: 90rpx;\n  text-align: center;\n  color: #999;\n  font-size: 20rpx;\n}\n\n/* 评论图片样式 */\n.comment-image {\n  margin-top: 10rpx;\n  max-width: 200rpx;\n  max-height: 200rpx;\n  border-radius: 8rpx;\n  transition: transform 0.2s ease;\n}\n\n.comment-image:active {\n  transform: scale(0.95);\n}\n\n.reply-comment-image {\n  max-width: 160rpx;\n  max-height: 160rpx;\n}\n\n/* 表情显示优化样式 */\n.comment-rich-text {\n  line-height: 1.6;\n  word-break: break-word;\n  white-space: pre-wrap;\n}\n\n.reply-rich-text {\n  font-size: 24rpx;\n}\n\n/* 表情图片样式 - 强制固定尺寸 */\n.emoji-img, .emoji-img-inline {\n  width: 32rpx !important;\n  height: 32rpx !important;\n  max-width: 32rpx !important;\n  max-height: 32rpx !important;\n  min-width: 32rpx !important;\n  min-height: 32rpx !important;\n  vertical-align: middle !important;\n  margin: 0 4rpx !important;\n  display: inline-block !important;\n  border-radius: 4rpx !important;\n  object-fit: cover !important;\n  transition: transform 0.1s ease;\n}\n\n.emoji-img:active, .emoji-img-inline:active {\n  transform: scale(1.1);\n}\n\n/* 回复中的表情稍小一些 */\n.reply-rich-text .emoji-img,\n.reply-rich-text .emoji-img-inline {\n  width: 28rpx !important;\n  height: 28rpx !important;\n  max-width: 28rpx !important;\n  max-height: 28rpx !important;\n  min-width: 28rpx !important;\n  min-height: 28rpx !important;\n  margin: 0 2rpx !important;\n}\n\n/* 删除的评论样式 */\n.deleted-comment {\n  color: #ccc !important;\n  font-style: italic;\n}\n\n/* 表情加载失败的占位样式 */\n.emoji-img-error {\n  width: 32rpx;\n  height: 32rpx;\n  background-color: #f5f5f5;\n  border-radius: 4rpx;\n  display: inline-block;\n  vertical-align: middle;\n  margin: 0 4rpx;\n  position: relative;\n}\n\n.emoji-img-error::after {\n  content: '😊';\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  font-size: 20rpx;\n  opacity: 0.5;\n}\n\n/* 评论内容整体优化 */\n.comment-info-content {\n  margin-top: 8rpx;\n  padding: 4rpx 0;\n  border-radius: 4rpx;\n  transition: background-color 0.2s ease;\n}\n\n.comment-info-content:active {\n  background-color: rgba(0, 0, 0, 0.02);\n}\n\n/* 强制表情尺寸一致 - 更高优先级选择器 */\n.comment-rich-text image[data-emoji],\n.comment-rich-text img[data-emoji],\n.reply-rich-text image[data-emoji],\n.reply-rich-text img[data-emoji] {\n  width: 32rpx !important;\n  height: 32rpx !important;\n  max-width: 32rpx !important;\n  max-height: 32rpx !important;\n  min-width: 32rpx !important;\n  min-height: 32rpx !important;\n  object-fit: cover !important;\n  display: inline-block !important;\n  vertical-align: middle !important;\n  margin: 0 4rpx !important;\n  border-radius: 4rpx !important;\n}\n\n/* 回复中的表情强制尺寸 */\n.reply-rich-text image[data-emoji],\n.reply-rich-text img[data-emoji] {\n  width: 28rpx !important;\n  height: 28rpx !important;\n  max-width: 28rpx !important;\n  max-height: 28rpx !important;\n  min-width: 28rpx !important;\n  min-height: 28rpx !important;\n  margin: 0 2rpx !important;\n}\n\n/* 通用表情图片强制样式 */\nrich-text image[data-emoji],\nrich-text img[data-emoji] {\n  width: 32rpx !important;\n  height: 32rpx !important;\n  max-width: 32rpx !important;\n  max-height: 32rpx !important;\n  min-width: 32rpx !important;\n  min-height: 32rpx !important;\n  object-fit: cover !important;\n  display: inline-block !important;\n  vertical-align: middle !important;\n}\n\n/* 响应式表情大小 */\n@media screen and (max-width: 750rpx) {\n  .emoji-img, .emoji-img-inline {\n    width: 28rpx !important;\n    height: 28rpx !important;\n    max-width: 28rpx !important;\n    max-height: 28rpx !important;\n    min-width: 28rpx !important;\n    min-height: 28rpx !important;\n  }\n  \n  .reply-rich-text .emoji-img,\n  .reply-rich-text .emoji-img-inline {\n    width: 24rpx !important;\n    height: 24rpx !important;\n    max-width: 24rpx !important;\n    max-height: 24rpx !important;\n    min-width: 24rpx !important;\n    min-height: 24rpx !important;\n  }\n}\n\n/* 表情预览弹窗样式 */\n.emoji-preview-popup {\n  position: fixed;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  background: rgba(0, 0, 0, 0.8);\n  border-radius: 12rpx;\n  padding: 20rpx;\n  z-index: 9999;\n  animation: fadeIn 0.2s ease;\n}\n\n.emoji-preview-image {\n  width: 120rpx;\n  height: 120rpx;\n  border-radius: 8rpx;\n}\n\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n    transform: translate(-50%, -50%) scale(0.8);\n  }\n  to {\n    opacity: 1;\n    transform: translate(-50%, -50%) scale(1);\n  }\n}\n\n.emoji-tabs {\n  display: flex;\n  justify-content: space-between;\n  padding: 10rpx 20rpx;\n  border-bottom: 1rpx solid #f1f1f1;\n}\n\n.emoji-tabs-inner {\n  display: flex;\n  justify-content: space-between;\n}\n\n.emoji-tab-item {\n  padding: 10rpx 20rpx;\n  font-size: 24rpx;\n  color: #999;\n}\n\n.emoji-tab-item.active {\n  border-bottom: 2rpx solid #ff4d6a;\n  color: #ff4d6a;\n}\n\n.emoji-content {\n  flex: 1;\n  padding: 20rpx;\n}\n\n.empty-emoji {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 100%;\n  color: #999;\n  font-size: 24rpx;\n}\n\n.emoji-tools {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 10rpx 20rpx;\n  border-top: 1rpx solid #f1f1f1;\n}\n\n.emoji-backspace {\n  width: 64rpx;\n  height: 64rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.emoji-backspace image {\n  width: 44rpx;\n  height: 44rpx;\n}\n\n.emoji-send {\n  width: 120rpx;\n  height: 64rpx;\n  line-height: 64rpx;\n  text-align: center;\n  background-color: #f5f5f5;\n  color: #ccc;\n  font-size: 28rpx;\n  border-radius: 32rpx;\n}\n\n.emoji-send.active {\n  background-color: #ff4d6a;\n  color: #fff;\n}\n\n.gif-search {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 64rpx;\n  border-bottom: 1rpx solid #f1f1f1;\n}\n\n.gif-search image {\n  width: 44rpx;\n  height: 44rpx;\n}\n\n.gif-item {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.gif-image {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n/* ==== 投票展示样式（复用add.vue） ==== */\n.vote-box {\n  width: 100%;\n  margin-top: 16rpx;\n}\n.vote-container {\n  width: 100%;\n  background-color: #f5f5f5;\n  border-radius: 16rpx;\n  position: relative;\n}\n.vote-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  position: relative;\n  padding: 20rpx;\n}\n.vote-title-container {\n  display: flex;\n  align-items: center;\n}\n.vote-icon {\n  width: 32rpx;\n  height: 32rpx;\n  margin-right: 10rpx;\n}\n.vote-title {\n  font-size: 28rpx;\n  font-weight: 500;\n  color: #333;\n  text-align: left;\n  white-space: normal;\n  word-break: break-word;\n}\n.vote-options {\n  display: flex;\n  flex-direction: column;\n  padding: 0 0;\n}\n.vote-option-voted {\n  background: #f5f5f5;\n  border-radius: 32rpx;\n  margin-bottom: 16rpx;\n  padding: 0;\n  border: none;\n  position: relative;\n  transition: background 0.2s, border 0.2s;\n}\n.vote-option-voted.selected {\n  background: #fff;\n}\n.vote-bar-bg {\n    border-radius: 18px;\n    font-size: 17px;\n    color: #333;\n    margin-bottom: 10px;\n    border: none;\n    box-shadow: none;\n    text-align: center;\n    transition: background 0.2s, border 0.2s;\n    margin-left: 11px;\n    margin-right: 11px;\n    min-height: 27px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    padding: 0 11px;\n    box-sizing: border-box;\n}\n.vote-option-voted.selected .vote-bar-bg {\n  background: #fff;\n}\n.vote-bar {\n  height: 100%;\n  border-radius: 32rpx;\n  transition: width 0.3s;\n  position: absolute;\n  left: 0;\n  top: 0;\n  z-index: 1;\n}\n.vote-row {\n  position: absolute;\n  left: 0;\n  top: 0;\n  width: 100%;\n  height: 100%;\n  z-index: 2;\n  pointer-events: none;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0 20rpx;\n}\n.vote-left {\n  display: flex;\n  align-items: center;\n}\n\n.vote-option-unvoted {\n  background: #fff;\n  border-radius: 32rpx;\n  font-size: 26rpx;\n  color: #333;\n  margin-bottom: 18rpx;\n  border: none;\n  box-shadow: none;\n  text-align: center;\n  transition: background 0.2s, border 0.2s;\n  margin-left: 20rpx;\n  margin-right: 20rpx;\n  position: relative;\n  min-height: 66rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 0 20rpx;\n  box-sizing: border-box;\n}\n\n.vote-progress-bar {\n  position: absolute;\n  left: 0;\n  bottom: 0;\n  height: 12rpx;\n  border-radius: 32rpx;\n  z-index: 1;\n  background: #ffd600;\n  transition: width 0.3s;\n}\n.vote-option-unvoted:not(.selected) .vote-progress-bar {\n  background: #eaeaea;\n}\n\n.vote-checked-icon {\n  width: 28rpx;\n  height: 28rpx;\n  background: #ffd600;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 10rpx;\n}\n.vote-checked-icon image {\n  width: 20rpx;\n  height: 20rpx;\n  display: block;\n}\n.vote-content {\n  font-size: 26rpx;\n  color: #333;\n  text-align: left;\n  white-space: normal;\n  word-break: break-word;\n  font-weight: 500;\n}\n.vote-option-voted.selected .vote-content {\n  color: #000;\n}\n.vote-percent {\n  font-size: 26rpx;\n  color: #000000;\n  text-align: right;\n  margin-left: 12rpx;\n  min-width: 48rpx;\n  flex-shrink: 0;\n  white-space: nowrap;\n  display: flex;\n  align-items: center;\n  justify-content: flex-end;\n  font-weight: 500;\n  margin-right: 40rpx;\n  position: relative;\n}\n.vote-people {\n  margin-top: 16rpx;\n  color: #999;\n  font-size: 24rpx;\n  text-align: left;\n  padding-left: 20rpx;\n  padding-bottom: 20rpx;\n}\n\n</style> ", "import MiniProgramPage from 'Z:/WWW/shejiao/vue3/pages/note/details.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "getDynamicDetail", "getCommentsList", "_a", "_b", "addComment", "deleteComment", "res", "likeComment", "deleteDynamic", "commentData", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;AA8cA,MAAA,YAAA,MAAA;AACA,MAAA,OAAA,MAAA;AACA,MAAA,cAAA,MAAA;AACA,MAAA,aAAA,MAAA;AACA,MAAA,eAAA,MAAA;AAiBA,MAAA,YAAA;AAAA,EACE,MAAA;AAAA;IAEE;AAAA;;;;;EAMF,OAAA;AAAA;AAAA;AAGI,UAAA,WAAA,QAAA;AAEE,aAAA,aAAA;AAAA,MACF;AAAA,IACF;AAAA;EAEF,OAAA;AACE,WAAA;AAAA;;MAGE,gBAAA;AAAA,MACA,QAAA;AAAA;;MAGA,cAAA;AAAA;;MAEA,aAAA;AAAA,MACA,eAAA;AAAA;;;;;MAIA,UAAA;AAAA;;QAGE,SAAA;AAAA,QACA,MAAA;AAAA;UAEE,UAAA;AAAA;;;QAGF,SAAA;AAAA;QAEA,OAAA;AAAA,QACA,OAAA;AAAA;QAEA,aAAA;AAAA,QACA,eAAA;AAAA,QACA,UAAA;AAAA,QACA,QAAA,CAAA;AAAA,QACA,OAAA;AAAA,QACA,aAAA;AAAA,QACA,OAAA;AAAA;;;MAIF,aAAA;AAAA,MACA,WAAA;AAAA,MACA,aAAA,CAAA;AAAA,MACA,OAAA;AAAA,MACA,cAAA;AAAA,MACA,MAAA;AAAA;MAEA,MAAA;AAAA,MACA,SAAA;AAAA,MACA,OAAA;AAAA,MACA,YAAA;AAAA,MACA,MAAA;AAAA,MACA,MAAA;AAAA;;MAEA,SAAA;AAAA;AAAA;;MAEA,SAAA;AAAA;AAAA,MACA,kBAAA;AAAA;AAAA;;MAEA,kBAAA;AAAA;AAAA;AAAA,MAGA,eAAA;AAAA,MACA,gBAAA;AAAA,MACA,iBAAA;AAAA,MACA,gBAAA;AAAA;MAGA,qBAAA;AAAA;AAAA,MACA,kBAAA;AAAA;AAAA,MACA,kBAAA;AAAA;AAAA,MACA,mBAAA;AAAA;AAAA;;MAEA,gBAAA;AAAA;AAAA,MACA,cAAA,oBAAA,IAAA;AAAA;AAAA;;;MAGA,eAAA;AAAA,MACA,eAAA;AAAA,MAEA,cAAA;AAAA;;MAIA,UAAA,oBAAA,IAAA;AAAA;AAAA,MACA,oBAAA,oBAAA,IAAA;AAAA;AAAA,MACA,iBAAA;AAAA;AAAA;;;;;;;;;MAOA,kBAAA;AAAA;AAAA;;MAEA,OAAA;AAAA;AAAA;IAEF;AAAA;EAEF,UAAA;AAAA;AAAA,IAEE,iBAAA;AACE,aAAA,KAAA,WAAA,KAAA,SAAA;AAAA;;IAIF,sBAAA;;;;IAKA,gBAAA;AACE,aAAA,KAAA,QAAA,SAAA,wBAAA;AAAA;;;;;;;;;;;;;;IAmBF,iBAAA;AACE,aAAA;AAAA,QACE,cAAA,KAAA,SAAA;AAAA;QAEA,cAAA,KAAA,mBAAA,OAAA,KACE,KAAA,mBAAA,QAAA,KAAA,mBAAA,OAAA,KAAA,QAAA,CAAA,IAAA;AAAA;;;IAKN,sBAAA;AACE,aAAA;AAAA;QAEE,kBAAA,KAAA,aAAA;AAAA;;;IAIJ,iBAAA;AACE,UAAA,CAAA,KAAA,SAAA;AAAA,eAAA;AACA,YAAA,QAAA,KAAA,SAAA,UAAA,SAAA;;;;;AAGA,aAAA,QAAA;AAAA,IACF;AAAA;EAEF,MAAA,OAAA,SAAA;AACEA,kBAAA,MAAA,MAAA,OAAA,iCAAA,WAAA,OAAA;;;AAOEA,oBAAA,MAAA,cAAA;AAAA,IACF;AAGA,UAAA,KAAA;;AAIA,SAAA,aAAA;;AAKA,QAAA,QAAA,IAAA;AACEA,oBAAA,MAAA,MAAA,OAAA,iCAAA,YAAA,QAAA,EAAA;;AAIA,UAAA,QAAA,SAAA;AACE,aAAA,cAAA,CAAA,CAAA,QAAA;;MAEF;AAGA,UAAA,QAAA,OAAA;AACE,aAAA,YAAA,CAAA,CAAA,QAAA;;MAEF;;AAIA,WAAA,eAAA;;AAIA,WAAA,eAAA;AAAA;;AAGA,WAAA,YAAA,oBAAA,IAAA;AAAA,IACF;AAAA;EAGF,SAAA;;AAKE,SAAA,eAAA;AAGA,QAAA,KAAA,eAAA,KAAA,gBAAA;;IAEA;AAAA;EAGF,SAAA;;;AAMI,WAAA,WAAA;AAAA,IACF;AAAA;EAGF,UAAA;;AAGIA,oBAAAA,MAAA,aAAA,KAAA,gBAAA;AAAA,IACF;AAGA,SAAA,aAAA;AAAA;;;;;EAUF,SAAA;AAAA;AAAA,IAEE,YAAA,MAAA;AACE,UAAA,KAAA,OAAA;AACEA,sBAAA,MAAA,MAAA,OAAA,iCAAA,aAAA,GAAA,IAAA;AAAA,MACF;AAAA;;IAIF,mBAAA;AACE,UAAA;;AAEE,YAAA,iBAAA;AACE,eAAA,eAAA,KAAA,MAAA,eAAA;AAAA,QACF;AAAA,MACF,SAAA,GAAA;AACEA,sBAAA,MAAA,MAAA,SAAA,iCAAA,YAAA,CAAA;AAAA,MACF;AAAA;;IAIF,eAAA;;;;IAMA,mBAAA;;;AAKEA,sBAAAA,MAAA,cAAA,KAAA,gBAAA;AAAA,MACF;AAGE,UAAA,KAAA,aAAA;;MAEA;AAGA,WAAA,eAAA;;;AAOE,aAAA,iBAAA;;MAEF;AAAA;;IAIF,iBAAA;;AAEE,qBAAA,KAAA,aAAA;;MAEF;;AAGI,qBAAA,KAAA,gBAAA;;MAEF;;AAGE,qBAAA,KAAA,gBAAA;;MAEF;;AAGA,sBAAA,KAAA,kBAAA;;MAEF;AAAA;;;;AAME,qBAAA,KAAA,eAAA;;MAEF;;;;;AASE,UAAA,KAAA,SAAA,eAAA,KAAA,SAAA,YAAA,YAAA,GAAA;;MAEA;;;MAIA;;;;;AAMA,UAAA,KAAA,SAAA,eAAA,KAAA,SAAA,YAAA,WAAA;AACE,eAAA,KAAA,SAAA,YAAA;AAAA,MACF;AACA,aAAA,KAAA,SAAA,aAAA;AAAA;;IAIF,gBAAA;AACE,UAAA,KAAA,SAAA,eAAA,KAAA,SAAA,YAAA,aAAA;AACE,eAAA,KAAA,SAAA,YAAA;AAAA,MACF;AACA,aAAA,KAAA,SAAA,eAAA;AAAA;;IAIF,kBAAA;AACE,UAAA,KAAA,SAAA,eAAA,KAAA,SAAA,YAAA,eAAA;AACE,eAAA,KAAA,SAAA,YAAA;AAAA,MACF;;;;IAKF,SAAA;;AAIE,YAAA,aAAA,KAAA,SAAA,SAAA,IAAA,KAAA,SAAA,OAAA,KAAA,SAAA;AAGA,UAAA,cAAA,WAAA,SAAA,KAAA,WAAA,KAAA,SAAA,GAAA;AACE,cAAA,eAAA,WAAA,KAAA,SAAA;AACA,YAAA,OAAA;AACA,YAAA,OAAA;AAGA,YAAA,OAAA,iBAAA,UAAA;;;QAGA;AAGA,YAAA,QAAA,MAAA;AAEE,cAAA,QAAA,OAAA;AACA,cAAA,aAAA,KAAA,MAAA,MAAA,KAAA;AAGA,cAAA,aAAA;AAAA,yBAAA;AACA,cAAA,aAAA;AAAA,yBAAA;;QAGF;AAAA,MACF;;;;IAMF,iBAAA;;AACE,UAAA;;AAGAA,sBAAA,MAAA,MAAA,OAAA,iCAAA,eAAA,OAAA,QAAA;;AAIE,cAAA;;;UAGA,SAAA,GAAA;AACEA,0BAAA,MAAA,MAAA,SAAA,iCAAA,aAAA,CAAA;;UAEF;AAAA,QACF;AAGE,cAAA,aAAA,UAAA,OAAA,MAAA,QAAA,mBAAA,QAAA;;AAIF,aAAA,aAAA,SAAA,UAAA;AACA,aAAA,eAAA,SAAA,YAAA;;AAIA,cAAA,WAAA,CAAA,CAAA,SAAA;AACA,aAAA,SAAA,aAAA;;UAGE,QAAA,KAAA;AAAA;UAEA,cAAA,KAAA;AAAA,UACA;AAAA,UACA;AAAA,UACA,QAAA,KAAA;AAAA,QACF,CAAA;AAAA;AAEEA,sBAAA,MAAA,MAAA,SAAA,iCAAA,aAAA,KAAA;;AAGA,aAAA,aAAA;AACA,aAAA,eAAA;AACA,aAAA,SAAA;AAAA,MACF;AAAA;;IAKF,iBAAA;AAEEA,oBAAAA,MAAA,YAAA;AAAA;QAEE,MAAA;AAAA,MACF,CAAA;;AAIAC,kCAAA,KAAA,SAAA,EAAA;AAEID,sBAAA,MAAA,YAAA;AACA,aAAA,SAAA,cAAA,GAAA;;;AAME,eAAA,OAAA,UAAA,KAAA,OAAA,UAAA,MAAA,KAAA,UAAA,OAAA,KAAA;;UAEA;AAGA,cAAA,OAAA,UAAA;AACE,gBAAA;AACE,qBAAA,WAAA,KAAA,MAAA,OAAA,QAAA;AAAA,YACF,SAAA,GAAA;AACE,qBAAA,WAAA;YACF;AAAA,UACF;;;;cAMI,OAAA,MAAA;AAAA,YACF,EAAA;AAAA;AAEA,mBAAA,aAAA;UACF;AAGA,cAAA,OAAA,YAAA;AACE,mBAAA,aAAA;AAAA;;cAGE,YAAA,OAAA,WAAA;AAAA;;UAGJ;;AAIE,mBAAA,cAAA;AAAA,cACE,WAAA,OAAA,YAAA,aAAA;AAAA;;;UAIJ;AAGA,cAAA,OAAA,WAAA;AACE,mBAAA,YAAA;AAAA;cAEE,UAAA,OAAA,UAAA,YAAA;AAAA,cACA,QAAA,OAAA,UAAA,UAAA;AAAA,cACA,WAAA,CAAA,CAAA,OAAA,UAAA;AAAA;;;AAIF,mBAAA,YAAA;AAAA,cACE,KAAA,OAAA;AAAA;cAEA,QAAA;AAAA,cACA,WAAA;AAAA,cACA,kBAAA;AAAA;UAEJ;AAGA,cAAA,OAAA,QAAA;AACE,gBAAA,OAAA,OAAA,WAAA,UAAA;AACE,kBAAA;AACE,uBAAA,SAAA,KAAA,MAAA,OAAA,MAAA;AAAA,cACF,SAAA,GAAA;;cAEA;AAAA,YACF;AAEA,gBAAA,CAAA,MAAA,QAAA,OAAA,MAAA,GAAA;AACE,qBAAA,SAAA;YACF;AAAA;AAEA,mBAAA,SAAA;UACF;AAGA,eAAA,WAAA;AAAA,YACE,GAAA,KAAA;AAAA,YACA,GAAA;AAAA;AAAA;YAGA,KAAA,OAAA;AAAA,YACA,MAAA,OAAA,QAAA;AAAA;YAEA,eAAA,OAAA,iBAAA;AAAA;YAEA,WAAA,OAAA,aAAA;AAAA,YACA,aAAA,OAAA,eAAA;AAAA,YACA,aAAA,OAAA,eAAA;AAAA,YACA,OAAA,SAAA,OAAA,KAAA,KAAA;AAAA,YACA,UAAA,SAAA,OAAA,QAAA,KAAA;AAAA,YACA,OAAA,SAAA,OAAA,KAAA,KAAA;AAAA,YACA,QAAA,SAAA,OAAA,MAAA,KAAA;AAAA;;;YAIA,YAAA,OAAA,cAAA;AAAA;;YAGA,aAAA,OAAA,eAAA;AAAA;YAEA,aAAA,OAAA,eAAA;AAAA,YACA,aAAA,OAAA,eAAA;AAAA,YACA,YAAA,OAAA,cAAA;AAAA;;AAIF,eAAA,SAAA,aAAA,KAAA,QAAA;;AAMA,eAAA,eAAA;AAGA,cAAA,KAAA,WAAA;AACE,iBAAA,WAAA,IAAA;AAAA,UACF;;;;AAME,iBAAA,SAAA,SAAA,KAAA,WAAA;AAAA,UACF;AAAA;AAEA,eAAA,SAAA,WAAA,IAAA,GAAA;AACA,eAAA,YAAA,IAAA,OAAA,UAAA,IAAA;AAAA,QACF;AAAA;AAGAA,sBAAA,MAAA,YAAA;;AAEA,aAAA,YAAA,KAAA,eAAA;AAEA,mBAAA,MAAA;AACEA,wBAAA,MAAA,aAAA;AAAA,QACF,GAAA,GAAA;AAAA,MACF,CAAA;AAAA;;IAIJ,mBAAA;AACE,UAAA;;;UAKI,KAAA;AAAA,UACA,KAAA;;;UAGA,KAAA;;;UAGA,KAAA;;;UAGF;AACI,iBAAA,SAAA,YAAA,KAAA,SAAA,IAAA;;AAGE,mBAAA,SAAA,OAAA;;;AAGA,mBAAA,SAAA,OAAA;;;;;YAKF;AAAA,QACJ;AAAA;AAEA,aAAA,SAAA,aAAA,KAAA;AAAA,MACF;AAAA;;IAIF,mBAAA;;AAGE,UAAA;AAEE,YAAA,SAAA,KAAA,SAAA;;AAEE,mBAAA,CAAA;AAAA,QACF;AAGA,aAAA,SAAA,SAAA,OAAA,IAAA,SAAA;;AAEI,mBAAA;AAAA,cACE,KAAA;AAAA,cACA,MAAA;AAAA,cACA,MAAA;AAAA;UAEJ;AACA,iBAAA;AAAA,QACF,CAAA;;MAGF,SAAA,GAAA;;;MAGA;AAAA;;IAIF,mBAAA;AACEA,0BAAA,MAAA,OAAA,kCAAA,WAAA,KAAA,SAAA,KAAA;AAGA,UAAA,CAAA,KAAA,SAAA,eAAA,KAAA,SAAA,SAAA,KAAA,SAAA,MAAA,OAAA;;MAEA;;AAIE,aAAA,SAAA,QAAA,KAAA,SAAA;AAAA,MACF;AAAA;;IAIF,mBAAA;AACE,UAAA,CAAA,KAAA,aAAA;AACEA,sBAAAA,MAAA,MAAA,OAAA,kCAAA,gBAAA;AACA;AAAA,MACF;AAEAA,0BAAA,MAAA,OAAA,kCAAA,WAAA,KAAA,SAAA,KAAA;AAGA,UAAA,CAAA,KAAA,SAAA,aAAA;AACE,aAAA,SAAA,cAAA;AAAA,MACF;AAGA,UAAA,CAAA,KAAA,SAAA,aAAA;AACE,aAAA,SAAA,cAAA;AAAA,MACF;AAGA,WAAA,eAAA;AAAA;;IAIF,iBAAA;;;;;;;;;;IAaA,oBAAA,gBAAA;AAEM,UAAA,CAAA,gBAAA;AACEA,sBAAAA,MAAA,MAAA,OAAA,kCAAA,gBAAA;AACJ,eAAA;MACE;AAGJ,UAAA,OAAA,mBAAA,UAAA;AACMA,sBAAAA,MAAA,MAAA,OAAA,kCAAA,mBAAA;AACA,YAAA,eAAA,WAAA,GAAA,GAAA;AACE,cAAA;AACE,6BAAA,KAAA,MAAA,cAAA;AACAA,0BAAA,MAAA,MAAA,OAAA,kCAAA,aAAA,cAAA;AAAA,UACF,SAAA,UAAA;AACEA,0BAAA,MAAA,MAAA,SAAA,kCAAA,aAAA,QAAA;AACA,6BAAA,CAAA,cAAA;AAAA,UACF;AAAA;AAEAA,wBAAAA,MAAA,MAAA,OAAA,kCAAA,kBAAA;AACA,2BAAA,CAAA,cAAA;AAAA,QACF;AAAA,MACF;AAGA,UAAA,MAAA,QAAA,cAAA,GAAA;AACEA,sBAAA,MAAA,MAAA,OAAA,kCAAA,cAAA,eAAA,MAAA;AACJ,eAAA,eAAA,IAAA,CAAA,KAAA,UAAA;AACMA,wBAAAA,MAAA,MAAA,OAAA,kCAAA,MAAA,QAAA,CAAA,QAAA,GAAA;;AAEE,mBAAA,EAAA,KAAA,KAAA,MAAA,KAAA,MAAA;UACF,WAAA,OAAA,OAAA,QAAA,UAAA;;cAGI,KAAA,IAAA,OAAA,IAAA,QAAA,IAAA,OAAA,IAAA,SAAA;AAAA,cACA,MAAA,SAAA,IAAA,QAAA,IAAA,SAAA,GAAA;AAAA,cACA,MAAA,SAAA,IAAA,QAAA,IAAA,UAAA,GAAA;AAAA;AAEFA,0BAAAA,MAAA,MAAA,OAAA,kCAAA,KAAA,QAAA,CAAA,SAAA,MAAA;;UAEF;AACA,iBAAA,EAAA,KAAA,IAAA,MAAA,KAAA,MAAA;QACF,CAAA,EAAA,OAAA,SAAA,CAAA,CAAA,IAAA,GAAA;AAAA;AAEAA,sBAAAA,MAAA,MAAA,OAAA,kCAAA,kBAAA;AACJ,eAAA;MACA;AAAA;;IAIJ,oBAAA;;;;UAKM,QAAA;AAAA;MAEJ;AAGA,UAAA,CAAA,KAAA,SAAA,UAAA,QAAA;AACE,aAAA,SAAA,UAAA,SAAA;AAAA,MACF;AAGA,UAAA,KAAA,SAAA,aAAA,UAAA,KAAA,SAAA,aAAA,MAAA;;;;MAIA;AAGA,UAAA,CAAA,KAAA,SAAA,OAAA,KAAA,SAAA,SAAA;AACE,aAAA,SAAA,MAAA,KAAA,SAAA;AAAA,MACF;;;;IAMF,iBAAA;;AAGI,aAAA,SAAA,eAAA;AACA;AAAA,MACF;AAGA,UAAA,KAAA,eAAA,aAAA,KAAA,OAAA,GAAA;;AAEE;AAAA,MACF;;AAKA,UAAA,KAAA,SAAA,GAAA;AACEA,sBAAAA,MAAA,YAAA;AAAA;UAEE,MAAA;AAAA,QACF,CAAA;AAAA;;MAIF;;QAIE,MAAA;AAAA;AAAA,QACA,MAAA,KAAA,QAAA;AAAA,QACA,WAAA,KAAA,SAAA;AAAA;AAAA;;QAIA,MAAA,KAAA,SAAA;AAAA,QACA,IAAA,OAAA;AAAA,QACA,MAAA,OAAA,cAAA,IAAA,OAAA;AAAA,MACF,CAAA;AAGAE,iBAAAA,gBAAA,KAAA,SAAA,IAAA,MAAA;AAEI,YAAA,KAAA,SAAA,GAAA;AACEF,wBAAA,MAAA,YAAA;AAAA,QACF;;AAIA,YAAA,IAAA,WAAA,KAAA;AACE,eAAA,SAAA,YAAA,IAAA,IAAA;;;;;;;;UAWA;AAGA,gBAAA,gBAAA,KAAA,IAAA,UAAA;;AACE,mBAAA;AAAA,cACE,GAAA;AAAA;gBAEE,KAAA,KAAA,OAAA;AAAA,gBACA,YAAA,UAAA,cAAA,mBAAA,aAAA;AAAA,gBACA,UAAA,UAAA,cAAA,mBAAA,WAAA;AAAA;cAEF,aAAA,SAAA,KAAA,WAAA,KAAA;AAAA,cACA,YAAA,SAAA,KAAA,UAAA,KAAA;AAAA;cAEA,aAAA,KAAA,eAAA;AAAA;;;;;kBAIE,GAAA;AAAA;oBAEE,KAAA,MAAA,OAAA;AAAA,oBACA,YAAAG,MAAA,MAAA,cAAA,gBAAAA,IAAA,aAAA;AAAA,oBACA,UAAAC,MAAA,MAAA,cAAA,gBAAAA,IAAA,WAAA;AAAA;kBAEF,iBAAA,MAAA,kBAAA;AAAA,oBACE,KAAA,MAAA,gBAAA,OAAA;AAAA,oBACA,UAAA,MAAA,gBAAA,YAAA;AAAA,kBACF,IAAA;AAAA,kBACA,YAAA,SAAA,MAAA,UAAA,KAAA;AAAA;kBAEA,aAAA,MAAA,eAAA;AAAA;;;;UAIN,CAAA;AAGA,cAAA,KAAA,SAAA,GAAA;AAEE,iBAAA,cAAA;AAEA,iBAAA,UAAA,cAAA,WAAA;AAAA;;UAIF;AAGA,cAAA,KAAA,SAAA,IAAA;;;;UAIA;AAGA,cAAA,KAAA,SAAA,GAAA;AACE,kBAAA,WAAA,YAAA,KAAA,SAAA,EAAA,IAAA,KAAA,KAAA,IAAA,KAAA,IAAA;;cAEE,MAAA;AAAA,cACA,SAAA,KAAA;AAAA;cAEA,eAAA,KAAA,SAAA;AAAA;UAEJ;;YAGE,MAAA,KAAA;AAAA;YAEA,MAAA,KAAA;AAAA,YACA,MAAA,KAAA;AAAA,UACF,CAAA;AAAA;;;AAIAJ,wBAAAA,MAAA,UAAA;AAAA;;UAGA,CAAA;AAAA,QACF;AAAA;AAGA,YAAA,KAAA,SAAA,GAAA;AACEA,wBAAA,MAAA,YAAA;AAAA,QACF;;;;AAOA,cAAA,WAAA,KAAA,YAAA,KAAA,QAAA;AACAA,sBAAAA,MAAA,UAAA;AAAA,UACE,OAAA;AAAA;QAEF,CAAA;AAAA,MACF,CAAA;AAAA;IAEJ,MAAA,OAAA,UAAA;;;AAEA,WAAA,SAAA;AACA,UAAA;AAEE,cAAA,SAAA,KAAA,SAAA,UAAA,KAAA;;AAIA,aAAA,eAAA;AAAA,MACF,SAAA,GAAA;;MAEA,UAAA;AACE,aAAA,SAAA;AAAA,MACF;AAAA;;IAGJ,eAAA,GAAA;AACE,UAAA,KAAA,SAAA,EAAA,cAAA,QAAA,EAAA,KAAA;;;;QAKE,cAAA;AAAA,MACF,CAAA;AAGA,UAAA,KAAA;AAAA;;AAIA,YAAA,cAAA,KAAA,YAAA,GAAA;AACA,UAAA,aAAA;AACE,aAAA,KAAA,aAAA,mBAAA,IAAA;AAAA,MACF;;AAMA,YAAA,WAAA,WAAA,EAAA,IAAA,WAAA;AAGA,UAAA,KAAA,aAAA,QAAA,GAAA;;AAEE,aAAA,qBAAA,KAAA,aAAA,QAAA,GAAA,KAAA,WAAA;AACA;AAAA,MACF;;;QAKE,MAAA;AAAA,QACA,OAAA;AAAA;AAAA,QACA,WAAA;AAAA;AAAA;AAGFA,oBAAA,MAAA,MAAA,OAAA,kCAAA,aAAA,MAAA;;AAKI,YAAA,IAAA,WAAA,KAAA;AACEA,wBAAA,MAAA,MAAA,OAAA,kCAAA,YAAA,IAAA,IAAA;AAGA,eAAA,aAAA,QAAA,IAAA,IAAA;;;AAMA,cAAA,aAAA;;UAEA;;AAGAA,wBAAAA,MAAA,UAAA;AAAA;;UAGA,CAAA;AAAA,QACF;AAAA;AAGAA,sBAAA,MAAA,MAAA,SAAA,kCAAA,WAAA,GAAA;AAGA,YAAA,aAAA;;QAEA;;AAGAA,sBAAAA,MAAA,UAAA;AAAA,UACE,OAAA;AAAA;QAEF,CAAA;AAAA,MACF,CAAA;AAAA,IACJ;AAAA;AAAA;;;;;;QAUQ;AAAA,QACA;AAAA,QACA,cAAA;AAAA,QACA;AAAA,MACF,CAAA;AAGA,UAAA,KAAA;AAAA;AACA,WAAA,wBAAA;AAGA,YAAA,cAAA,KAAA,YAAA,GAAA;;;AAKA,WAAA,KAAA,WAAA,oBAAA,IAAA;AAGA,YAAA,WAAA,WAAA,QAAA,IAAA,OAAA,IAAA,WAAA;AAGA,UAAA,KAAA,aAAA,QAAA,GAAA;AACEA,sBAAAA,MAAA,MAAA,OAAA,kCAAA,aAAA;AACA,aAAA,0BAAA,KAAA,aAAA,QAAA,GAAA,KAAA,YAAA,WAAA;AACA;AAAA,MACF;;QAIE,WAAA,SAAA,QAAA,KAAA;AAAA;AAAA,QACA,UAAA,SAAA,OAAA,KAAA;AAAA;AAAA,QACA,YAAA,SAAA,CAAA;AAAA;AAAA,QACA,MAAA,SAAA,WAAA,KAAA;AAAA;AAAA;;;;;AAKFA,oBAAA,MAAA,MAAA,OAAA,kCAAA,cAAA,MAAA;;AAMI,aAAA,KAAA,WAAA,oBAAA,KAAA;AACA,aAAA,wBAAA;AAEAA,sBAAA,MAAA,MAAA,OAAA,kCAAA,YAAA,GAAA;AAEA,YAAA,IAAA,WAAA,KAAA;AAEE,eAAA,aAAA,QAAA,IAAA,IAAA;AAGA,eAAA,0BAAA,IAAA,MAAA,KAAA,YAAA,WAAA;AAAA;AAEAA,wBAAAA,MAAA,UAAA;AAAA;;UAGA,CAAA;AAAA,QACF;AAAA;AAIA,aAAA,KAAA,WAAA,oBAAA,KAAA;AACA,aAAA,wBAAA;AAEAA,sBAAAA,MAAA,MAAA,SAAA,kCAAA,YAAA,GAAA;AACAA,sBAAAA,MAAA,UAAA;AAAA,UACE,OAAA;AAAA;;MAGJ,CAAA;AAAA;;IAIJ,qBAAA,MAAA,KAAA,MAAA;;AAEI,cAAA,cAAA,KAAA,YAAA,GAAA;;AAGAA,sBAAA,MAAA,MAAA,OAAA,kCAAA,YAAA,OAAA;;AAIE,eAAA,KAAA,aAAA,WAAA,OAAA;AAAA;;AAKA,gBAAA,aAAA,QAAA,OAAA,OAAA,CAAA,YAAA,SAAA,EAAA,EAAA,CAAA;AAEA,eAAA,KAAA,aAAA,WAAA,CAAA,GAAA,YAAA,WAAA,CAAA,GAAA,GAAA,UAAA,CAAA;AAAA,QACF;AAGA,aAAA,KAAA,aAAA,aAAA,OAAA,CAAA;AAIA,cAAA,aAAA,SAAA,KAAA,SAAA,CAAA;AACA,cAAA,qBAAA,YAAA,UAAA,YAAA,QAAA,SAAA;AAGA,cAAA,mBAAA,QAAA,SAAA,KAAA,sBAAA;;;;QAMA;AAGA,aAAA,mBAAA,YAAA,OAAA;;;MAKF;AAAA;;;AAKA,UAAA,KAAA,YAAA,GAAA,KAAA,KAAA,YAAA,GAAA,EAAA,QAAA,UAAA,GAAA;;AAIE,cAAA,eAAA,KAAA,QAAA;;AAKE,gBAAA,iBAAA,UAAA,YAAA;AAGA,eAAA,YAAA,GAAA,EAAA,QAAA,KAAA,KAAA;AAAA,QACF,CAAA;;;AASA,aAAA,KAAA,WAAA,oBAAA,KAAA;AACA,aAAA,wBAAA;AAAA,MACF;AAAA;;;AAKA,UAAA,CAAA,WAAA,CAAA,QAAA;AAAA;AAEA,cAAA,QAAA,CAAA,OAAA,UAAA;AACE,aAAA,aAAA,IAAA,MAAA,IAAA,KAAA;AAGA,YAAA,MAAA,YAAA,MAAA,SAAA,QAAA;AACE,eAAA,mBAAA,MAAA,QAAA;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;;AAKA,UAAA,KAAA,EAAA,cAAA,QAAA;AACA,UAAA,MAAA,EAAA,cAAA,QAAA;;;QAIE,cAAA;AAAA;MAEF,CAAA;AAGA,UAAA,KAAA;AAAA;;AAIA,YAAA,cAAA,KAAA,YAAA,GAAA;AACA,UAAA,aAAA;AACE,aAAA,KAAA,aAAA,mBAAA,IAAA;AAAA,MACF;;AAMA,YAAA,WAAA,SAAA,EAAA,IAAA,KAAA,OAAA;AAGA,UAAA,KAAA,aAAA,QAAA,GAAA;;;AAGE;AAAA,MACF;;QAIE,WAAA;AAAA;AAAA,QACA,MAAA,KAAA;AAAA;AAAA,QACA,OAAA;AAAA;AAAA;;;AAIFA,oBAAA,MAAA,MAAA,OAAA,kCAAA,WAAA,MAAA;;AAMI,YAAA,aAAA;AACE,eAAA,KAAA,aAAA,mBAAA,KAAA;AAAA,QACF;;AAGAA,sBAAA,MAAA,MAAA,OAAA,kCAAA,WAAA,GAAA;AAEA,YAAA,IAAA,WAAA,KAAA;AAEE,eAAA,aAAA,QAAA,IAAA,IAAA;AAGA,eAAA,gBAAA,IAAA,MAAA,GAAA;AAAA;AAEAA,wBAAAA,MAAA,UAAA;AAAA;;UAGA,CAAA;AAAA,QACF;AAAA;AAIA,YAAA,aAAA;AACE,eAAA,KAAA,aAAA,mBAAA,KAAA;AAAA,QACF;;AAGAA,sBAAAA,MAAA,MAAA,SAAA,kCAAA,WAAA,GAAA;AACAA,sBAAAA,MAAA,UAAA;AAAA,UACE,OAAA;AAAA;;;;;;;;AAYJ,YAAA,CAAA,KAAA,YAAA,GAAA,EAAA,SAAA;AACE,eAAA,YAAA,GAAA,EAAA,UAAA,CAAA;AAAA,QACF;;AAMA,aAAA,YAAA,GAAA,EAAA,UAAA,CAAA,GAAA,KAAA,YAAA,GAAA,EAAA,SAAA,GAAA,IAAA;;;QAKA,CAAA;AAGA,YAAA,KAAA,UAAA,UAAA,KAAA,UAAA,MAAA;;;AAGE,eAAA,YAAA,GAAA,EAAA,cAAA,KAAA,YAAA,GAAA,EAAA,QAAA;AAAA,QACF;AAGA,YAAA,KAAA,SAAA,MAAA,KAAA,YAAA,GAAA,EAAA,QAAA,UAAA,KAAA,YAAA,GAAA,EAAA,aAAA;;QAEA;AAAA,MACF;AAAA;;IAIF,aAAA,MAAA;;;;;;AAOE,WAAA,QAAA;;;AAKA,WAAA,cAAA,CAAA;AACA,WAAA,UAAA;AAEA,WAAA,eAAA;AAGA,iBAAA,MAAA;AACE,aAAA,eAAA;AAAA;;;IAKJ,YAAA,GAAA;AAEE,UAAA,KAAA,EAAA,eAAA,EAAA,SAAA,EAAA,MAAA,EAAA,EAAA;AAGA,QAAA,mBAAA,EAAA;AAEAA,oBAAAA,MAAA,MAAA,OAAA,kCAAA,iBAAA;AAAA,QACE,QAAA,KAAA;AAAA,QACA,QAAA,KAAA;AAAA,QACA,UAAA;AAAA;;QAGA;AAAA,MACF,CAAA;AAEA,UAAA,CAAA,KAAA,QAAA;AACEA,sBAAAA,MAAA,MAAA,OAAA,kCAAA,cAAA;AACA,aAAA,YAAA,cAAA;AACA,mBAAA,MAAA;AACEA,wBAAAA,MAAA,WAAA;AAAA;;QAGF,GAAA,GAAA;;MAEF;AAEA,UAAA,UAAA,EAAA,cAAA,WAAA,CAAA;;AAEA,UAAA,MAAA,QAAA,OAAA;AACA,UAAA,MAAA,QAAA,OAAA;;;AAIA,WAAA,KAAA,QAAA,MAAA,SAAA,QAAA,IAAA;AAGA,WAAA,YAAA;AAGA,WAAA,sBAAA;;;;;;;;;MAWA;AAGA,WAAA,UAAA,MAAA;AAEE,aAAA,YAAA;AAGA,mBAAA,MAAA;AACE,eAAA,UAAA;AAGA,cAAA,CAAA,KAAA,WAAA;AACE,iBAAA,YAAA;AAAA,UACF;AAAA,QACF,GAAA,GAAA;AAAA,MACF,CAAA;AAAA;;;;;;AAWE,aAAA,sBAAA;AACA,eAAA,KAAA,YAAA,aAAA;AAAA,MACF;AAGA,WAAA,YAAA;AACA,WAAA,UAAA;AAEAA,oBAAAA,MAAA,YAAA;AAAA;QAEE,MAAA;AAAA,MACF,CAAA;AAGA,YAAA,gBAAA;AAAA,QACE,MAAA;AAAA;AAAA,QACA,WAAA,KAAA,SAAA;AAAA;AAAA,QACA,SAAA,KAAA;AAAA;AAAA;;;;;AAKFA,oBAAA,MAAA,MAAA,OAAA,kCAAA,WAAA,aAAA;AAGAK,iBAAAA,WAAA,aAAA,EAAA,KAAA,SAAA;AACEL,sBAAAA,MAAA,YAAA;;AAGAA,sBAAA,MAAA,MAAA,OAAA,kCAAA,WAAA,GAAA;AAEA,YAAA,IAAA,WAAA,KAAA;;AAKE,cAAA,cAAA,IAAA,QAAA,KAAA,yBAAA,KAAA,SAAA,EAAA;AAEAA,wBAAA,MAAA,MAAA,OAAA,kCAAA,YAAA,WAAA;;AAIIA,0BAAA,MAAA,MAAA,OAAA,kCAAA,aAAA,KAAA,IAAA;AAEA,gBAAA,CAAA,KAAA,YAAA,KAAA,IAAA,EAAA,SAAA;AACE,mBAAA,YAAA,KAAA,IAAA,EAAA,UAAA,CAAA;AAAA,YACF;AACA,gBAAA,CAAA,KAAA,YAAA,KAAA,IAAA,EAAA,aAAA;AACE,mBAAA,YAAA,KAAA,IAAA,EAAA,cAAA;AAAA,YACF;;AAIA,iBAAA,YAAA,KAAA,IAAA,EAAA,eAAA;AAGA,iBAAA,aAAA,IAAA,YAAA,IAAA,KAAA,YAAA,KAAA,IAAA,EAAA,QAAA,SAAA,CAAA;AAGA,iBAAA,aAAA;AAAA,UACF,WAEA,KAAA,QAAA,KAAA,KAAA,MAAA,GAAA;AACEA,gCAAA,MAAA,OAAA,kCAAA,aAAA,KAAA,MAAA,KAAA,EAAA;AAEA,gBAAA,CAAA,KAAA,YAAA,KAAA,IAAA,EAAA,SAAA;AACE,mBAAA,YAAA,KAAA,IAAA,EAAA,UAAA,CAAA;AAAA,YACF;AACA,gBAAA,CAAA,KAAA,YAAA,KAAA,IAAA,EAAA,aAAA;AACE,mBAAA,YAAA,KAAA,IAAA,EAAA,cAAA;AAAA,YACF;;AAIA,iBAAA,YAAA,KAAA,IAAA,EAAA,eAAA;AAGA,iBAAA,aAAA,IAAA,YAAA,IAAA,KAAA,YAAA,KAAA,IAAA,EAAA,QAAA,SAAA,CAAA;AAGA,iBAAA,aAAA;AAAA,UACF;;AAIE,gBAAA,KAAA,YAAA,UAAA,GAAA;AACE,mBAAA,UAAA;AACA,mBAAA,cAAA,CAAA;AAAA,YACF;AAGA,wBAAA,UAAA,CAAA;;AAGA,iBAAA,YAAA,QAAA,WAAA;AAGA,iBAAA,aAAA;AAAA,UACJ;AAEA,eAAA,UAAA;;;AAGA,eAAA,UAAA;;;AAGA,eAAA,YAAA,IAAA,OAAA,MAAA;AAAA,QACF;AAEA,aAAA,YAAA;AAAA,MACF,CAAA,EAAA,MAAA,SAAA;AACEA,sBAAAA,MAAA,YAAA;;AAEAA,sBAAA,MAAA,MAAA,SAAA,kCAAA,WAAA,GAAA;;AAEA,aAAA,YAAA;AAAA;;;;;AAOF,UAAA,MAAA,EAAA,cAAA,QAAA;;AAEA,UAAA,YAAA,EAAA,cAAA,QAAA;AAGA,UAAA,KAAA;AAAA;AAEAA,oBAAAA,MAAA,UAAA;AAAA;;QAGE,SAAA,SAAA,KAAA;AACE,cAAA,IAAA,SAAA;;AAGEA,0BAAAA,MAAA,YAAA;AAAA;cAEE,MAAA;AAAA;AAIFM,uBAAAA,cAAA,SAAA,EAAA,KAAA,CAAAC,SAAA;AACEP,4BAAAA,MAAA,YAAA;;AAGA,kBAAAO,KAAA,WAAA,KAAA;;;gBAIE;AAGF,oBAAA,KAAA,IAAA;AAEE,uBAAA,YAAA,GAAA,EAAA,eAAA,oBAAA,KAAA,GAAA,YAAA;AACA,uBAAA,YAAA,GAAA,EAAA,SAAA;AAAA;AAGE,sBAAA,KAAA,YAAA,GAAA,EAAA,WAAA,KAAA,YAAA,GAAA,EAAA,QAAA,SAAA,GAAA;AACE,yBAAA,YAAA,GAAA,EAAA,QAAA,CAAA,EAAA,eAAA,oBAAA,KAAA,GAAA,YAAA;;AAIA,wBAAA,KAAA,YAAA,GAAA,EAAA,cAAA,GAAA;AAEE,2BAAA,YAAA,GAAA,EAAA;AAAA,oBACF;AAAA,kBACF;AAAA,gBACF;;;AAGA,qBAAA,YAAAA,KAAA,OAAA,MAAA;AAAA,cACF;AAAA,YACF,CAAA,EAAA,MAAA,SAAA;AACEP,4BAAAA,MAAA,YAAA;;;;UAIJ;AAAA,QACF;AAAA;;;IAKJ,kBAAA,WAAA,kBAAA;AACE,UAAA,CAAA,KAAA,QAAA;;;MAGA;;;;AAOA,iBAAA,MAAA;;;AAKA,YAAA,mBAAA,mBAAA,IAAA;AACA,YAAA,eAAA,mBAAA,IAAA;AAIA,eAAA,IAAA,GAAA,IAAA,KAAA,YAAA,QAAA,KAAA;AACE,cAAA,UAAA,KAAA,YAAA,CAAA;;;AAKE,cAAA,cAAA;AACE,oBAAA,SAAA,QAAA,SAAA,KAAA;AAAA;AAEA,oBAAA;AAAA,UACF;AACA;AAAA,QACF;AAGA,YAAA,QAAA,WAAA,QAAA,QAAA,SAAA,GAAA;AACE,mBAAA,IAAA,GAAA,IAAA,QAAA,QAAA,QAAA,KAAA;;;;AAII,kBAAA,cAAA;;;;cAIA;AACA;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAGA,UAAA,kBAAA;;;;AASM,eAAA,YAAA,KAAA,UAAA;AAAA;;AAIJQ,mBAAAA,YAAA,SAAA;;;AAOI,eAAA,YAAA,KAAA,UAAA;AAAA;MAEN;AAAA;;;AAMA,eAAA,IAAA,GAAA,IAAA,KAAA,YAAA,QAAA,KAAA;AACE,cAAA,UAAA,KAAA,YAAA,CAAA;;AAIE,kBAAA,UAAA;;AAEE,gBAAA,QAAA,QAAA;AAAA,sBAAA;AAAA;AAEA,oBAAA;AAAA,UACF;;QAEF;AAGA,YAAA,QAAA,WAAA,QAAA,QAAA,SAAA,GAAA;AACE,mBAAA,IAAA,GAAA,IAAA,QAAA,QAAA,QAAA,KAAA;;;AAGI,oBAAA,UAAA;;;;;;cAKA;;YAEF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA;;;;AAOAR,oBAAAA,MAAA,UAAA;AAAA;;QAGE,SAAA,SAAA,KAAA;AACE,cAAA,IAAA,SAAA;AACEA,0BAAAA,MAAA,YAAA;AAAA,cACE,MAAA;AAAA;AAIFS,uBAAA,cAAA,KAAA,SAAA,EAAA,EAAA,KAAA,CAAAF,SAAA;AACEP,4BAAAA,MAAA,YAAA;AACA,qBAAA,EAAA,WAAA,eAAA;AAEA,kBAAAO,KAAA,WAAA,KAAA;;;AAGE,qBAAA,YAAAA,KAAA,OAAA,MAAA;AAAA,cACF;AAAA,YACF,CAAA,EAAA,MAAA,SAAA;AACEP,4BAAAA,MAAA,YAAA;;;UAGJ;AAAA,QACF;AAAA;;;IAKJ,YAAA,QAAA;;AAGEA,oBAAAA,MAAA,YAAA;AAAA,QACE,MAAA;AAAA;;AAKF,UAAA,KAAA,SAAA,QAAA,KAAA,KAAA,SAAA,UAAA,KAAA,SAAA,OAAA,SAAA,GAAA;AACE,mBAAA,KAAA,SAAA,OAAA,CAAA,EAAA;AAAA,MACF,WAAA,KAAA,SAAA,QAAA,KAAA,KAAA,SAAA,SAAA,KAAA,SAAA,aAAA;AACE,mBAAA,KAAA,SAAA;AAAA,MACF,WAAA,KAAA,SAAA,QAAA,KAAA,KAAA,SAAA,SAAA,KAAA,SAAA,MAAA,OAAA;AACE,mBAAA,KAAA,SAAA,MAAA;AAAA,MACF;;QAIE;AAAA,QACA,KAAA,SAAA;AAAA,QACA,KAAA,SAAA;AAAA,QACA,KAAA,SAAA;AAAA,QACA;AAAA;AAEAA,sBAAAA,MAAA,YAAA;AACA,aAAA,YAAA,IAAA,OAAA,MAAA;;MAEF,CAAA,EAAA,MAAA,SAAA;AACEA,sBAAAA,MAAA,YAAA;;;;;;AAOF,UAAA,KAAA;AAAA;;;AAKA,YAAA,eAAA,mBAAA,IAAA;AACA,YAAA,WAAA,KAAA,SAAA;AAGA,WAAA,SAAA,UAAA;;;QAKE,IAAA,KAAA,SAAA;AAAA;;;;AAQA,aAAA,SAAA,UAAA;;AAEA,aAAA,YAAA,KAAA,UAAA;AAAA;AAIA,mBAAA,MAAA;;QAEA,GAAA,GAAA;AAAA,MACF,CAAA;AAAA;;;;;;AAUA,YAAA,iBAAA,CAAA,KAAA;AACA,YAAA,iBAAA,KAAA;AAEA,WAAA,cAAA;;;MAKA;;;;;;AAYE,aAAA,cAAA;;;QAGA;AACA,aAAA,YAAA,KAAA,UAAA;AAAA;AAIA,mBAAA,MAAA;;QAEA,GAAA,GAAA;AAAA,MACF,CAAA;AAAA;;IAIF,YAAA,GAAA;;;;AAKE,UAAA,OAAA,CAAA;AAGA,YAAA,aAAA,KAAA,SAAA,SAAA,IAAA,KAAA,SAAA,OAAA,KAAA,SAAA;AAGA,UAAA,cAAA,WAAA,SAAA,GAAA;AACE,YAAA,OAAA,WAAA,CAAA,MAAA,UAAA;AAEE,iBAAA;AAAA;;AAIE,kBAAA,WAAA,KAAA,YAAA,IAAA;;AAEE,mBAAA,KAAA,QAAA;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAGA,UAAA,KAAA,WAAA,GAAA;AACEA,sBAAAA,MAAA,MAAA,SAAA,kCAAA,aAAA;AACA;AAAA,MACF;AAGAA,oBAAAA,MAAA,aAAA;AAAA;QAEE;AAAA;;;IAKJ,aAAA,GAAA;AAEE,WAAA,EAAA,mBAAA,EAAA,gBAAA;;;AAME;AAAA,MACF;;AAIE,qBAAA,KAAA,gBAAA;;MAEF;;AAMA,WAAA,YAAA;AACA,WAAA,UAAA;AAGA,WAAA,UAAA;AAAA;;IAIF,aAAA,GAAA;AAEE,iBAAA,MAAA;AACA,aAAA,YAAA,EAAA,OAAA;AAEE,aAAA,aAAA;AAAA,MACF,GAAA,CAAA;AAAA;;IAIF,oBAAA;AACEA,oBAAAA,MAAA,aAAA;AAAA,QACE,UAAA,WAAA,KAAA,SAAA,QAAA;AAAA,QACA,WAAA,WAAA,KAAA,SAAA,SAAA;AAAA,QACA,MAAA,KAAA,SAAA;AAAA;;;;IAOJ,WAAA,MAAA;;;;;;;;IAUA,WAAA,QAAA;AACEA,oBAAAA,MAAA,WAAA;AAAA,QACE,KAAA,sBAAA,MAAA;AAAA,MACF,CAAA;AAAA;;IAIF,aAAA,QAAA;AACE,WAAA,WAAA;AAAA;;;AAKA,WAAA,YAAA,WAAA,MAAA;AAAA;;IAIF,cAAA,QAAA;AACEA,oBAAA,MAAA,MAAA,OAAA,kCAAA,WAAA,MAAA;AAAA;;IAKF,cAAA,GAAA;AACEA,oBAAAA,MAAA,WAAA;AAAA,QACE,KAAA,YAAA,EAAA,cAAA,QAAA;AAAA;;;IAKJ,eAAA;AACE,UAAA,KAAA,SAAA,OAAA;AAAA;AAEA,UAAA;;;AAIM,iBAAA,SAAA,IAAA,MAAA,QAAA,KAAA;AAAA,UACF;AAAA,QACF,CAAA;AACAA,4BAAA,MAAA,OAAA,kCAAA,iBAAA,KAAA,SAAA,MAAA,KAAA;AAAA;AAEAA,sBAAA,MAAA,MAAA,SAAA,kCAAA,cAAA,KAAA;AAAA,MACF;AAAA;;;AAKA,UAAA,CAAA,QAAA,OAAA,SAAA;AAAA,eAAA,CAAA;AAGA,YAAA,WAAA;AACA,UAAA,KAAA,mBAAA,IAAA,QAAA,GAAA;AACE,eAAA,KAAA,mBAAA,IAAA,QAAA;AAAA,MACF;;AAGA,UAAA,YAAA;AAGA,YAAA,QAAA;AACA,UAAA;AAEA,UAAA;AACA,gBAAA,QAAA,MAAA,KAAA,IAAA,OAAA,MAAA;;;AAIM,gBAAA,aAAA;;;gBAGI,MAAA;AAAA,cACN,CAAA;AAAA,YACE;AAAA,UACJ;;AAIE,gBAAA,QAAA,KAAA,SAAA,IAAA,WAAA;AAEY,cAAA,SAAA,MAAA,KAAA;;;cAIV,OAAA;AAAA;gBAEM,OAAA;AAAA;;gBAGA,YAAA,MAAA;AAAA,cACN;AAAA,YACF,CAAA;AAAA;;;cAKI,MAAA;AAAA,YACJ,CAAA;AAAA,UACF;;QAGF;;AAII,gBAAA,gBAAA,KAAA,UAAA,SAAA;AACA,cAAA,eAAA;;;cAGI,MAAA;AAAA,YACF,CAAA;AAAA,UACF;AAAA,QACF;AAGA,aAAA,sBAAA,UAAA,KAAA;;;AAIAA,sBAAA,MAAA,MAAA,SAAA,kCAAA,aAAA,KAAA;AAEA,eAAA,CAAA;AAAA;UAEE;AAAA,QACF,CAAA;AAAA,MACF;AAAA;;IAIF,sBAAA,UAAA,OAAA;;;AAII,aAAA,mBAAA,OAAA,QAAA;AAAA,MACF;AACA,WAAA,mBAAA,IAAA,UAAA,KAAA;AAAA;;IAIF,6BAAA,MAAA;AACE,UAAA,CAAA,QAAA,OAAA,SAAA;AAAA,eAAA;AAGA,YAAA,WAAA,YAAA,IAAA;AACA,UAAA,KAAA,mBAAA,IAAA,QAAA,GAAA;AACE,eAAA,KAAA,mBAAA,IAAA,QAAA;AAAA,MACF;AAEA,UAAA;AAEG,YAAA,gBAAA,KAAA,QAAA,wBAAA,CAAA,UAAA;AACE,gBAAA,QAAA,KAAA,SAAA,IAAA,KAAA;AACA,cAAA,SAAA,MAAA,KAAA;AACE,mBAAA,aAAA,MAAA,GAAA,kUAAA,KAAA;AAAA,UACF;;QAEF,CAAA;;;AAKC,eAAA,mBAAA,OAAA,QAAA;AAAA,QACF;;AAGA,eAAA;AAAA;AAEAA,sBAAA,MAAA,MAAA,SAAA,kCAAA,aAAA,KAAA;;MAEF;AAAA;;IAIF,aAAA,OAAA;;AAGI,qBAAA,KAAA,eAAA;AAAA,MACF;AAEA,WAAA,kBAAA,WAAA,MAAA;AACE,cAAA,SAAA,MAAA,UAAA,MAAA;;AAEA,cAAA,WAAA,OAAA,aAAA,UAAA;;AAIE,eAAA,iBAAA,aAAA,QAAA;AAAA,QACF;AAAA,MACF,GAAA,GAAA;AAAA;;;;QAME;AAAA,QACA;AAAA,QACA,WAAA,KAAA,IAAA;AAAA;AAIFA,oBAAAA,MAAA,gBAAA;AAAA;QAEE,SAAA,CAAA,QAAA;AACE,cAAA,IAAA,aAAA,GAAA;AAEEA,0BAAAA,MAAA,iBAAA;AAAA;;AAGIA,8BAAAA,MAAA,UAAA;AAAA;;gBAGA,CAAA;AAAA,cACF;AAAA,YACF,CAAA;AAAA;AAGAA,0BAAAA,MAAA,aAAA;AAAA;;YAGA,CAAA;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;IAIF,kBAAA;AACE,WAAA,mBAAA;;;;IAKF,qBAAA;AACE,aAAA;AAAA,QACE,cAAA,KAAA,SAAA;AAAA;;;;;;AAQF,WAAA,UAAA,MAAA;AACE,YAAA;;AAKM,gBAAA,OAAA,IAAA,CAAA,GAAA;;AAGIA,8BAAAA,MAAA,oBAAA,EAAA,GAAA,IAAA,EACE,OAAA,+BAAA,QAAA,CAAA,gCAAA,QAAA,CAAA,GAAA,EACA,OAAA;AAAA,kBACE,MAAA;AAAA,kBACA,MAAA;AAAA,mBAEF,KAAA,CAAA,YAAA;AACE,sBAAA,WAAA,QAAA,CAAA,KAAA,QAAA,CAAA,EAAA,MAAA;;;;;;;;;kBAUA;AAAA,gBACF,CAAA;AAAA,cACJ,CAAA;AAAA,YACF;AAAA,UACF,CAAA;AAAA;AAEFA,wBAAA,MAAA,MAAA,QAAA,kCAAA,eAAA,KAAA;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;;AAKAA,oBAAA,MAAA,MAAA,OAAA,kCAAA,WAAA,EAAA,MAAA;AAGA,UAAA,EAAA,UAAA,EAAA,OAAA,WAAA,QAAA;AACE,aAAA,iBAAA,EAAA,OAAA;AAAA,MACF;AAAA;;IAIF,UAAA;AACE,UAAA,gBAAA,EAAA,SAAA,GAAA;AACEA,sBAAAA,MAAA,aAAA;AAAA;AAEAA,sBAAAA,MAAA,UAAA;AAAA;;MAGF;AAAA;;IAIF,eAAA,KAAA;AACE,UAAA,CAAA;AAAA,eAAA;;;AAMI,iBAAA,WAAA;AAAA,QACF;;AAGE,iBAAA,2BAAA;AAAA,QACF;AAEA,eAAA,4BAAA;AAAA,MACF;AAEA,aAAA;AAAA;;IAIF,eAAA;AAEE,UAAA,CAAA,KAAA,aAAA;AACEA,sBAAAA,MAAA,MAAA,OAAA,kCAAA,iBAAA;AACA;AAAA,MACF;AAEA,UAAA;;AAII,eAAA,WAAA;AAAA;AAGA,eAAA,UAAA;AAAA,QACF;AAAA,MACF,SAAA,GAAA;AACEA,sBAAA,MAAA,MAAA,SAAA,kCAAA,WAAA,CAAA;;MAEF;AAAA;;;;;AAOA,UAAA;;;;MAIA,SAAA,GAAA;AACEA,sBAAA,MAAA,MAAA,SAAA,kCAAA,WAAA,CAAA;;MAEE;AAAA;;;AAMJ,UAAA,CAAA,KAAA,aAAA;AACEA,sBAAAA,MAAA,MAAA,OAAA,kCAAA,iBAAA;AACA;AAAA,MACF;;AAIM,eAAA,KAAA,YAAA,SAAA;AAAA,MACF;;AAIE,YAAA;;;;AAIF;AAAA,QACE,SAAA,GAAA;AACEA,wBAAA,MAAA,MAAA,SAAA,kCAAA,kBAAA,CAAA;;QAGN;AAAA;;MAIF;AAAA;;IAIF,sBAAA;;;AAGE,UAAA;AAEEA,sBAAAA,MAAA,UAAA;AAAA,UACE,OAAA;AAAA,UACA,MAAA;AAAA,UACA,MAAA;AAAA,QACF,CAAA;;;AASA,aAAA,eAAA,SAAA,KAAA,SAAA,UAAA,YAAA;;AAIA,aAAA,eAAA,SAAA;;AAWA,cAAA,iBAAA,KAAA;AAGA,aAAA,oBAAA,cAAA;;AAIA,aAAA,eAAA,MAAA;AAAA,MAEF,SAAA,GAAA;AACEA,sBAAA,MAAA,MAAA,SAAA,kCAAA,aAAA,CAAA;;MAEF;AAAA;;IAIF,oBAAA,gBAAA;;;AAGE,UAAA;AACE,aAAA,eAAA,OAAA,MAAA;;;AAIEA,wBAAA,MAAA,UAAA;;;QAGF,CAAA;AAEA,aAAA,eAAA,QAAA,CAAA,QAAA;;;AAIEA,wBAAA,MAAA,MAAA,SAAA,kCAAA,WAAA,GAAA;;QAEF,CAAA;AAEA,aAAA,eAAA,QAAA,MAAA;;;;;QAIA,CAAA;AAEA,aAAA,eAAA,OAAA,MAAA;;;;;QAIA,CAAA;AAEA,aAAA,eAAA,QAAA,MAAA;;;;;QAIA,CAAA;AAEA,aAAA,eAAA,UAAA,MAAA;;;;QAGA,CAAA;AAEA,aAAA,eAAA,UAAA,MAAA;;;;AAGEA,wBAAA,MAAA,UAAA;AAAA,QACF,CAAA;AAAA,MAEF,SAAA,GAAA;AACEA,sBAAA,MAAA,MAAA,SAAA,kCAAA,cAAA,CAAA;;MAEF;AAAA;;;;;AAOIA,oBAAA,MAAA,UAAA;;;AAKJ,UAAA,OAAA,IAAA,SAAA;AACE,gBAAA,IAAA,SAAA;AAAA,UACE,KAAA;AAAA,uBAAA;AAAA;AAAA,UACA,KAAA;AAAA,uBAAA;AAAA;AAAA,UACA,KAAA;AAAA,uBAAA;AAAA;AAAA,UACA,KAAA;AAAA,uBAAA;AAAA;AAAA,UACA;AAAA,uBAAA,iBAAA,IAAA;AAAA,QACF;AAAA,MACF;;;;;;IASF,mBAAA;;;AAGE,UAAA;;MAIA,SAAA,GAAA;AACEA,sBAAA,MAAA,MAAA,SAAA,kCAAA,aAAA,CAAA;AAAA,MACA;AAAA;;;AAKF,UAAA,CAAA,KAAA,aAAA;AACEA,sBAAAA,MAAA,MAAA,OAAA,kCAAA,iBAAA;AACA;AAAA,MACF;;;AAKE,YAAA;;;UAIE;AAWA,cAAA;AACE,gBAAA,KAAA,eAAA,SAAA;;AAEE,mBAAA,eAAA;;AAEA,mBAAA,eAAA;AACA,mBAAA,eAAA;AACA,mBAAA,eAAA;AACA,mBAAA,eAAA;AACA,mBAAA,eAAA;YACF;AAAA,UACF,SAAA,GAAA;AACEA,0BAAA,MAAA,MAAA,SAAA,kCAAA,oBAAA,CAAA;AAAA,UACF;;;;;QASF,SAAA,GAAA;AACEA,wBAAA,MAAA,MAAA,SAAA,kCAAA,kBAAA,CAAA;;;;QAIF;AAAA,MACF;AAAA;;IAIF,aAAA,GAAA;AAEE,WAAA,aAAA,KAAA,EAAA,UAAA,EAAA,OAAA;AACA,WAAA,aAAA,KAAA,EAAA,UAAA,EAAA,OAAA;AAGA,iBAAA,MAAA;AAEE,YAAA,KAAA,aAAA,CAAA,KAAA,qBAAA;AACE,eAAA,YAAA;AACA,eAAA,UAAA;AAAA,QACF;AAAA,MACF,GAAA,GAAA;AAAA;IAEF,oBAAA;AACE,UAAA,CAAA,KAAA,QAAA;AACE,aAAA,YAAA,cAAA;AACA,mBAAA,MAAA;AACEA,wBAAAA,MAAA,WAAA;AAAA;;QAGF,GAAA,GAAA;;MAEF;;;;AAMA,WAAA,KAAA;AACA,WAAA,UAAA;;AAMA,WAAA,YAAA;;;;IAOF,mBAAA,OAAA,OAAA,GAAA,MAAA,GAAA,MAAA,GAAA,OAAA,IAAA,MAAA,IAAA,IAAA,IAAA;AACEA,oBAAAA,MAAA,MAAA,OAAA,kCAAA,WAAA,EAAA,MAAA,KAAA,KAAA,MAAA,KAAA,EAAA,CAAA;AAGA,UAAA,SAAA,MAAA,iBAAA;;MAEA;AAGA,UAAA,CAAA,KAAA,QAAA;AACEA,sBAAAA,MAAA,MAAA,OAAA,kCAAA,iBAAA;AAAA,UACE,QAAA,KAAA;AAAA,UACA,UAAA,CAAA,CAAA,KAAA,OAAA,MAAA,IAAA;AAAA,UACA,UAAA,KAAA,OAAA,MAAA,IAAA,WAAA,CAAA,CAAA,KAAA,OAAA,MAAA,IAAA,SAAA,QAAA;AAAA,QACF,CAAA;AAEA,aAAA,YAAA,cAAA;AACA,mBAAA,MAAA;AACEA,wBAAAA,MAAA,WAAA;AAAA;UAEA,CAAA;AAAA,QACF,GAAA,GAAA;AACA;AAAA,MACF;AAGA,UAAA,KAAA;AAAA;;;AAKA,WAAA,KAAA;;;AAKA,UAAA,SAAA,KAAA,MAAA;;;;MAIA;AAGA,WAAA,UAAA;AAGA,WAAA,sBAAA;AAGA,WAAA,YAAA;;AAIA,iBAAA,MAAA;;MAEA,GAAA,GAAA;AAAA;;;;;;;AAWAA,oBAAAA,MAAA,aAAA;AAAA;;MAGA,CAAA;AAAA;;IAGF,kBAAA,aAAA;AACE,UAAA,KAAA;AAAA;;AAIA,YAAA,UAAA,YAAA;;AAIA,UAAA,CAAA,WAAA,CAAA,OAAA;AACE,aAAA,sBAAA;AACA,eAAA,KAAA,YAAA,aAAA;AAAA,MACF;AAGAA,oBAAAA,MAAA,YAAA;AAAA;QAEE,MAAA;AAAA,MACF,CAAA;AAGA,WAAA,YAAA;AACA,WAAA,UAAA;AACA,WAAA,YAAA;;;QAKE;AAAA,QACA,KAAA,KAAA,QAAA;AAAA,QACA,QAAA,KAAA,QAAA;AAAA;AAIF,UAAA,OAAA;AACE,eAAA,QAAA;AAAA,MACF;AAGAK,iBAAAA,WAAA,MAAA;AAEIL,sBAAA,MAAA,YAAA;AAEA,YAAA,IAAA,WAAA,KAAA;AAEE,gBAAAU,eAAA,IAAA,QAAA,KAAA,yBAAA,SAAA,KAAA;AAGA,eAAA,sBAAAA,YAAA;;AAMA,cAAA,KAAA,SAAA;AACE,iBAAA,UAAA;AAAA,UACF;AAAA;AAEA,eAAA,YAAA,IAAA,OAAA,MAAA;AAAA,QACF;AAAA;AAGAV,sBAAA,MAAA,YAAA;;AAEAA,sBAAA,MAAA,MAAA,SAAA,kCAAA,UAAA,GAAA;AAAA;AAIA,aAAA,sBAAA;;;;AAMA,aAAA,UAAA;AAAA,MACF,CAAA;AAAA;;IAIJ,yBAAA,SAAA,UAAA;;AAEE,YAAA,SAAA,QAAA,KAAA,IAAA,CAAA,IAAA,KAAA,OAAA,EAAA,SAAA,EAAA,EAAA,OAAA,GAAA,CAAA,CAAA;AAGA,YAAA,iBAAA,UAAA,aAAA,mBAAA,eAAA,gBAAA,OAAA,MAAA,QAAA,mBAAA,aAAA,mBAAA,aAAA;AAGA,YAAA,cAAA;AAAA,QACE,IAAA;AAAA;AAAA,QACA,KAAA,KAAA;AAAA,QACA,UAAA,KAAA,gBAAA;AAAA;QAEA,SAAA,WAAA;AAAA;;QAEA,aAAA,KAAA,WAAA,oBAAA,KAAA,CAAA;AAAA,QACA,OAAA;AAAA,QACA,YAAA;AAAA;AAAA;QAEA,QAAA;AAAA;AAAA,QACA,UAAA;AAAA,QACA,aAAA;AAAA;AAAA,QACA,SAAA,CAAA;AAAA;AAAA,QACA,aAAA;AAAA;AAAA,QACA,kBAAA;AAAA;AAAA,QACA,WAAA;AAAA;AAAA,QACA,iBAAA;AAAA;AAAA;AAGFA,oBAAA,MAAA,MAAA,OAAA,kCAAA,aAAA,WAAA;AACA,aAAA;AAAA;;IAIF,WAAA,MAAA;;;;;;;;;IAWA,sBAAA,aAAA;;;QAGI,MAAA,KAAA;AAAA;QAEA,kBAAA,KAAA,SAAA;AAAA,MACF,CAAA;AAGA,UAAA,CAAA,aAAA;AACEA,sBAAAA,MAAA,MAAA,SAAA,kCAAA,aAAA;AACA;AAAA,MACF;;AAMA,UAAA,KAAA,QAAA,GAAA;AACEA,sBAAA,MAAA,MAAA,OAAA,kCAAA,UAAA,KAAA,IAAA;AAEA,YAAA,CAAA,KAAA,YAAA,KAAA,IAAA,EAAA,SAAA;AACE,eAAA,YAAA,KAAA,IAAA,EAAA,UAAA,CAAA;AAAA,QACF;;AAEE,eAAA,YAAA,KAAA,IAAA,EAAA,cAAA;AAAA,QACF;;AAKE,gBAAA,WAAA,KAAA,QAAA,QAAA,OAAA,EAAA;AACA,sBAAA,YAAA,KAAA;AACA,sBAAA,iBAAA;AAAA,QACF;;AAIA,aAAA,YAAA,KAAA,IAAA,EAAA;AAGA,aAAA,aAAA,IAAA,YAAA,IAAA,KAAA,YAAA,KAAA,IAAA,EAAA,QAAA,SAAA,CAAA;AAGA,aAAA,aAAA;;UAGE,MAAA,KAAA,YAAA,KAAA,IAAA,EAAA;AAAA;QAEF,CAAA;AAAA,MACF;;AAKE,YAAA,KAAA,WAAA,KAAA,YAAA,WAAA,GAAA;AACE,eAAA,UAAA;AACA,eAAA,cAAA;;QAEF;;;AAOA,aAAA,YAAA,QAAA,WAAA;AAGA,aAAA,aAAA;;UAGE,QAAA,KAAA,YAAA;AAAA;QAEF,CAAA;AAAA,MACF;AAAA;;IAGF,iBAAA,GAAA;;;;;;4DAUM,YAAA,OAAA,KAAA,eAAA,QAAA;;MAEJ;AAAA;;IAIF,kBAAA;;;AAIE,WAAA,gBAAA,WAAA,MAAA;;AAII,eAAA,qBAAA,KAAA,OAAA,GAAA,IAAA;AAAA,QACF;AAAA,MACF,GAAA,GAAA;AAAA;;IAGF,YAAA,OAAA,iBAAA,QAAA;;AACEA,oBAAA,MAAA,MAAA,SAAA,kCAAA,SAAA,KAAA;;;;MAOA,WAEA,SAAA,OAAA,UAAA,UAAA;AACE,kBAAA,MAAA,OAAA,MAAA,aAAA,WAAA,SAAA,mBAAA,QAAA;AAAA,MACF;;;;;;;AAUAA,oBAAA,MAAA,MAAA,OAAA,kCAAA,QAAA,KAAA,IAAA;AAEA,WAAA,YAAA;;AAGA,iBAAA,MAAA;;AAEE,YAAA,MAAA;;QAEA;AAAA,MACF,GAAA,GAAA;AAAA;;IAIF,cAAA,SAAA,SAAA;AAEE,UAAA,KAAA,aAAA,IAAA,OAAA,GAAA;AACE,eAAA,KAAA,aAAA,IAAA,OAAA;AAAA,MACF;AAGA,eAAA,IAAA,GAAA,IAAA,QAAA,QAAA,KAAA;;AAGI,eAAA,aAAA,IAAA,SAAA,CAAA;AACA,iBAAA;AAAA,QACF;AAAA,MACF;AACA,aAAA;AAAA;;;AAKA,UAAA,CAAA,WAAA,CAAA,QAAA;AAAA,eAAA,CAAA;AAGA,YAAA,SAAA,CAAA,GAAA,OAAA,EAAA,KAAA,CAAA,GAAA,MAAA;;;;;AASE,YAAA,OAAA;AAEA,YAAA;AAEE,kBAAA,IAAA,KAAA,EAAA,WAAA,EAAA,QAAA;AAAA,QACF,SAAA,GAAA;AACE,kBAAA;AAAA,QACF;AAEA,YAAA;AAEE,kBAAA,IAAA,KAAA,EAAA,WAAA,EAAA,QAAA;AAAA,QACF,SAAA,GAAA;AACE,kBAAA;AAAA,QACF;AAGA,YAAA,CAAA,MAAA,KAAA,KAAA,CAAA,MAAA,KAAA,GAAA;AACE,iBAAA,QAAA;AAAA;;QAIF;AAAA,MACF,CAAA;;;;;;;AASE,YAAA;;;UAIE;;;QAYF,SAAA,GAAA;AACEA,wBAAA,MAAA,MAAA,SAAA,kCAAA,kBAAA,CAAA;;;QAGF;AAAA,MACF;AAAA;;IAGF,YAAA,MAAA;AAEE,UAAA,MAAA;;AAIE,cAAA;AACAA,sBAAA,MAAA,MAAA,OAAA,kCAAA,cAAA,GAAA;AAAA,MACF,WAEA,QAAA,OAAA,SAAA,UAAA;;AAGEA,4BAAA,MAAA,OAAA,kCAAA,gBAAA,KAAA,QAAA,IAAA;AAAA,MACF;AAGA,UAAA,KAAA;;AAGIA,wBAAA,MAAA,MAAA,OAAA,kCAAA,aAAA,GAAA;AACA,gBAAA,WAAA;AAAA,QACF;;;AAMIA,0BAAA,MAAA,MAAA,OAAA,kCAAA,aAAA,GAAA;AACA,mBAAA;AAAA,UACF;;AAIEA,0BAAA,MAAA,MAAA,OAAA,kCAAA,WAAA,GAAA;;UAEF;AAAA,QACF;AAAA,MACF;;AAIAA,oBAAA,MAAA,MAAA,OAAA,kCAAA,YAAA,QAAA;AACA,aAAA;AAAA;;IAGF,mBAAA;;;AAGM,gBAAA,aAAA,KAAA,SAAA,OAAA,CAAA;AACA,cAAA,OAAA,eAAA,UAAA;AACE,mBAAA;AAAA;AAEA,mBAAA,WAAA;AAAA,UACF;AAAA,QACF;AACA,eAAA;AAAA,MACF,WAAA,KAAA,SAAA,QAAA,GAAA;AACE,eAAA,KAAA,SAAA,eAAA;AAAA,MACF,WAAA,KAAA,SAAA,QAAA,GAAA;AACE,eAAA,KAAA,SAAA,eAAA;AAAA,MACF;AACA,aAAA;AAAA;;;AAIA,WAAA,UAAA;AACA,iBAAA,MAAA;AACE,aAAA,YAAA,CAAA,KAAA;AAEA,YAAA,KAAA,WAAA;AACE,cAAA;;AAEE,gBAAA,gBAAA;AACE,mBAAA,eAAA,KAAA,MAAA,cAAA;AAAA,YACF;AAAA,UACF,SAAA,GAAA;AACEA,0BAAA,MAAA,MAAA,SAAA,kCAAA,cAAA,CAAA;AAAA,UACF;AAAA,QACF;AAAA,MACF,GAAA,GAAA;AAAA;;IAGF,YAAA,OAAA;;;AAMI,aAAA,aAAA,QAAA,KAAA;AACA,YAAA,KAAA,aAAA,SAAA,IAAA;;QAEA;AAAA,MACF;AAGA,UAAA;AACEA,sBAAA,MAAA,eAAA,iBAAA,KAAA,UAAA,KAAA,YAAA,CAAA;AAAA,MACF,SAAA,GAAA;AACEA,sBAAA,MAAA,MAAA,SAAA,kCAAA,YAAA,CAAA;AAAA,MACF;AAAA;;IAGF,UAAA,KAAA;AAEEA,oBAAA,MAAA,MAAA,OAAA,kCAAA,YAAA,GAAA;AAEAA,oBAAAA,MAAA,UAAA;AAAA,QACE,OAAA;AAAA;MAEF,CAAA;AAAA;;IAGF,oBAAA,aAAA;AACE,UAAA,KAAA;AAAA;;AAIA,YAAA,UAAA,YAAA,QAAA,KAAA;;AAIA,UAAA,CAAA,WAAA,CAAA,OAAA;AACE,aAAA,sBAAA;AACA;AAAA,MACF;AAGA,YAAA,gBAAA;AAAA,QACE,MAAA;AAAA;AAAA;QAEA;AAAA;AAAA;;QAEA,OAAA,SAAA;AAAA;AAAA;AAGFA,oBAAA,MAAA,MAAA,OAAA,kCAAA,WAAA,aAAA;;AAKIA,sBAAA,MAAA,MAAA,OAAA,kCAAA,WAAA,GAAA;AAEA,YAAA;AACE,cAAA,IAAA,WAAA,KAAA;;AAKE,gBAAAU,eAAA,IAAA,QAAA,KAAA,yBAAA,SAAA,KAAA;AAEAV,0BAAA,MAAA,MAAA,OAAA,kCAAA,YAAAU,YAAA;;AAKE,oBAAA,gBAAA,KAAA,YAAA,KAAA,UAAA,KAAA,OAAA,KAAA,IAAA;AACA,kBAAA,eAAA;;;gBAIE;AAGA,8BAAA,QAAA,QAAA;AAAA,kBACE,IAAAA,aAAA;AAAA;kBAEA,KAAA,KAAA;AAAA;;kBAGA;AAAA,kBACA;AAAA;AAAA,kBACA,aAAA;AAAA,kBACA,UAAA,KAAA,SAAA,YAAA;AAAA,kBACA,OAAA;AAAA;kBAEA,QAAA;AAAA,kBACA,WAAA,KAAA;AAAA,kBACA,eAAA,KAAA,iBAAA,KAAA,cAAA,WAAA,KAAA,cAAA,WAAA,QAAA,KAAA,cAAA,WAAA,MAAA;AAAA,gBACF,CAAA;;cAIF;AAAA;;gBAIE,IAAAA,aAAA;AAAA,gBACA,KAAA,KAAA;AAAA;;gBAGA;AAAA,gBACA;AAAA;AAAA,gBACA,aAAA;AAAA,gBACA,UAAA,KAAA,SAAA,YAAA;AAAA,gBACA,OAAA;AAAA;gBAEA,QAAA;AAAA;;cAGF,CAAA;AAAA,YACF;AAGA,iBAAA,aAAA;;AAMA,gBAAA,KAAA,SAAA;AACE,mBAAA,UAAA;AAAA,YACF;AAAA;AAGA,iBAAA,YAAA,IAAA,OAAA,UAAA;AAAA,UACF;AAAA;AAEAV,wBAAA,MAAA,MAAA,SAAA,kCAAA,cAAA,KAAA;AACA,eAAA,YAAA,YAAA;AAAA,QACF;AAAA;AAIAA,sBAAA,MAAA,MAAA,SAAA,kCAAA,WAAA,GAAA;AACA,aAAA,YAAA,KAAA,UAAA;AAAA;AAGA,aAAA,sBAAA;AAAA,MACF,CAAA;AAAA;;IAIJ,eAAA;;AAGI;AAAA,MACF;;AAIE,qBAAA,KAAA,gBAAA;;MAEF;AAGA,WAAA,YAAA;AACA,WAAA,UAAA;AAGA,WAAA,UAAA;;;;IAIF,eAAA;AACE,WAAA,MAAA,YAAA;;;;;AAOF,UAAA,CAAA;AAAA,eAAA;AAEA,UAAA;AAEE,YAAA,cAAA,QAAA,QAAA,sBAAA,CAAA,MAAA,UAAA;AAEE,gBAAA,QAAAW,2BAAAA,UAAA,KAAA,OAAA,EAAA,WAAA,IAAA;AAEA,cAAA,OAAA;;UAGA;;QAIF,CAAA;;;AAKAX,sBAAA,MAAA,MAAA,SAAA,kCAAA,WAAA,KAAA;;MAGF;AAAA;;;AAKE,UAAA,CAAA,WAAA,CAAA,QAAA;AAAA,eAAA;;AAMA,iBAAA,gBAAA,KAAA,kBAAA,WAAA,OAAA;AAEA,aAAA;AAAA;;;EAKJ,gBAAA;AAEE,QAAA,CAAA,KAAA,wEAGI,KAAA;AAGF,WAAA,OAAA,KAAA,OAAA;;AAEA,WAAA,eAAA;AAAA,IACF;AAAA;;;AAIA,WAAA;AAAA;MAEE,UAAA,KAAA,iBAAA;AAAA;IAEF;AAAA;;EAGF,kBAAA;AACE,WAAA;AAAA;MAEE,UAAA,KAAA,iBAAA;AAAA;IAEF;AAAA;;;;;;;EASF,SAAA;;;;AAQI,WAAA,WAAA;AAAA,IACF;AAAA;AAKJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACj1HA,GAAG,WAAW,eAAe;"}