<template>
  <view class="empty-page">
    <view class="empty-content">
      <image 
        :src="image" 
        mode="aspectFit" 
        class="empty-image"
        :style="{ width: imageSize, height: imageSize }"
      />
      <view class="empty-title">{{ title }}</view>
      <view v-if="description" class="empty-description">{{ description }}</view>
      <view v-if="buttonText" class="empty-button" @tap="handleButtonClick">
        {{ buttonText }}
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'EmptyPage',
  props: {
    // 标题
    title: {
      type: String,
      default: '暂无数据'
    },
    // 描述文字
    description: {
      type: String,
      default: ''
    },
    // 图片地址
    image: {
      type: String,
      default: '/static/img/empty.png'
    },
    // 图片尺寸
    imageSize: {
      type: String,
      default: '280rpx'
    },
    // 按钮文字
    buttonText: {
      type: String,
      default: ''
    }
  },
  methods: {
    handleButtonClick() {
      this.$emit('buttonClick');
    }
  }
}
</script>

<style scoped>
.empty-page {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400rpx;
  padding: 60rpx 40rpx;
}

.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.empty-image {
  margin-bottom: 40rpx;
  opacity: 0.6;
}

.empty-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
  line-height: 1.4;
}

.empty-description {
  font-size: 26rpx;
  color: #999;
  line-height: 1.5;
  margin-bottom: 40rpx;
  max-width: 400rpx;
}

.empty-button {
  padding: 20rpx 40rpx;
  background: #007aff;
  color: #fff;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.empty-button:active {
  background: #0056cc;
  transform: scale(0.95);
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .empty-title {
    color: #fff;
  }
  
  .empty-description {
    color: #ccc;
  }
}
</style>
