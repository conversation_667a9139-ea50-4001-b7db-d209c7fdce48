{"version": 3, "file": "index.js", "sources": ["pages/users/login/index.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvdXNlcnMvbG9naW4vaW5kZXgudnVl"], "sourcesContent": ["<template>\n\t<view class=\"container\">\n\t\t<navbar></navbar>\n\t\t<view class=\"content-box\" :style=\"{'margin-top': '30px'}\">\n\t\t\t<view class=\"logo-box\">\n\t\t\t\t<image :src=\"logoUrl\" />\n\t\t\t</view>\n\t\t\t<view class=\"login-box\">\n\t\t\t\t<view class=\"title\">{{ $t(`账号登录`) }}</view>\n\t\t\t\t<view class=\"sub-title\">{{ $t(`为了提供更好的服务，请登录您的账号`) }}</view>\n\t\t\t\t\n\t\t\t\t<view v-if=\"current !== 1\">\n\t\t\t\t\t<view class=\"input-item df\">\n\t\t\t\t\t\t<view class=\"icon-mobile\">\n\t\t\t\t\t\t\t<image src=\"/static/phone_1.png\"></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<input type=\"text\" maxlength=\"11\" :placeholder=\"$t(`输入手机号码`)\" v-model=\"account\" />\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"input-item df\">\n\t\t\t\t\t\t<view class=\"icon-code\">\n\t\t\t\t\t\t\t<image src=\"/static/img/sj.png\"></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<input type=\"password\" :placeholder=\"$t(`填写登录密码`)\" v-model=\"password\" />\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view v-if=\"current !== 0 || appLoginStatus || appleLoginStatus\">\n\t\t\t\t\t<view class=\"input-item df\">\n\t\t\t\t\t\t<view class=\"icon-mobile\">\n\t\t\t\t\t\t\t<image src=\"/static/phone_1.png\"></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<input type=\"text\" :placeholder=\"$t(`输入手机号码`)\" v-model=\"account\" :maxlength=\"11\" />\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"input-item df\">\n\t\t\t\t\t\t<view class=\"icon-code\">\n\t\t\t\t\t\t\t<image src=\"/static/code_2.png\"></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<input type=\"text\" :placeholder=\"$t(`填写验证码`)\" :maxlength=\"6\" v-model=\"captcha\" />\n\t\t\t\t\t\t<view :class=\"['send-code', disabled ? 'active' : '']\" @tap=\"code\">{{text}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"tips-text\" v-if=\"current !== 1\">* {{ $t(`忘记密码可以使用验证码登录`) }}</view>\n\t\t\t\t\n\t\t\t\t<view class=\"btn-submit df\" @tap=\"loginMobile\" v-if=\"current !== 0\">{{ $t(`登录`) }}</view>\n\t\t\t\t<view class=\"btn-submit df\" @tap=\"submit\" v-if=\"current === 0\">{{ $t(`登录`) }}</view>\n\t\t\t\t\n\t\t\t\t<!-- #ifndef APP-PLUS -->\n\t\t\t\t<view class=\"login-type-switch\">\n\t\t\t\t\t<view v-if=\"current == 0\" @tap=\"current = 1\">{{ $t(`验证码登录`) }}</view>\n\t\t\t\t\t<view v-if=\"current == 1\" @tap=\"current = 0\">{{ $t(`账号登录`) }}</view>\n\t\t\t\t</view>\n\t\t\t\t<!-- #endif -->\n\t\t\t\t\n\t\t\t\t<!-- #ifdef APP-PLUS -->\n\t\t\t\t<view class=\"appLogin\" v-if=\"!appLoginStatus && !appleLoginStatus\">\n\t\t\t\t\t<view class=\"other-login-title\">\n\t\t\t\t\t\t<view class=\"line\"></view>\n\t\t\t\t\t\t<view class=\"text\">{{ $t(`其他方式登录`) }}</view>\n\t\t\t\t\t\t<view class=\"line\"></view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"other-login-methods\">\n\t\t\t\t\t\t<view class=\"login-method\" @tap=\"wxLogin\">\n\t\t\t\t\t\t\t<view class=\"iconfont icon-s-weixindenglu1\"></view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"login-method\" v-if=\"current == 1\" @tap=\"current = 0\">\n\t\t\t\t\t\t\t<view class=\"iconfont icon-s-mimadenglu1\"></view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"login-method\" v-if=\"current == 0\" @tap=\"current = 1\">\n\t\t\t\t\t\t\t<view class=\"iconfont icon-s-yanzhengmadenglu1\"></view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"login-method apple\" @tap=\"appleLogin\" v-if=\"appleShow\">\n\t\t\t\t\t\t\t<view class=\"iconfont icon-s-pingguo\"></view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<!-- #endif -->\n\t\t\t\t\n\t\t\t\t<view class=\"agreement\">\n\t\t\t\t\t<checkbox-group @change=\"ChangeIsDefault\">\n\t\t\t\t\t\t<label class=\"df\">\n\t\t\t\t\t\t\t<checkbox :checked=\"protocol ? true : false\" :class=\"inAnimation ? 'trembling' : ''\" @animationend=\"inAnimation = false\" style=\"transform:scale(0.7)\"/>\n\t\t\t\t\t\t\t<view class=\"agreement-text\">\n\t\t\t\t\t\t\t\t{{ $t(`已阅读并同意`) }}\n\t\t\t\t\t\t\t\t<text @tap.stop=\"privacy(4)\">{{ $t(`《用户协议》`) }}</text>\n\t\t\t\t\t\t\t\t{{ $t(`与`) }}\n\t\t\t\t\t\t\t\t<text @tap.stop=\"privacy(3)\">{{ $t(`《隐私协议》`) }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</label>\n\t\t\t\t\t</checkbox-group>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<view class=\"bottom\">\n\t\t\t<view class=\"ver\" v-if=\"copyRight\">{{ copyRight }}</view>\n\t\t\t<view v-else class=\"ver\">\n\t\t\t\tCopyright ©2024 Zing！. All Rights\n\t\t\t</view>\n\t\t</view>\n\t\t<Verify @success=\"success\" :captchaType=\"captchaType\" :imgSize=\"{ width: '330px', height: '155px' }\" ref=\"verify\"></Verify>\n\t</view>\n</template>\n<script>\nimport sendVerifyCode from '@/mixins/SendVerifyCode';\nimport { loginH5, loginMobile, registerVerify, register, getCodeApi, getUserInfo, appleLogin } from '@/api/user';\nimport { getUserSocialInfo } from '@/api/social.js';\nimport attrs, { required, alpha_num, chs_phone } from '@/utils/validate';\nimport { getLogo } from '@/api/public';\n// import cookie from \"@/utils/store/cookie\";\nimport { VUE_APP_API_URL } from '@/utils';\n// #ifdef APP-PLUS\nimport { wechatAppAuth } from '@/api/api.js';\n// #endif\nconst BACK_URL = 'login_back_url';\nimport colors from '@/mixins/color.js';\nimport Verify from '../components/verify/index.vue';\nexport default {\n\tname: 'Login',\n\tcomponents: {\n\t\tVerify\n\t},\n\tmixins: [sendVerifyCode, colors],\n\tdata: function () {\n\t\treturn {\n\t\t\tcopyRight: '',\n\t\t\tinAnimation: false,\n\t\t\tprotocol: false,\n\t\t\tnavList: [this.$t(`快速登录`), this.$t(`账号登录`)],\n\t\t\tcurrent: 1,\n\t\t\taccount: '',\n\t\t\tpassword: '',\n\t\t\tcaptcha: '',\n\t\t\tformItem: 1,\n\t\t\ttype: 'login',\n\t\t\tlogoUrl: '',\n\t\t\tkeyCode: '',\n\t\t\tcodeUrl: '',\n\t\t\tcodeVal: '',\n\t\t\tisShowCode: false,\n\t\t\tappLoginStatus: false, // 微信登录强制绑定手机号码状态\n\t\t\tappUserInfo: null, // 微信登录保存的用户信息\n\t\t\tappleLoginStatus: false, // 微信登录强制绑定手机号码状态\n\t\t\tappleUserInfo: null,\n\t\t\tappleShow: false, // 苹果登录版本必须要求ios13以上的\n\t\t\tkeyLock: true,\n\t\t\t// 添加导航防抖标志\n\t\t\tisNavigating: false\n\t\t};\n\t},\n\twatch: {\n\t\tformItem: function (nval, oVal) {\n\t\t\tif (nval == 1) {\n\t\t\t\tthis.type = 'login';\n\t\t\t} else {\n\t\t\t\tthis.type = 'register';\n\t\t\t}\n\t\t}\n\t},\n\tonLoad() {\n\t\tlet self = this;\n\t\tuni.getSystemInfo({\n\t\t\tsuccess: (res) => {\n\t\t\t\tif (res.platform.toLowerCase() == 'ios' && this.getSystem(res.system)) {\n\t\t\t\t\tself.appleShow = true;\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\t\tif (uni.getStorageSync('copyRight').copyrightContext) {\n\t\t\tthis.copyRight = uni.getStorageSync('copyRight').copyrightContext;\n\t\t}\n\t},\n\tmounted() {\n\t\t// this.getCode();\n\t\tthis.getLogoImage();\n\t},\n\tmethods: {\n\t\tChangeIsDefault(e) {\n\t\t\tthis.$set(this, 'protocol', !this.protocol);\n\t\t},\n\t\tprivacy(type) {\n\t\t\t// 防抖机制，避免快速连续点击\n\t\t\tif (this.isNavigating) {\n\t\t\t\tconsole.log('正在导航中，忽略重复点击');\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tthis.isNavigating = true;\n\n\t\t\t// 延迟执行，避免与其他导航冲突\n\t\t\tsetTimeout(() => {\n\t\t\t\ttry {\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl: '/pages/users/privacy/index?type=' + type,\n\t\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\t\tconsole.log('导航成功');\n\t\t\t\t\t\t\t// 导航成功后重置标志\n\t\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\t\tthis.isNavigating = false;\n\t\t\t\t\t\t\t}, 1500);\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfail: (error) => {\n\t\t\t\t\t\t\tconsole.error('导航失败:', error);\n\t\t\t\t\t\t\tthis.isNavigating = false;\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '页面跳转失败',\n\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('导航错误:', error);\n\t\t\t\t\tthis.isNavigating = false;\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '页面跳转失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}, 200); // 延迟200ms执行\n\t\t},\n\t\t// IOS 版本号判断\n\t\tgetSystem(system) {\n\t\t\tlet str;\n\t\t\tsystem.toLowerCase().indexOf('ios') === -1 ? (str = system) : (str = system.split(' ')[1]);\n\t\t\tif (str.indexOf('.')) return str.split('.')[0] >= 13;\n\t\t\treturn str >= 13;\n\t\t},\n\t\t// 苹果登录\n\t\tappleLogin() {\n\t\t\tlet self = this;\n\t\t\tthis.account = '';\n\t\t\tthis.captcha = '';\n\t\t\tif (!self.protocol) {\n\t\t\t\tthis.inAnimation = true;\n\t\t\t\treturn self.$util.Tips({\n\t\t\t\t\ttitle: '请先阅读并同意协议'\n\t\t\t\t});\n\t\t\t}\n\t\t\tuni.showLoading({\n\t\t\t\ttitle: this.$t(`登录中`)\n\t\t\t});\n\t\t\tuni.login({\n\t\t\t\tprovider: 'apple',\n\t\t\t\ttimeout: 10000,\n\t\t\t\tsuccess(loginRes) {\n\t\t\t\t\tuni.getUserInfo({\n\t\t\t\t\t\tprovider: 'apple',\n\t\t\t\t\t\tsuccess: function (infoRes) {\n\t\t\t\t\t\t\tself.appleUserInfo = infoRes.userInfo;\n\t\t\t\t\t\t\tself.appleLoginApi();\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfail() {\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: self.$t(`获取用户信息失败`),\n\t\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t},\n\t\t\t\t\t\tcomplete() {\n\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t},\n\t\t\t\tfail(error) {\n\t\t\t\t\tconsole.log(error);\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t// 苹果登录Api\n\t\tappleLoginApi() {\n\t\t\tlet self = this;\n\t\t\tappleLogin({\n\t\t\t\topenId: self.appleUserInfo.openId,\n\t\t\t\temail: self.appleUserInfo.email || '',\n\t\t\t\tphone: this.account,\n\t\t\t\tcaptcha: this.captcha\n\t\t\t})\n\t\t\t\t.then(({ data }) => {\n\t\t\t\t\tif (data.isbind) {\n\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\ttitle: self.$t(`提示`),\n\t\t\t\t\t\t\tcontent: self.$t(`请绑定手机号后，继续操作`),\n\t\t\t\t\t\t\tshowCancel: false,\n\t\t\t\t\t\t\tsuccess: function (res) {\n\t\t\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\t\t\tself.current = 1;\n\t\t\t\t\t\t\t\t\tself.appleLoginStatus = true;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t} else {\n\t\t\t\t\t\tself.$store.commit('LOGIN', {\n\t\t\t\t\t\t\ttoken: data.token,\n\t\t\t\t\t\t\ttime: data.expires_time - self.$Cache.time()\n\t\t\t\t\t\t});\n\t\t\t\t\t\tlet backUrl = self.$Cache.get(BACK_URL) || '/pages/tabbar/center';\n\t\t\t\t\t\tself.$Cache.clear(BACK_URL);\n\t\t\t\t\t\tself.$store.commit('SETUID', data.userInfo.uid);\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 先获取基础用户信息\n\t\t\t\t\t\tgetUserInfo().then(res => {\n\t\t\t\t\t\t\t// 再获取用户社交信息（拓展信息）\n\t\t\t\t\t\t\tgetUserSocialInfo()\n\t\t\t\t\t\t\t\t.then((socialRes) => {\n\t\t\t\t\t\t\t\t\t// 更新用户社交信息到Vuex\n\t\t\t\t\t\t\t\t\tif (socialRes.data) {\n\t\t\t\t\t\t\t\t\t\tself.$store.commit('UPDATE_USERINFO', socialRes.data);\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t// 完成后跳转\n\t\t\t\t\t\t\t\t\tuni.reLaunch({\n\t\t\t\t\t\t\t\t\t\turl: backUrl\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\t.catch(() => {\n\t\t\t\t\t\t\t\t\t// 即使社交信息获取失败，仍然跳转\n\t\t\t\t\t\t\t\t\tuni.reLaunch({\n\t\t\t\t\t\t\t\t\t\turl: backUrl\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}).catch(() => {\n\t\t\t\t\t\t\t// 即使基础信息获取失败，仍然跳转\n\t\t\t\t\t\t\tuni.reLaunch({\n\t\t\t\t\t\t\t\turl: backUrl\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t\t.catch((error) => {\n\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\ttitle: self.$t(`提示`),\n\t\t\t\t\t\tcontent: self.$t(`错误信息`) + `${error}`,\n\t\t\t\t\t\tsuccess: function (res) {\n\t\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\t\tconsole.log(self.$t(`用户点击确定`));\n\t\t\t\t\t\t\t} else if (res.cancel) {\n\t\t\t\t\t\t\t\tconsole.log(self.$t(`用户点击取消`));\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t},\n\t\t// App微信登录\n\t\twxLogin() {\n\t\t\tlet self = this;\n\t\t\tthis.account = '';\n\t\t\tthis.captcha = '';\n\t\t\tif (!self.protocol) {\n\t\t\t\tthis.inAnimation = true;\n\t\t\t\treturn self.$util.Tips({\n\t\t\t\t\ttitle: '请先阅读并同意协议'\n\t\t\t\t});\n\t\t\t}\n\t\t\tuni.showLoading({\n\t\t\t\ttitle: self.$t(`登录中`)\n\t\t\t});\n\t\t\tuni.login({\n\t\t\t\tprovider: 'weixin',\n\t\t\t\tsuccess: function (loginRes) {\n\t\t\t\t\t// 获取用户信息\n\t\t\t\t\tuni.getUserInfo({\n\t\t\t\t\t\tprovider: 'weixin',\n\t\t\t\t\t\tsuccess: function (infoRes) {\n\t\t\t\t\t\t\tself.appUserInfo = infoRes.userInfo;\n\t\t\t\t\t\t\tself.wxLoginApi();\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfail() {\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: self.$t(`获取用户信息失败`),\n\t\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t},\n\t\t\t\t\t\tcomplete() {\n\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t},\n\t\t\t\tfail() {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: self.$t(`登录失败`),\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\n\t\twxLoginApi() {\n\t\t\tlet self = this;\n\t\t\twechatAppAuth({\n\t\t\t\tuserInfo: self.appUserInfo,\n\t\t\t\tphone: this.account,\n\t\t\t\tcode: this.captcha\n\t\t\t})\n\t\t\t\t.then(({ data }) => {\n\t\t\t\t\tif (data.isbind) {\n\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\ttitle: self.$t(`提示`),\n\t\t\t\t\t\t\tcontent: self.$t(`请绑定手机号后，继续操作`),\n\t\t\t\t\t\t\tshowCancel: false,\n\t\t\t\t\t\t\tsuccess: function (res) {\n\t\t\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\t\t\tself.current = 1;\n\t\t\t\t\t\t\t\t\tself.appLoginStatus = true;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t} else {\n\t\t\t\t\t\tself.$store.commit('LOGIN', {\n\t\t\t\t\t\t\ttoken: data.token,\n\t\t\t\t\t\t\ttime: data.expires_time - self.$Cache.time()\n\t\t\t\t\t\t});\n\t\t\t\t\t\tlet backUrl = self.$Cache.get(BACK_URL) || '/pages/index/index';\n\t\t\t\t\t\tself.$Cache.clear(BACK_URL);\n\t\t\t\t\t\tself.$store.commit('SETUID', data.userInfo.uid);\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 先获取基础用户信息\n\t\t\t\t\t\tgetUserInfo().then(res => {\n\t\t\t\t\t\t\t// 再获取用户社交信息（拓展信息）\n\t\t\t\t\t\t\tgetUserSocialInfo()\n\t\t\t\t\t\t\t\t.then((socialRes) => {\n\t\t\t\t\t\t\t\t\t// 更新用户社交信息到Vuex\n\t\t\t\t\t\t\t\t\tif (socialRes.data) {\n\t\t\t\t\t\t\t\t\t\tself.$store.commit('UPDATE_USERINFO', socialRes.data);\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t// 完成后跳转\n\t\t\t\t\t\t\t\t\tuni.reLaunch({\n\t\t\t\t\t\t\t\t\t\turl: backUrl\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\t.catch(() => {\n\t\t\t\t\t\t\t\t\t// 即使社交信息获取失败，仍然跳转\n\t\t\t\t\t\t\t\t\tuni.reLaunch({\n\t\t\t\t\t\t\t\t\t\turl: backUrl\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}).catch(() => {\n\t\t\t\t\t\t\t// 即使基础信息获取失败，仍然跳转\n\t\t\t\t\t\t\tuni.reLaunch({\n\t\t\t\t\t\t\t\turl: backUrl\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t\t.catch((error) => {\n\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\ttitle: self.$t(`提示`),\n\t\t\t\t\t\tcontent: self.$t(`错误信息`) + `${error}`,\n\t\t\t\t\t\tsuccess: function (res) {\n\t\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\t\tconsole.log(self.$t(`用户点击确定`));\n\t\t\t\t\t\t\t} else if (res.cancel) {\n\t\t\t\t\t\t\t\tconsole.log(self.$t(`用户点击取消`));\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t},\n\t\tagain() {\n\t\t\tthis.codeUrl = VUE_APP_API_URL + '/sms_captcha?' + 'key=' + this.keyCode + Date.parse(new Date());\n\t\t},\n\t\tsuccess(data) {\n\t\t\tthis.$refs.verify.hide();\n\t\t\tgetCodeApi()\n\t\t\t\t.then((res) => {\n\t\t\t\t\tthis.keyCode = res.data.key;\n\t\t\t\t\tthis.getCode(data);\n\t\t\t\t})\n\t\t\t\t.catch((res) => {\n\t\t\t\t\tthis.$util.Tips({\n\t\t\t\t\t\ttitle: res\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t},\n\t\tcode() {\n\t\t\tlet that = this;\n\t\t\tif (!that.protocol) {\n\t\t\t\tthis.inAnimation = true;\n\t\t\t\treturn that.$util.Tips({\n\t\t\t\t\ttitle: '请先阅读并同意协议'\n\t\t\t\t});\n\t\t\t}\n\t\t\tif (!that.account)\n\t\t\t\treturn that.$util.Tips({\n\t\t\t\t\ttitle: that.$t(`请填写手机号码`)\n\t\t\t\t});\n\t\t\tif (!/^1(3|4|5|7|8|9|6)\\d{9}$/i.test(that.account))\n\t\t\t\treturn that.$util.Tips({\n\t\t\t\t\ttitle: that.$t(`请输入正确的手机号码`)\n\t\t\t\t});\n\t\t\tthis.$refs.verify.show();\n\t\t},\n\t\tasync getLogoImage() {\n\t\t\tlet that = this;\n\t\t\tgetLogo(2).then((res) => {\n\t\t\t\tthat.logoUrl = res.data.logo_url;\n\t\t\t});\n\t\t},\n\t\tasync loginMobile() {\n\t\t\tlet that = this;\n\t\t\tif (!that.protocol) {\n\t\t\t\tthis.inAnimation = true;\n\t\t\t\treturn that.$util.Tips({\n\t\t\t\t\ttitle: '请先阅读并同意协议'\n\t\t\t\t});\n\t\t\t}\n\t\t\tif (!that.account)\n\t\t\t\treturn that.$util.Tips({\n\t\t\t\t\ttitle: that.$t(`请填写手机号码`)\n\t\t\t\t});\n\t\t\tif (!/^1(3|4|5|7|8|9|6)\\d{9}$/i.test(that.account))\n\t\t\t\treturn that.$util.Tips({\n\t\t\t\t\ttitle: that.$t(`请输入正确的手机号码`)\n\t\t\t\t});\n\t\t\tif (!that.captcha)\n\t\t\t\treturn that.$util.Tips({\n\t\t\t\t\ttitle: that.$t(`请填写验证码`)\n\t\t\t\t});\n\t\t\tif (!/^[\\w\\d]+$/i.test(that.captcha))\n\t\t\t\treturn that.$util.Tips({\n\t\t\t\t\ttitle: that.$t(`请输入正确的验证码`)\n\t\t\t\t});\n\t\t\tif (that.appLoginStatus) {\n\t\t\t\tthat.wxLoginApi();\n\t\t\t} else if (that.appleLoginStatus) {\n\t\t\t\tthat.appleLoginApi();\n\t\t\t} else {\n\t\t\t\tif (this.keyLock) {\n\t\t\t\t\tthis.keyLock = !this.keyLock;\n\t\t\t\t} else {\n\t\t\t\t\treturn that.$util.Tips({\n\t\t\t\t\t\ttitle: that.$t(`请勿重复点击`)\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\tloginMobile({\n\t\t\t\t\tphone: that.account,\n\t\t\t\t\tcaptcha: that.captcha,\n\t\t\t\t\tspread: that.$Cache.get('spread')\n\t\t\t\t})\n\t\t\t\t\t.then((res) => {\n\t\t\t\t\t\tlet data = res.data;\n\t\t\t\t\t\tthat.$store.commit('LOGIN', {\n\t\t\t\t\t\t\ttoken: data.token,\n\t\t\t\t\t\t\ttime: data.expires_time - this.$Cache.time()\n\t\t\t\t\t\t});\n\t\t\t\t\t\tlet backUrl = that.$Cache.get(BACK_URL) || '/pages/index/index';\n\t\t\t\t\t\tthat.$Cache.clear(BACK_URL);\n\t\t\t\t\t\t// 先获取基础用户信息\n\t\t\t\t\t\tgetUserInfo().then((res) => {\n\t\t\t\t\t\t\tthis.keyLock = true;\n\t\t\t\t\t\t\tthat.$store.commit('SETUID', res.data.uid);\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 获取用户社交信息（拓展信息）\n\t\t\t\t\t\t\tgetUserSocialInfo()\n\t\t\t\t\t\t\t\t.then((socialRes) => {\n\t\t\t\t\t\t\t\t\t// 更新用户社交信息到Vuex\n\t\t\t\t\t\t\t\t\tif (socialRes.data) {\n\t\t\t\t\t\t\t\t\t\tthat.$store.commit('UPDATE_USERINFO', socialRes.data);\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t// 处理跳转URL\n\t\t\t\t\t\t\t\t\tif (backUrl.indexOf('/pages/users/login/index') !== -1) {\n\t\t\t\t\t\t\t\t\t\tbackUrl = '/pages/index/index';\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t// 完成后跳转\n\t\t\t\t\t\t\t\t\tuni.reLaunch({\n\t\t\t\t\t\t\t\t\t\turl: backUrl\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\t.catch(() => {\n\t\t\t\t\t\t\t\t\t// 即使社交信息获取失败，仍然跳转\n\t\t\t\t\t\t\t\t\tif (backUrl.indexOf('/pages/users/login/index') !== -1) {\n\t\t\t\t\t\t\t\t\t\tbackUrl = '/pages/index/index';\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tuni.reLaunch({\n\t\t\t\t\t\t\t\t\t\turl: backUrl\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t});\n\t\t\t\t\t})\n\t\t\t\t\t.catch((res) => {\n\t\t\t\t\t\tthis.keyLock = true;\n\t\t\t\t\t\tthat.$util.Tips({\n\t\t\t\t\t\t\ttitle: res\n\t\t\t\t\t\t});\n\t\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\tasync register() {\n\t\t\tlet that = this;\n\t\t\tif (!that.protocol) {\n\t\t\t\tthis.inAnimation = true;\n\t\t\t\treturn that.$util.Tips({\n\t\t\t\t\ttitle: '请先阅读并同意协议'\n\t\t\t\t});\n\t\t\t}\n\t\t\tif (!that.account)\n\t\t\t\treturn that.$util.Tips({\n\t\t\t\t\ttitle: that.$t(`请填写手机号码`)\n\t\t\t\t});\n\t\t\tif (!/^1(3|4|5|7|8|9|6)\\d{9}$/i.test(that.account))\n\t\t\t\treturn that.$util.Tips({\n\t\t\t\t\ttitle: that.$t(`请输入正确的手机号码`)\n\t\t\t\t});\n\t\t\tif (!that.captcha)\n\t\t\t\treturn that.$util.Tips({\n\t\t\t\t\ttitle: that.$t(`请填写验证码`)\n\t\t\t\t});\n\t\t\tif (!/^[\\w\\d]+$/i.test(that.captcha))\n\t\t\t\treturn that.$util.Tips({\n\t\t\t\t\ttitle: that.$t(`请输入正确的验证码`)\n\t\t\t\t});\n\t\t\tif (!that.password)\n\t\t\t\treturn that.$util.Tips({\n\t\t\t\t\ttitle: that.$t(`请填写密码`)\n\t\t\t\t});\n\t\t\tif (/^([0-9]|[a-z]|[A-Z]){0,6}$/i.test(that.password))\n\t\t\t\treturn that.$util.Tips({\n\t\t\t\t\ttitle: that.$t(`您输入的密码过于简单`)\n\t\t\t\t});\n\t\t\tregister({\n\t\t\t\taccount: that.account,\n\t\t\t\tcaptcha: that.captcha,\n\t\t\t\tpassword: that.password,\n\t\t\t\tspread: that.$Cache.get('spread')\n\t\t\t})\n\t\t\t\t.then((res) => {\n\t\t\t\t\tthat.$util.Tips({\n\t\t\t\t\t\ttitle: res\n\t\t\t\t\t});\n\t\t\t\t\tthat.formItem = 1;\n\t\t\t\t})\n\t\t\t\t.catch((res) => {\n\t\t\t\t\tthat.$util.Tips({\n\t\t\t\t\t\ttitle: res\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t},\n\t\tasync getCode(data) {\n\t\t\tlet that = this;\n\t\t\tif (!that.protocol) {\n\t\t\t\tthis.inAnimation = true;\n\t\t\t\treturn that.$util.Tips({\n\t\t\t\t\ttitle: '请先阅读并同意协议'\n\t\t\t\t});\n\t\t\t}\n\t\t\tif (!that.account)\n\t\t\t\treturn that.$util.Tips({\n\t\t\t\t\ttitle: that.$t(`请填写手机号码`)\n\t\t\t\t});\n\t\t\tif (!/^1(3|4|5|7|8|9|6)\\d{9}$/i.test(that.account))\n\t\t\t\treturn that.$util.Tips({\n\t\t\t\t\ttitle: that.$t(`请输入正确的手机号码`)\n\t\t\t\t});\n\t\t\tif (that.formItem == 2) that.type = 'register';\n\n\t\t\tawait registerVerify({\n\t\t\t\tphone: that.account,\n\t\t\t\ttype: that.type,\n\t\t\t\tkey: that.keyCode,\n\t\t\t\tcaptchaType: this.captchaType,\n\t\t\t\tcaptchaVerification: data.captchaVerification\n\t\t\t})\n\t\t\t\t.then((res) => {\n\t\t\t\t\tthis.sendCode();\n\t\t\t\t\tthat.$util.Tips({\n\t\t\t\t\t\ttitle: res.msg\n\t\t\t\t\t});\n\t\t\t\t})\n\t\t\t\t.catch((res) => {\n\t\t\t\t\tthat.$util.Tips({\n\t\t\t\t\t\ttitle: res\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t},\n\t\tnavTap: function (index) {\n\t\t\tthis.current = index;\n\t\t},\n\t\tasync submit() {\n\t\t\tlet that = this;\n\t\t\tif (!that.protocol) {\n\t\t\t\tthis.inAnimation = true;\n\t\t\t\treturn that.$util.Tips({\n\t\t\t\t\ttitle: '请先阅读并同意协议'\n\t\t\t\t});\n\t\t\t}\n\t\t\tif (!that.account)\n\t\t\t\treturn that.$util.Tips({\n\t\t\t\t\ttitle: that.$t(`请填写账号`)\n\t\t\t\t});\n\t\t\tif (!/^[\\w\\d]{5,16}$/i.test(that.account))\n\t\t\t\treturn that.$util.Tips({\n\t\t\t\t\ttitle: that.$t(`请输入正确的账号`)\n\t\t\t\t});\n\t\t\tif (!that.password)\n\t\t\t\treturn that.$util.Tips({\n\t\t\t\t\ttitle: that.$t(`请填写密码`)\n\t\t\t\t});\n\t\t\tif (this.keyLock) {\n\t\t\t\tthis.keyLock = !this.keyLock;\n\t\t\t} else {\n\t\t\t\treturn that.$util.Tips({\n\t\t\t\t\ttitle: that.$t(`请勿重复点击`)\n\t\t\t\t});\n\t\t\t}\n\t\t\tloginH5({\n\t\t\t\taccount: that.account,\n\t\t\t\tpassword: that.password,\n\t\t\t\tspread: that.$Cache.get('spread')\n\t\t\t})\n\t\t\t\t.then(({ data }) => {\n\t\t\t\t\tthat.$store.commit('LOGIN', {\n\t\t\t\t\t\ttoken: data.token,\n\t\t\t\t\t\ttime: data.expires_time - this.$Cache.time()\n\t\t\t\t\t});\n\t\t\t\t\tlet backUrl = that.$Cache.get(BACK_URL) || '/pages/index/index';\n\t\t\t\t\tthat.$Cache.clear(BACK_URL);\n\t\t\t\t\t// 先获取基础用户信息\n\t\t\t\t\tgetUserInfo()\n\t\t\t\t\t\t.then((res) => {\n\t\t\t\t\t\t\tthis.keyLock = true;\n\t\t\t\t\t\t\tthat.$store.commit('SETUID', res.data.uid);\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 获取用户社交信息（拓展信息）\n\t\t\t\t\t\t\tgetUserSocialInfo()\n\t\t\t\t\t\t\t\t.then((socialRes) => {\n\t\t\t\t\t\t\t\t\t// 更新用户社交信息到Vuex\n\t\t\t\t\t\t\t\t\tif (socialRes.data) {\n\t\t\t\t\t\t\t\t\t\tthat.$store.commit('UPDATE_USERINFO', socialRes.data);\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t// 完成后跳转\n\t\t\t\t\t\t\t\t\tuni.reLaunch({\n\t\t\t\t\t\t\t\t\t\turl: backUrl\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\t.catch(() => {\n\t\t\t\t\t\t\t\t\t// 即使社交信息获取失败，仍然跳转\n\t\t\t\t\t\t\t\t\tuni.reLaunch({\n\t\t\t\t\t\t\t\t\t\turl: backUrl\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t})\n\t\t\t\t\t\t.catch((error) => {\n\t\t\t\t\t\t\tthis.keyLock = true;\n\t\t\t\t\t\t});\n\t\t\t\t})\n\t\t\t\t.catch((e) => {\n\t\t\t\t\tthis.keyLock = true;\n\t\t\t\t\tthat.$util.Tips({\n\t\t\t\t\t\ttitle: e\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t}\n\t}\n};\n</script>\n<style lang=\"scss\">\n.container {\n\twidth: 100%;\n\tbackground: #fff;\n\theight: 100vh;\n\toverflow: hidden;\n}\n\n.content-box {\n\twidth: 100%;\n}\n\n.logo-box {\n\twidth: 100%;\n\tdisplay: flex;\n\tjustify-content: center;\n\talign-items: center;\n\t/* #ifdef APP-VUE */\n\tmargin-top: 50rpx;\n\t/* #endif */\n\t/* #ifndef APP-VUE */\n\tmargin-top: 200rpx;\n\t/* #endif */\n\tmargin-bottom: 30rpx;\n}\n\n.logo-box image {\n\twidth: 240rpx;\n\theight: 240rpx;\n\tborder-radius: 20rpx;\n}\n\n.login-box {\n\twidth: calc(100% - 60rpx);\n\tmargin: 30rpx;\n}\n\n.login-box .title {\n\tfont-size: 40rpx;\n\tfont-weight: 700;\n\tcolor: #000;\n}\n\n.login-box .sub-title {\n\tmargin-top: 20rpx;\n\tfont-size: 26rpx;\n\tcolor: #999;\n}\n\n.login-box .input-item {\n\tmargin-top: 60rpx;\n\tpadding: 0 30rpx;\n\twidth: calc(100% - 60rpx);\n\theight: 100rpx;\n\tborder-radius: 100rpx;\n\tbackground: #f8f8f8;\n\tposition: relative;\n}\n\n.input-item .icon-mobile,\n.input-item .icon-code {\n\tmargin-right: 20rpx;\n\twidth: 36rpx;\n\theight: 36rpx;\n}\n\n.input-item .icon-mobile image,\n.input-item .icon-code image {\n\twidth: 100%;\n\theight: 100%;\n}\n\n.input-item input {\n\twidth: calc(100% - 56rpx);\n\theight: 100%;\n\tfont-size: 28rpx;\n}\n\n.input-item .send-code {\n\tposition: absolute;\n\tright: 30rpx;\n\twidth: 180rpx;\n\theight: 60rpx;\n\tline-height: 60rpx;\n\ttext-align: center;\n\tfont-size: 24rpx;\n\tfont-weight: 700;\n\tbackground: #000;\n\tcolor: #fff;\n\tborder-radius: 30rpx;\n}\n\n.input-item .send-code.active {\n\tbackground: #f5f5f5;\n\tcolor: #999;\n}\n\n.login-box .tips-text {\n\tmargin-top: 30rpx;\n\tfont-size: 24rpx;\n\tcolor: #999;\n}\n\n.login-box .btn-submit {\n\tmargin-top: 60rpx;\n\twidth: 100%;\n\theight: 100rpx;\n\tline-height: 100rpx;\n\ttext-align: center;\n\tfont-size: 28rpx;\n\tfont-weight: 700;\n\tbackground: #000;\n\tcolor: #fff;\n\tborder-radius: 100rpx;\n\tjustify-content: center;\n}\n\n.login-box .agreement {\n\tmargin-top: 30rpx;\n\tfont-size: 24rpx;\n\tcolor: #999;\n}\n\n.agreement .agreement-text {\n\tfont-size: 24rpx;\n\tcolor: #999;\n}\n\n.agreement-text text {\n\tcolor: #576b95;\n}\n\n.login-type-switch {\n\tmargin-top: 30rpx;\n\ttext-align: center;\n\tfont-size: 26rpx;\n\tcolor: #576b95;\n}\n\n.appLogin {\n\tmargin-top: 60rpx;\n}\n\n.other-login-title {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.other-login-title .line {\n\twidth: 68rpx;\n\theight: 1rpx;\n\tbackground: #ccc;\n}\n\n.other-login-title .text {\n\tmargin: 0 20rpx;\n\tfont-size: 24rpx;\n\tcolor: #b4b4b4;\n}\n\n.other-login-methods {\n\tdisplay: flex;\n\tjustify-content: center;\n\tmargin-top: 30rpx;\n}\n\n.login-method {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\twidth: 68rpx;\n\theight: 68rpx;\n\tborder-radius: 50%;\n\tmargin: 0 15rpx;\n}\n\n.login-method .iconfont {\n\tfont-size: 40rpx;\n\tcolor: #fff;\n}\n\n.login-method:nth-child(1) {\n\tbackground-color: #61c64f;\n}\n\n.login-method:nth-child(2),\n.login-method:nth-child(3) {\n\tbackground-color: #28b3e9;\n}\n\n.login-method.apple {\n\tbackground: #000;\n}\n\n.bottom {\n\tposition: fixed;\n\tbottom: 30rpx;\n\tleft: 0;\n\twidth: 100%;\n\tdisplay: flex;\n\tjustify-content: center;\n}\n\n.bottom .ver {\n\tfont-size: 20rpx;\n\tcolor: #999;\n}\n\n.bottom .ver a {\n\tcolor: #999;\n\ttext-decoration: none;\n}\n\n.tips-box {\n\tmargin: 0 auto;\n\tpadding: 30rpx;\n\tfont-size: 28rpx;\n\tcolor: #fff;\n\tbackground: rgba(0,0,0,.6);\n\tborder-radius: 12rpx;\n\tjustify-content: center;\n}\n\n.trembling {\n\tanimation: shake 0.6s;\n}\n\n@keyframes shake {\n\t0%, 100% { transform: translateX(0); }\n\t10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }\n\t20%, 40%, 60%, 80% { transform: translateX(5px); }\n}\n\n.df {\n\tdisplay: flex;\n\talign-items: center;\n}\n</style>\n", "import MiniProgramPage from 'Z:/WWW/shejiao/vue3/pages/users/login/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["sendVerifyCode", "colors", "uni", "appleLogin", "getUserInfo", "getUserSocialInfo", "VUE_APP_API_URL", "getCodeApi", "get<PERSON>ogo", "loginMobile", "res", "register", "registerVerify", "loginH5"], "mappings": ";;;;;;;;;AAoHA,MAAM,WAAW;AAEjB,eAAe,MAAW;AAC1B,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,YAAY;AAAA,IACX;AAAA,EACA;AAAA,EACD,QAAQ,CAACA,sBAAc,gBAAEC,mBAAM;AAAA,EAC/B,MAAM,WAAY;AACjB,WAAO;AAAA,MACN,WAAW;AAAA,MACX,aAAa;AAAA,MACb,UAAU;AAAA,MACV,SAAS,CAAC,KAAK,GAAG,MAAM,GAAG,KAAK,GAAG,MAAM,CAAC;AAAA,MAC1C,SAAS;AAAA,MACT,SAAS;AAAA,MACT,UAAU;AAAA,MACV,SAAS;AAAA,MACT,UAAU;AAAA,MACV,MAAM;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,gBAAgB;AAAA;AAAA,MAChB,aAAa;AAAA;AAAA,MACb,kBAAkB;AAAA;AAAA,MAClB,eAAe;AAAA,MACf,WAAW;AAAA;AAAA,MACX,SAAS;AAAA;AAAA,MAET,cAAc;AAAA;EAEf;AAAA,EACD,OAAO;AAAA,IACN,UAAU,SAAU,MAAM,MAAM;AAC/B,UAAI,QAAQ,GAAG;AACd,aAAK,OAAO;AAAA,aACN;AACN,aAAK,OAAO;AAAA,MACb;AAAA,IACD;AAAA,EACA;AAAA,EACD,SAAS;AACR,QAAI,OAAO;AACXC,kBAAAA,MAAI,cAAc;AAAA,MACjB,SAAS,CAAC,QAAQ;AACjB,YAAI,IAAI,SAAS,iBAAiB,SAAS,KAAK,UAAU,IAAI,MAAM,GAAG;AACtE,eAAK,YAAY;AAAA,QAClB;AAAA,MACD;AAAA,IACD,CAAC;AACD,QAAIA,oBAAI,eAAe,WAAW,EAAE,kBAAkB;AACrD,WAAK,YAAYA,cAAG,MAAC,eAAe,WAAW,EAAE;AAAA,IAClD;AAAA,EACA;AAAA,EACD,UAAU;AAET,SAAK,aAAY;AAAA,EACjB;AAAA,EACD,SAAS;AAAA,IACR,gBAAgB,GAAG;AAClB,WAAK,KAAK,MAAM,YAAY,CAAC,KAAK,QAAQ;AAAA,IAC1C;AAAA,IACD,QAAQ,MAAM;AAEb,UAAI,KAAK,cAAc;AACtBA,sBAAAA,yDAAY,cAAc;AAC1B;AAAA,MACD;AAEA,WAAK,eAAe;AAGpB,iBAAW,MAAM;AAChB,YAAI;AACHA,wBAAAA,MAAI,WAAW;AAAA,YACd,KAAK,qCAAqC;AAAA,YAC1C,SAAS,MAAM;AACdA,4BAAAA,MAAA,MAAA,OAAA,sCAAY,MAAM;AAElB,yBAAW,MAAM;AAChB,qBAAK,eAAe;AAAA,cACpB,GAAE,IAAI;AAAA,YACP;AAAA,YACD,MAAM,CAAC,UAAU;AAChBA,4BAAA,MAAA,MAAA,SAAA,sCAAc,SAAS,KAAK;AAC5B,mBAAK,eAAe;AACpBA,4BAAAA,MAAI,UAAU;AAAA,gBACb,OAAO;AAAA,gBACP,MAAM;AAAA,cACP,CAAC;AAAA,YACF;AAAA,UACD,CAAC;AAAA,QACA,SAAO,OAAO;AACfA,mFAAc,SAAS,KAAK;AAC5B,eAAK,eAAe;AACpBA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACP,CAAC;AAAA,QACF;AAAA,MACA,GAAE,GAAG;AAAA,IACN;AAAA;AAAA,IAED,UAAU,QAAQ;AACjB,UAAI;AACJ,aAAO,YAAW,EAAG,QAAQ,KAAK,MAAM,KAAM,MAAM,SAAW,MAAM,OAAO,MAAM,GAAG,EAAE,CAAC;AACxF,UAAI,IAAI,QAAQ,GAAG;AAAG,eAAO,IAAI,MAAM,GAAG,EAAE,CAAC,KAAK;AAClD,aAAO,OAAO;AAAA,IACd;AAAA;AAAA,IAED,aAAa;AACZ,UAAI,OAAO;AACX,WAAK,UAAU;AACf,WAAK,UAAU;AACf,UAAI,CAAC,KAAK,UAAU;AACnB,aAAK,cAAc;AACnB,eAAO,KAAK,MAAM,KAAK;AAAA,UACtB,OAAO;AAAA,QACR,CAAC;AAAA,MACF;AACAA,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO,KAAK,GAAG,KAAK;AAAA,MACrB,CAAC;AACDA,oBAAAA,MAAI,MAAM;AAAA,QACT,UAAU;AAAA,QACV,SAAS;AAAA,QACT,QAAQ,UAAU;AACjBA,wBAAAA,MAAI,YAAY;AAAA,YACf,UAAU;AAAA,YACV,SAAS,SAAU,SAAS;AAC3B,mBAAK,gBAAgB,QAAQ;AAC7B,mBAAK,cAAa;AAAA,YAClB;AAAA,YACD,OAAO;AACNA,4BAAAA,MAAI,UAAU;AAAA,gBACb,OAAO,KAAK,GAAG,UAAU;AAAA,gBACzB,MAAM;AAAA,gBACN,UAAU;AAAA,cACX,CAAC;AAAA,YACD;AAAA,YACD,WAAW;AACVA,4BAAG,MAAC,YAAW;AAAA,YAChB;AAAA,UACD,CAAC;AAAA,QACD;AAAA,QACD,KAAK,OAAO;AACXA,wBAAAA,MAAA,MAAA,OAAA,sCAAY,KAAK;AAAA,QAClB;AAAA,MACD,CAAC;AAAA,IACD;AAAA;AAAA,IAED,gBAAgB;AACf,UAAI,OAAO;AACXC,0BAAW;AAAA,QACV,QAAQ,KAAK,cAAc;AAAA,QAC3B,OAAO,KAAK,cAAc,SAAS;AAAA,QACnC,OAAO,KAAK;AAAA,QACZ,SAAS,KAAK;AAAA,OACd,EACC,KAAK,CAAC,EAAE,WAAW;AACnB,YAAI,KAAK,QAAQ;AAChBD,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO,KAAK,GAAG,IAAI;AAAA,YACnB,SAAS,KAAK,GAAG,cAAc;AAAA,YAC/B,YAAY;AAAA,YACZ,SAAS,SAAU,KAAK;AACvB,kBAAI,IAAI,SAAS;AAChB,qBAAK,UAAU;AACf,qBAAK,mBAAmB;AAAA,cACzB;AAAA,YACD;AAAA,UACD,CAAC;AAAA,eACK;AACN,eAAK,OAAO,OAAO,SAAS;AAAA,YAC3B,OAAO,KAAK;AAAA,YACZ,MAAM,KAAK,eAAe,KAAK,OAAO,KAAK;AAAA,UAC5C,CAAC;AACD,cAAI,UAAU,KAAK,OAAO,IAAI,QAAQ,KAAK;AAC3C,eAAK,OAAO,MAAM,QAAQ;AAC1B,eAAK,OAAO,OAAO,UAAU,KAAK,SAAS,GAAG;AAG9CE,+BAAa,EAAC,KAAK,SAAO;AAEzBC,yCAAkB,EAChB,KAAK,CAAC,cAAc;AAEpB,kBAAI,UAAU,MAAM;AACnB,qBAAK,OAAO,OAAO,mBAAmB,UAAU,IAAI;AAAA,cACrD;AAEAH,4BAAAA,MAAI,SAAS;AAAA,gBACZ,KAAK;AAAA,cACN,CAAC;AAAA,aACD,EACA,MAAM,MAAM;AAEZA,4BAAAA,MAAI,SAAS;AAAA,gBACZ,KAAK;AAAA,cACN,CAAC;AAAA,YACF,CAAC;AAAA,WACF,EAAE,MAAM,MAAM;AAEdA,0BAAAA,MAAI,SAAS;AAAA,cACZ,KAAK;AAAA,YACN,CAAC;AAAA,UACF,CAAC;AAAA,QACF;AAAA,OACA,EACA,MAAM,CAAC,UAAU;AACjBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO,KAAK,GAAG,IAAI;AAAA,UACnB,SAAS,KAAK,GAAG,MAAM,IAAI,GAAG,KAAK;AAAA,UACnC,SAAS,SAAU,KAAK;AACvB,gBAAI,IAAI,SAAS;AAChBA,kCAAY,MAAA,OAAA,sCAAA,KAAK,GAAG,QAAQ,CAAC;AAAA,YAC9B,WAAW,IAAI,QAAQ;AACtBA,kCAAY,MAAA,OAAA,sCAAA,KAAK,GAAG,QAAQ,CAAC;AAAA,YAC9B;AAAA,UACD;AAAA,QACD,CAAC;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAED,UAAU;AACT,UAAI,OAAO;AACX,WAAK,UAAU;AACf,WAAK,UAAU;AACf,UAAI,CAAC,KAAK,UAAU;AACnB,aAAK,cAAc;AACnB,eAAO,KAAK,MAAM,KAAK;AAAA,UACtB,OAAO;AAAA,QACR,CAAC;AAAA,MACF;AACAA,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO,KAAK,GAAG,KAAK;AAAA,MACrB,CAAC;AACDA,oBAAAA,MAAI,MAAM;AAAA,QACT,UAAU;AAAA,QACV,SAAS,SAAU,UAAU;AAE5BA,wBAAAA,MAAI,YAAY;AAAA,YACf,UAAU;AAAA,YACV,SAAS,SAAU,SAAS;AAC3B,mBAAK,cAAc,QAAQ;AAC3B,mBAAK,WAAU;AAAA,YACf;AAAA,YACD,OAAO;AACNA,4BAAAA,MAAI,UAAU;AAAA,gBACb,OAAO,KAAK,GAAG,UAAU;AAAA,gBACzB,MAAM;AAAA,gBACN,UAAU;AAAA,cACX,CAAC;AAAA,YACD;AAAA,YACD,WAAW;AACVA,4BAAG,MAAC,YAAW;AAAA,YAChB;AAAA,UACD,CAAC;AAAA,QACD;AAAA,QACD,OAAO;AACNA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO,KAAK,GAAG,MAAM;AAAA,YACrB,MAAM;AAAA,YACN,UAAU;AAAA,UACX,CAAC;AAAA,QACF;AAAA,MACD,CAAC;AAAA,IACD;AAAA,IAED,aAAa;AACZ,UAAI,OAAO;AACX,oBAAc;AAAA,QACb,UAAU,KAAK;AAAA,QACf,OAAO,KAAK;AAAA,QACZ,MAAM,KAAK;AAAA,OACX,EACC,KAAK,CAAC,EAAE,WAAW;AACnB,YAAI,KAAK,QAAQ;AAChBA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO,KAAK,GAAG,IAAI;AAAA,YACnB,SAAS,KAAK,GAAG,cAAc;AAAA,YAC/B,YAAY;AAAA,YACZ,SAAS,SAAU,KAAK;AACvB,kBAAI,IAAI,SAAS;AAChB,qBAAK,UAAU;AACf,qBAAK,iBAAiB;AAAA,cACvB;AAAA,YACD;AAAA,UACD,CAAC;AAAA,eACK;AACN,eAAK,OAAO,OAAO,SAAS;AAAA,YAC3B,OAAO,KAAK;AAAA,YACZ,MAAM,KAAK,eAAe,KAAK,OAAO,KAAK;AAAA,UAC5C,CAAC;AACD,cAAI,UAAU,KAAK,OAAO,IAAI,QAAQ,KAAK;AAC3C,eAAK,OAAO,MAAM,QAAQ;AAC1B,eAAK,OAAO,OAAO,UAAU,KAAK,SAAS,GAAG;AAG9CE,+BAAa,EAAC,KAAK,SAAO;AAEzBC,yCAAkB,EAChB,KAAK,CAAC,cAAc;AAEpB,kBAAI,UAAU,MAAM;AACnB,qBAAK,OAAO,OAAO,mBAAmB,UAAU,IAAI;AAAA,cACrD;AAEAH,4BAAAA,MAAI,SAAS;AAAA,gBACZ,KAAK;AAAA,cACN,CAAC;AAAA,aACD,EACA,MAAM,MAAM;AAEZA,4BAAAA,MAAI,SAAS;AAAA,gBACZ,KAAK;AAAA,cACN,CAAC;AAAA,YACF,CAAC;AAAA,WACF,EAAE,MAAM,MAAM;AAEdA,0BAAAA,MAAI,SAAS;AAAA,cACZ,KAAK;AAAA,YACN,CAAC;AAAA,UACF,CAAC;AAAA,QACF;AAAA,OACA,EACA,MAAM,CAAC,UAAU;AACjBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO,KAAK,GAAG,IAAI;AAAA,UACnB,SAAS,KAAK,GAAG,MAAM,IAAI,GAAG,KAAK;AAAA,UACnC,SAAS,SAAU,KAAK;AACvB,gBAAI,IAAI,SAAS;AAChBA,kCAAY,MAAA,OAAA,sCAAA,KAAK,GAAG,QAAQ,CAAC;AAAA,YAC9B,WAAW,IAAI,QAAQ;AACtBA,kCAAY,MAAA,OAAA,sCAAA,KAAK,GAAG,QAAQ,CAAC;AAAA,YAC9B;AAAA,UACD;AAAA,QACD,CAAC;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IACD,QAAQ;AACP,WAAK,UAAUI,8BAAkB,sBAA2B,KAAK,UAAU,KAAK,MAAM,oBAAI,KAAM,CAAA;AAAA,IAChG;AAAA,IACD,QAAQ,MAAM;AACb,WAAK,MAAM,OAAO;AAClBC,0BAAW,EACT,KAAK,CAAC,QAAQ;AACd,aAAK,UAAU,IAAI,KAAK;AACxB,aAAK,QAAQ,IAAI;AAAA,OACjB,EACA,MAAM,CAAC,QAAQ;AACf,aAAK,MAAM,KAAK;AAAA,UACf,OAAO;AAAA,QACR,CAAC;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IACD,OAAO;AACN,UAAI,OAAO;AACX,UAAI,CAAC,KAAK,UAAU;AACnB,aAAK,cAAc;AACnB,eAAO,KAAK,MAAM,KAAK;AAAA,UACtB,OAAO;AAAA,QACR,CAAC;AAAA,MACF;AACA,UAAI,CAAC,KAAK;AACT,eAAO,KAAK,MAAM,KAAK;AAAA,UACtB,OAAO,KAAK,GAAG,SAAS;AAAA,QACzB,CAAC;AACF,UAAI,CAAC,2BAA2B,KAAK,KAAK,OAAO;AAChD,eAAO,KAAK,MAAM,KAAK;AAAA,UACtB,OAAO,KAAK,GAAG,YAAY;AAAA,QAC5B,CAAC;AACF,WAAK,MAAM,OAAO;IAClB;AAAA,IACD,MAAM,eAAe;AACpB,UAAI,OAAO;AACXC,iBAAAA,QAAS,EAAE,KAAK,CAAC,QAAQ;AACxB,aAAK,UAAU,IAAI,KAAK;AAAA,MACzB,CAAC;AAAA,IACD;AAAA,IACD,MAAM,cAAc;AACnB,UAAI,OAAO;AACX,UAAI,CAAC,KAAK,UAAU;AACnB,aAAK,cAAc;AACnB,eAAO,KAAK,MAAM,KAAK;AAAA,UACtB,OAAO;AAAA,QACR,CAAC;AAAA,MACF;AACA,UAAI,CAAC,KAAK;AACT,eAAO,KAAK,MAAM,KAAK;AAAA,UACtB,OAAO,KAAK,GAAG,SAAS;AAAA,QACzB,CAAC;AACF,UAAI,CAAC,2BAA2B,KAAK,KAAK,OAAO;AAChD,eAAO,KAAK,MAAM,KAAK;AAAA,UACtB,OAAO,KAAK,GAAG,YAAY;AAAA,QAC5B,CAAC;AACF,UAAI,CAAC,KAAK;AACT,eAAO,KAAK,MAAM,KAAK;AAAA,UACtB,OAAO,KAAK,GAAG,QAAQ;AAAA,QACxB,CAAC;AACF,UAAI,CAAC,aAAa,KAAK,KAAK,OAAO;AAClC,eAAO,KAAK,MAAM,KAAK;AAAA,UACtB,OAAO,KAAK,GAAG,WAAW;AAAA,QAC3B,CAAC;AACF,UAAI,KAAK,gBAAgB;AACxB,aAAK,WAAU;AAAA,MAChB,WAAW,KAAK,kBAAkB;AACjC,aAAK,cAAa;AAAA,aACZ;AACN,YAAI,KAAK,SAAS;AACjB,eAAK,UAAU,CAAC,KAAK;AAAA,eACf;AACN,iBAAO,KAAK,MAAM,KAAK;AAAA,YACtB,OAAO,KAAK,GAAG,QAAQ;AAAA,UACxB,CAAC;AAAA,QACF;AACAC,6BAAY;AAAA,UACX,OAAO,KAAK;AAAA,UACZ,SAAS,KAAK;AAAA,UACd,QAAQ,KAAK,OAAO,IAAI,QAAQ;AAAA,SAChC,EACC,KAAK,CAAC,QAAQ;AACd,cAAI,OAAO,IAAI;AACf,eAAK,OAAO,OAAO,SAAS;AAAA,YAC3B,OAAO,KAAK;AAAA,YACZ,MAAM,KAAK,eAAe,KAAK,OAAO,KAAK;AAAA,UAC5C,CAAC;AACD,cAAI,UAAU,KAAK,OAAO,IAAI,QAAQ,KAAK;AAC3C,eAAK,OAAO,MAAM,QAAQ;AAE1BL,+BAAa,EAAC,KAAK,CAACM,SAAQ;AAC3B,iBAAK,UAAU;AACf,iBAAK,OAAO,OAAO,UAAUA,KAAI,KAAK,GAAG;AAGzCL,yCAAkB,EAChB,KAAK,CAAC,cAAc;AAEpB,kBAAI,UAAU,MAAM;AACnB,qBAAK,OAAO,OAAO,mBAAmB,UAAU,IAAI;AAAA,cACrD;AAGA,kBAAI,QAAQ,QAAQ,0BAA0B,MAAM,IAAI;AACvD,0BAAU;AAAA,cACX;AAGAH,4BAAAA,MAAI,SAAS;AAAA,gBACZ,KAAK;AAAA,cACN,CAAC;AAAA,aACD,EACA,MAAM,MAAM;AAEZ,kBAAI,QAAQ,QAAQ,0BAA0B,MAAM,IAAI;AACvD,0BAAU;AAAA,cACX;AACAA,4BAAAA,MAAI,SAAS;AAAA,gBACZ,KAAK;AAAA,cACN,CAAC;AAAA,YACF,CAAC;AAAA,UACH,CAAC;AAAA,SACD,EACA,MAAM,CAAC,QAAQ;AACf,eAAK,UAAU;AACf,eAAK,MAAM,KAAK;AAAA,YACf,OAAO;AAAA,UACR,CAAC;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACA;AAAA,IACD,MAAM,WAAW;AAChB,UAAI,OAAO;AACX,UAAI,CAAC,KAAK,UAAU;AACnB,aAAK,cAAc;AACnB,eAAO,KAAK,MAAM,KAAK;AAAA,UACtB,OAAO;AAAA,QACR,CAAC;AAAA,MACF;AACA,UAAI,CAAC,KAAK;AACT,eAAO,KAAK,MAAM,KAAK;AAAA,UACtB,OAAO,KAAK,GAAG,SAAS;AAAA,QACzB,CAAC;AACF,UAAI,CAAC,2BAA2B,KAAK,KAAK,OAAO;AAChD,eAAO,KAAK,MAAM,KAAK;AAAA,UACtB,OAAO,KAAK,GAAG,YAAY;AAAA,QAC5B,CAAC;AACF,UAAI,CAAC,KAAK;AACT,eAAO,KAAK,MAAM,KAAK;AAAA,UACtB,OAAO,KAAK,GAAG,QAAQ;AAAA,QACxB,CAAC;AACF,UAAI,CAAC,aAAa,KAAK,KAAK,OAAO;AAClC,eAAO,KAAK,MAAM,KAAK;AAAA,UACtB,OAAO,KAAK,GAAG,WAAW;AAAA,QAC3B,CAAC;AACF,UAAI,CAAC,KAAK;AACT,eAAO,KAAK,MAAM,KAAK;AAAA,UACtB,OAAO,KAAK,GAAG,OAAO;AAAA,QACvB,CAAC;AACF,UAAI,8BAA8B,KAAK,KAAK,QAAQ;AACnD,eAAO,KAAK,MAAM,KAAK;AAAA,UACtB,OAAO,KAAK,GAAG,YAAY;AAAA,QAC5B,CAAC;AACFS,wBAAS;AAAA,QACR,SAAS,KAAK;AAAA,QACd,SAAS,KAAK;AAAA,QACd,UAAU,KAAK;AAAA,QACf,QAAQ,KAAK,OAAO,IAAI,QAAQ;AAAA,OAChC,EACC,KAAK,CAAC,QAAQ;AACd,aAAK,MAAM,KAAK;AAAA,UACf,OAAO;AAAA,QACR,CAAC;AACD,aAAK,WAAW;AAAA,OAChB,EACA,MAAM,CAAC,QAAQ;AACf,aAAK,MAAM,KAAK;AAAA,UACf,OAAO;AAAA,QACR,CAAC;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IACD,MAAM,QAAQ,MAAM;AACnB,UAAI,OAAO;AACX,UAAI,CAAC,KAAK,UAAU;AACnB,aAAK,cAAc;AACnB,eAAO,KAAK,MAAM,KAAK;AAAA,UACtB,OAAO;AAAA,QACR,CAAC;AAAA,MACF;AACA,UAAI,CAAC,KAAK;AACT,eAAO,KAAK,MAAM,KAAK;AAAA,UACtB,OAAO,KAAK,GAAG,SAAS;AAAA,QACzB,CAAC;AACF,UAAI,CAAC,2BAA2B,KAAK,KAAK,OAAO;AAChD,eAAO,KAAK,MAAM,KAAK;AAAA,UACtB,OAAO,KAAK,GAAG,YAAY;AAAA,QAC5B,CAAC;AACF,UAAI,KAAK,YAAY;AAAG,aAAK,OAAO;AAEpC,YAAMC,wBAAe;AAAA,QACpB,OAAO,KAAK;AAAA,QACZ,MAAM,KAAK;AAAA,QACX,KAAK,KAAK;AAAA,QACV,aAAa,KAAK;AAAA,QAClB,qBAAqB,KAAK;AAAA,OAC1B,EACC,KAAK,CAAC,QAAQ;AACd,aAAK,SAAQ;AACb,aAAK,MAAM,KAAK;AAAA,UACf,OAAO,IAAI;AAAA,QACZ,CAAC;AAAA,OACD,EACA,MAAM,CAAC,QAAQ;AACf,aAAK,MAAM,KAAK;AAAA,UACf,OAAO;AAAA,QACR,CAAC;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IACD,QAAQ,SAAU,OAAO;AACxB,WAAK,UAAU;AAAA,IACf;AAAA,IACD,MAAM,SAAS;AACd,UAAI,OAAO;AACX,UAAI,CAAC,KAAK,UAAU;AACnB,aAAK,cAAc;AACnB,eAAO,KAAK,MAAM,KAAK;AAAA,UACtB,OAAO;AAAA,QACR,CAAC;AAAA,MACF;AACA,UAAI,CAAC,KAAK;AACT,eAAO,KAAK,MAAM,KAAK;AAAA,UACtB,OAAO,KAAK,GAAG,OAAO;AAAA,QACvB,CAAC;AACF,UAAI,CAAC,kBAAkB,KAAK,KAAK,OAAO;AACvC,eAAO,KAAK,MAAM,KAAK;AAAA,UACtB,OAAO,KAAK,GAAG,UAAU;AAAA,QAC1B,CAAC;AACF,UAAI,CAAC,KAAK;AACT,eAAO,KAAK,MAAM,KAAK;AAAA,UACtB,OAAO,KAAK,GAAG,OAAO;AAAA,QACvB,CAAC;AACF,UAAI,KAAK,SAAS;AACjB,aAAK,UAAU,CAAC,KAAK;AAAA,aACf;AACN,eAAO,KAAK,MAAM,KAAK;AAAA,UACtB,OAAO,KAAK,GAAG,QAAQ;AAAA,QACxB,CAAC;AAAA,MACF;AACAC,uBAAQ;AAAA,QACP,SAAS,KAAK;AAAA,QACd,UAAU,KAAK;AAAA,QACf,QAAQ,KAAK,OAAO,IAAI,QAAQ;AAAA,OAChC,EACC,KAAK,CAAC,EAAE,WAAW;AACnB,aAAK,OAAO,OAAO,SAAS;AAAA,UAC3B,OAAO,KAAK;AAAA,UACZ,MAAM,KAAK,eAAe,KAAK,OAAO,KAAK;AAAA,QAC5C,CAAC;AACD,YAAI,UAAU,KAAK,OAAO,IAAI,QAAQ,KAAK;AAC3C,aAAK,OAAO,MAAM,QAAQ;AAE1BT,6BAAY,EACV,KAAK,CAAC,QAAQ;AACd,eAAK,UAAU;AACf,eAAK,OAAO,OAAO,UAAU,IAAI,KAAK,GAAG;AAGzCC,uCAAkB,EAChB,KAAK,CAAC,cAAc;AAEpB,gBAAI,UAAU,MAAM;AACnB,mBAAK,OAAO,OAAO,mBAAmB,UAAU,IAAI;AAAA,YACrD;AAEAH,0BAAAA,MAAI,SAAS;AAAA,cACZ,KAAK;AAAA,YACN,CAAC;AAAA,WACD,EACA,MAAM,MAAM;AAEZA,0BAAAA,MAAI,SAAS;AAAA,cACZ,KAAK;AAAA,YACN,CAAC;AAAA,UACF,CAAC;AAAA,SACF,EACA,MAAM,CAAC,UAAU;AACjB,eAAK,UAAU;AAAA,QAChB,CAAC;AAAA,OACF,EACA,MAAM,CAAC,MAAM;AACb,aAAK,UAAU;AACf,aAAK,MAAM,KAAK;AAAA,UACf,OAAO;AAAA,QACR,CAAC;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpvBA,GAAG,WAAW,eAAe;"}