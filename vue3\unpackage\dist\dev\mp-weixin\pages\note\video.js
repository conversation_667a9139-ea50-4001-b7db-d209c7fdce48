"use strict";
const common_vendor = require("../../common/vendor.js");
const components_emojiPanel_sina = require("../../components/emoji-panel/sina.js");
const api_social = require("../../api/social.js");
const common_assets = require("../../common/assets.js");
const lazyImage = () => "../../components/lazyImage/lazyImage.js";
const play = () => "../../components/play/play.js";
const uniLoadMore = () => "../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js";
const SharePanel = () => "../../components/share/index.js";
const CommentInput = () => "../../components/comment-input/comment-input.js";
const _sfc_main = {
  name: "VideoDetails",
  components: {
    lazyImage,
    play,
    uniLoadMore,
    SharePanel,
    CommentInput
  },
  computed: {
    // 空状态判断
    showEmptyState() {
      return this.isEmpty && this.page === 1;
    },
    // 处理头像展示
    formattedUserAvatar() {
      return this.userAvatar || "/static/img/avatar_default.png";
    },
    // 发送按钮图标
    sendButtonSrc() {
      return this.comtext.length ? "/static/img/fs1.png" : "/static/img/fs.png";
    },
    // 是否为音频动态
    isAudioNote() {
      return this.noteInfo.type === 4;
    },
    // 是否为视频动态
    isVideoNote() {
      return this.noteInfo.type === 3;
    },
    // 是否为图片动态
    isImageNote() {
      return this.noteInfo.type === 1 || this.noteInfo.type === 2;
    },
    // 表情缓存统计（用于性能监控）
    emojiCacheInfo() {
      return {
        emojiMapSize: this.emojiMap.size,
        parsedContentCacheSize: this.parsedContentCache.size,
        cacheHitRate: this.parsedContentCache.size > 0 ? (this.parsedContentCache.size / (this.parsedContentCache.size + 10)).toFixed(2) : "0.00"
      };
    },
    // 页面性能统计
    pagePerformanceInfo() {
      return {
        commentCacheSize: Object.keys(this.commentCache).length,
        replyIndicesSize: this.replyIndices.size,
        isPageActive: this.isPageActive
      };
    }
  },
  data() {
    return {
      statusBarHeight: this.$store.state.statusBarHeight || 20,
      titleBarHeight: this.$store.state.titleBarHeight || 44,
      keyboardHeight: 0,
      footerHeight: 0,
      userId: 0,
      // 用户ID (uid)
      userNickname: "",
      // 用户昵称 (nickname)
      userAvatar: "/static/img/avatar_default.png",
      // 设置默认头像
      isUser: false,
      // 用户是否完善资料 (根据userId和phone判断)
      isFollowing: false,
      // 是否已关注动态作者
      followChecked: false,
      // 关注状态是否已检查
      actionInProgress: false,
      // 操作进行中防重复
      likeThrottling: false,
      // 点赞防抖
      bgAudioStatus: false,
      // 音频播放状态
      bgAudioManager: null,
      // 音频管理器
      audioPlayingId: "",
      // 当前播放音频ID
      audioProgress: 0,
      // 音频播放进度
      currentTime: 0,
      // 当前播放时间
      duration: 0,
      // 音频总时长
      // 添加加载状态控制
      isLoadingDetail: false,
      // 是否正在加载详情
      isLoadingComments: false,
      // 是否正在加载评论
      isSubmittingComment: false,
      // 是否正在提交评论
      isDeletingComment: false,
      // 是否正在删除评论
      isLoadingReplies: false,
      // 是否正在加载回复
      // 缓存相关
      commentsCache: {},
      // 评论列表缓存
      commentCache: {},
      // 回复列表缓存
      replyIndices: /* @__PURE__ */ new Map(),
      // 回复索引映射
      noteInfo: {
        id: 0,
        uid: 0,
        user_info: {
          uid: 0,
          nickname: "昵称加载中",
          avatar: "/static/img/avatar_default.png"
          // 设置默认头像
        },
        content: "内容加载中",
        type: 0,
        comments: 0,
        likes: 0,
        views: 0,
        shares: 0,
        is_like: false,
        create_time: "日期",
        location_name: "IP属地",
        latitude: "0",
        longitude: "0",
        topic_id: "",
        topic_info: [],
        product_id: 0,
        goods_info: null,
        images: [],
        video: "",
        video_cover: "",
        audio: "",
        audio_title: "",
        audio_cover: ""
      },
      isCommentContent: false,
      isEmpty: false,
      commentView: false,
      shareView: false,
      commentList: [],
      cType: 0,
      isThrottling: true,
      cIdx: -1,
      cI: -1,
      page: 1,
      sonPage: 1,
      limit: 10,
      // 每页评论数量
      loadStatus: "loading",
      cCId: 0,
      cUId: 0,
      isComment: false,
      isFocus: false,
      comtips: "说点什么...",
      comtext: "",
      isCommentPopup: false,
      isFocusPopup: false,
      tipsTitle: "",
      isContentOverflow: false,
      // 内容是否超出两行
      isExpanded: false,
      // 内容是否已展开
      currentImageIndex: 0,
      // 当前显示的图片索引
      isShareVisible: false,
      // 分享面板显示状态
      debug: true,
      // 是否开启调试日志
      // 表情相关优化
      emojiMap: /* @__PURE__ */ new Map(),
      // 表情映射缓存，提高查找性能
      parsedContentCache: /* @__PURE__ */ new Map(),
      // 解析内容缓存
      emojiClickTimer: null,
      // 表情点击防抖定时器
      isEmojiLoading: false,
      // 表情加载状态
      previewEmojiData: null,
      // 预览表情数据
      maxCacheSize: 200,
      // 最大缓存条目数
      // 性能优化相关
      isPageActive: true,
      // 页面是否活跃
      performanceTimer: null,
      // 性能监控定时器
      lazyLoadObserver: null
      // 懒加载观察器
    };
  },
  async onLoad(options) {
    common_vendor.index.__f__("log", "at pages/note/video.vue:659", "页面加载参数:", options);
    this.isPageActive = true;
    if (common_vendor.index.showShareMenu && typeof common_vendor.index.showShareMenu === "function") {
      common_vendor.index.showShareMenu();
    }
    await this.$onLaunched;
    common_vendor.index.__f__("log", "at pages/note/video.vue:671", "小程序初始化完成");
    this.initEmojiMap();
    this.loadRecentEmojis();
    this.userInfoHandle();
    if (options.id) {
      this.noteInfo.id = options.id;
      this.debugLog("获取到动态ID", this.noteInfo.id);
      this.dynamicDetails();
    } else {
      common_vendor.index.__f__("error", "at pages/note/video.vue:690", "未提供动态ID");
      common_vendor.index.showToast({
        title: "参数错误",
        icon: "none"
      });
      setTimeout(() => {
        common_vendor.index.navigateBack();
      }, 1500);
      return;
    }
    this.safeAreaBottom = common_vendor.index.getSystemInfoSync().safeAreaInsets && common_vendor.index.getSystemInfoSync().safeAreaInsets.bottom || 0;
    this.$nextTick(() => {
      common_vendor.index.createSelectorQuery().in(this).select(".footer-box").boundingClientRect((data) => {
        if (data) {
          this.footerHeight = parseInt(data.height);
        }
      }).exec();
    });
  },
  onShow() {
    this.isPageActive = true;
    this.userInfoHandle();
    if (this.isAudioNote && this.bgAudioManager) {
      this.checkAudioStatus();
    }
  },
  onHide() {
    this.isPageActive = false;
    if (this.isAudioNote && this.bgAudioManager && this.bgAudioStatus) {
      this.pauseAudio();
    }
  },
  mounted() {
    if (common_vendor.index.onPageScroll && typeof common_vendor.index.onPageScroll === "function") {
      common_vendor.index.onPageScroll(this.handlePageScroll);
    }
    this.initLazyLoad();
  },
  beforeUnmount() {
    this.isPageActive = false;
    this.cleanupResources();
  },
  methods: {
    // 添加调试日志函数
    debugLog(...args) {
      if (this.debug) {
        common_vendor.index.__f__("log", "at pages/note/video.vue:762", "[Video]", ...args);
      }
    },
    // 获取用户信息
    userInfoHandle() {
      var _a;
      try {
        let userInfo = common_vendor.index.getStorageSync("USER_INFO") || {};
        common_vendor.index.__f__("log", "at pages/note/video.vue:771", "本地存储用户数据类型:", typeof userInfo);
        if (typeof userInfo === "string") {
          try {
            userInfo = JSON.parse(userInfo);
            common_vendor.index.__f__("log", "at pages/note/video.vue:777", "已将字符串解析为对象");
          } catch (e) {
            common_vendor.index.__f__("error", "at pages/note/video.vue:779", "解析用户数据失败:", e);
            userInfo = {};
          }
        }
        const storeUid = ((_a = this.$store.state.app) == null ? void 0 : _a.uid) || 0;
        this.userId = userInfo.uid || userInfo.id || storeUid || 0;
        this.userAvatar = userInfo.avatar || "";
        this.userNickname = userInfo.nickname || "";
        const hasUserId = this.userId > 0;
        const hasPhone = !!userInfo.phone;
        this.isUser = hasUserId && hasPhone;
        this.debugLog("用户信息处理完成", {
          userId: this.userId,
          userAvatar: this.userAvatar,
          userNickname: this.userNickname,
          hasUserId,
          hasPhone,
          isUser: this.isUser
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/note/video.vue:806", "获取用户信息失败:", error);
        this.userId = 0;
        this.userAvatar = "";
        this.userNickname = "";
        this.isUser = false;
      }
    },
    // 处理媒体数据
    processMediaData() {
      try {
        this.debugLog("开始处理媒体数据，笔记类型:", this.noteInfo.type);
        switch (this.noteInfo.type) {
          case 1:
          case 2:
            this.processImageData();
            break;
          case 3:
            this.processVideoData();
            break;
          case 4:
            this.processAudioData();
            break;
          default:
            this.debugLog("未知的媒体类型:", this.noteInfo.type);
            if (this.noteInfo.video) {
              this.noteInfo.type = 3;
              this.processVideoData();
            } else if (this.noteInfo.audio) {
              this.noteInfo.type = 4;
              this.processAudioData();
            } else if (this.noteInfo.images && this.noteInfo.images.length) {
              this.noteInfo.type = this.noteInfo.images.length > 1 ? 2 : 1;
              this.processImageData();
            }
        }
      } catch (error) {
        this.debugLog("处理媒体数据失败:", error);
      }
    },
    // 处理图片数据
    processImageData() {
      this.debugLog("处理图片数据");
      try {
        let images = this.noteInfo.images;
        if (!Array.isArray(images)) {
          if (typeof images === "string" && images.startsWith("[")) {
            try {
              images = JSON.parse(images);
            } catch (e) {
              images = [images];
            }
          } else if (images) {
            images = [images];
          } else {
            images = [];
          }
        }
        this.noteInfo.images = images.map((img) => {
          if (img && typeof img === "object") {
            return img.url || img.src || img.path || img.image || "";
          }
          return img || "";
        }).filter((url) => !!url);
        this.noteInfo.images = this.noteInfo.images.map((url) => {
          return typeof url === "string" ? url : String(url);
        });
        this.debugLog("图片数据处理完成，数量:", this.noteInfo.images.length);
      } catch (e) {
        this.debugLog("处理图片数据失败:", e);
        this.noteInfo.images = [];
      }
    },
    // 处理视频数据
    processVideoData() {
      common_vendor.index.__f__("log", "at pages/note/video.vue:899", "处理视频数据:", this.noteInfo.video);
      if (!this.noteInfo.video_cover && this.noteInfo.video && this.noteInfo.video.cover) {
        this.noteInfo.video_cover = this.noteInfo.video.cover;
      }
      if (this.noteInfo.video && typeof this.noteInfo.video === "object") {
        if (this.noteInfo.video.url) {
          this.noteInfo.video = this.noteInfo.video.url;
        } else if (this.noteInfo.video.src) {
          this.noteInfo.video = this.noteInfo.video.src;
        } else if (this.noteInfo.video.path) {
          this.noteInfo.video = this.noteInfo.video.path;
        }
      }
      if (!this.noteInfo.video && this.noteInfo.video_url) {
        this.noteInfo.video = this.noteInfo.video_url;
      }
      if (this.noteInfo.video && typeof this.noteInfo.video !== "string") {
        this.noteInfo.video = String(this.noteInfo.video);
      }
      if (this.noteInfo.video_cover && typeof this.noteInfo.video_cover === "object") {
        if (this.noteInfo.video_cover.url) {
          this.noteInfo.video_cover = this.noteInfo.video_cover.url;
        }
      }
      if (this.noteInfo.video_cover && typeof this.noteInfo.video_cover !== "string") {
        this.noteInfo.video_cover = String(this.noteInfo.video_cover);
      }
    },
    // 处理音频数据 - 只有音频动态时才执行
    processAudioData() {
      if (!this.isAudioNote) {
        common_vendor.index.__f__("log", "at pages/note/video.vue:944", "非音频动态，跳过音频数据处理");
        return;
      }
      common_vendor.index.__f__("log", "at pages/note/video.vue:948", "处理音频数据:", this.noteInfo.audio);
      if (this.noteInfo.audio && typeof this.noteInfo.audio === "object") {
        if (this.noteInfo.audio.url) {
          this.noteInfo.audio = this.noteInfo.audio.url;
        } else if (this.noteInfo.audio.src) {
          this.noteInfo.audio = this.noteInfo.audio.src;
        } else if (this.noteInfo.audio.path) {
          this.noteInfo.audio = this.noteInfo.audio.path;
        }
      }
      if (this.noteInfo.audio && typeof this.noteInfo.audio !== "string") {
        this.noteInfo.audio = String(this.noteInfo.audio);
      }
      if (!this.noteInfo.audio_cover) {
        this.noteInfo.audio_cover = "/static/img/audio_default_cover.png";
      } else if (typeof this.noteInfo.audio_cover === "object") {
        if (this.noteInfo.audio_cover.url) {
          this.noteInfo.audio_cover = this.noteInfo.audio_cover.url;
        }
      }
      if (this.noteInfo.audio_cover && typeof this.noteInfo.audio_cover !== "string") {
        this.noteInfo.audio_cover = String(this.noteInfo.audio_cover);
      }
      if (!this.noteInfo.audio_title) {
        this.noteInfo.audio_title = "音频";
      }
      this.initAudioState();
    },
    // 初始化音频状态 - 只有音频动态时才调用
    initAudioState() {
      if (!this.isAudioNote)
        return;
      common_vendor.index.__f__("log", "at pages/note/video.vue:993", "初始化音频状态");
      this.bgAudioStatus = false;
      this.bgAudioManager = null;
      this.audioRetryCount = 0;
      this.audioPlayingId = "";
    },
    // 标准化图片数组
    normalizeImageArray(originalImages) {
      if (!originalImages) {
        common_vendor.index.__f__("log", "at pages/note/video.vue:1006", "图片数据为空，初始化为空数组");
        return [];
      }
      if (typeof originalImages === "string") {
        common_vendor.index.__f__("log", "at pages/note/video.vue:1012", "图片数据是字符串，尝试解析JSON");
        if (originalImages.startsWith("[")) {
          try {
            originalImages = JSON.parse(originalImages);
            common_vendor.index.__f__("log", "at pages/note/video.vue:1016", "JSON解析成功:", originalImages);
          } catch (parseErr) {
            common_vendor.index.__f__("error", "at pages/note/video.vue:1018", "JSON解析失败:", parseErr);
            originalImages = [originalImages];
          }
        } else {
          common_vendor.index.__f__("log", "at pages/note/video.vue:1022", "图片数据是单个字符串，转换为数组");
          originalImages = [originalImages];
        }
      }
      if (Array.isArray(originalImages)) {
        common_vendor.index.__f__("log", "at pages/note/video.vue:1029", "处理图片数组，数量:", originalImages.length);
        return originalImages.map((img, index) => {
          common_vendor.index.__f__("log", "at pages/note/video.vue:1031", `处理第${index + 1}张图片:`, img);
          if (typeof img === "string") {
            return { url: img, wide: 750, high: 750 };
          } else if (img && typeof img === "object") {
            const imgObj = {
              url: img.url || img.path || img.src || img.image || "",
              wide: parseInt(img.wide || img.width || 750),
              high: parseInt(img.high || img.height || 750)
            };
            common_vendor.index.__f__("log", "at pages/note/video.vue:1041", `图片${index + 1}处理结果:`, imgObj);
            return imgObj;
          }
          return { url: "", wide: 750, high: 750 };
        }).filter((img) => !!img.url);
      } else {
        common_vendor.index.__f__("log", "at pages/note/video.vue:1047", "图片数据不是数组，初始化为空数组");
        return [];
      }
    },
    // 处理通用数据
    processCommonData() {
      if (!this.noteInfo.user_info) {
        this.noteInfo.user_info = {
          nickname: "用户",
          avatar: "/static/img/avatar_default.png"
        };
      }
      if (!this.noteInfo.user_info.avatar) {
        this.noteInfo.user_info.avatar = "/static/img/avatar_default.png";
      }
      if (this.noteInfo.comments === void 0 || this.noteInfo.comments === null) {
        this.noteInfo.comments = 0;
      } else if (typeof this.noteInfo.comments === "string") {
        this.noteInfo.comments = parseInt(this.noteInfo.comments) || 0;
      }
      if (!this.noteInfo.uid && this.noteInfo.user_id) {
        this.noteInfo.uid = this.noteInfo.user_id;
      }
      common_vendor.index.__f__("log", "at pages/note/video.vue:1079", "通用数据处理完成");
    },
    // 检查是否有圈子
    hasCircle() {
      if (this.noteInfo.circle_info && this.noteInfo.circle_info.circle_id > 0) {
        return true;
      }
      if (this.noteInfo.circle_id && this.noteInfo.circle_id > 0) {
        return true;
      }
      return false;
    },
    // 获取圈子ID
    getCircleId() {
      if (this.noteInfo.circle_info && this.noteInfo.circle_info.circle_id) {
        return this.noteInfo.circle_info.circle_id;
      }
      return this.noteInfo.circle_id || 0;
    },
    // 获取圈子名称
    getCircleName() {
      if (this.noteInfo.circle_info && this.noteInfo.circle_info.circle_name) {
        return this.noteInfo.circle_info.circle_name;
      }
      return this.noteInfo.circle_name || "";
    },
    // 获取圈子头像
    getCircleAvatar() {
      if (this.noteInfo.circle_info && this.noteInfo.circle_info.circle_avatar) {
        return this.noteInfo.circle_info.circle_avatar;
      }
      return this.noteInfo.circle_avatar || "/static/img/qz1.png";
    },
    // 初始化表情映射缓存
    initEmojiMap() {
      if (this.emojiMap.size > 0)
        return;
      try {
        components_emojiPanel_sina.sinaEmoji.forEach((emoji) => {
          if (emoji && emoji.phrase) {
            this.emojiMap.set(emoji.phrase, emoji);
          }
        });
        common_vendor.index.__f__("log", "at pages/note/video.vue:1130", "表情映射缓存初始化完成，共", this.emojiMap.size, "个表情");
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/note/video.vue:1132", "初始化表情映射失败:", error);
      }
    },
    // 加载最近使用的表情
    loadRecentEmojis() {
      try {
        const recentEmojisStr = common_vendor.index.getStorageSync("recent_emojis");
        if (recentEmojisStr) {
          this.recentEmojis = JSON.parse(recentEmojisStr);
        }
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/note/video.vue:1144", "加载表情记录失败", e);
      }
    },
    // 初始化懒加载
    initLazyLoad() {
      common_vendor.index.__f__("log", "at pages/note/video.vue:1151", "初始化懒加载");
    },
    // 清理所有资源
    cleanupResources() {
      common_vendor.index.__f__("log", "at pages/note/video.vue:1156", "清理页面资源");
      if (common_vendor.index.offPageScroll && typeof common_vendor.index.offPageScroll === "function") {
        common_vendor.index.offPageScroll(this.handlePageScroll);
      }
      if (this.isAudioNote) {
        this.destroyAudioInstance();
      }
      this.clearAllTimers();
      this.cleanupEmojiResources();
      if (this.lazyLoadObserver) {
        this.lazyLoadObserver.disconnect();
        this.lazyLoadObserver = null;
      }
    },
    // 清除所有定时器
    clearAllTimers() {
      if (this.debounceTimer) {
        clearTimeout(this.debounceTimer);
        this.debounceTimer = null;
      }
      if (this.commentBlurTimer) {
        clearTimeout(this.commentBlurTimer);
        this.commentBlurTimer = null;
      }
      if (this.performanceTimer) {
        clearTimeout(this.performanceTimer);
        this.performanceTimer = null;
      }
      if (this.imageCheckInterval) {
        clearInterval(this.imageCheckInterval);
        this.imageCheckInterval = null;
      }
    },
    // 清理表情相关资源
    cleanupEmojiResources() {
      if (this.emojiClickTimer) {
        clearTimeout(this.emojiClickTimer);
        this.emojiClickTimer = null;
      }
      this.clearEmojiCache();
    },
    // 清理表情缓存
    clearEmojiCache() {
      this.parsedContentCache.clear();
      common_vendor.index.__f__("log", "at pages/note/video.vue:1218", "表情缓存已清理");
    },
    // 获取表情缓存统计
    getEmojiCacheStats() {
      return {
        emojiMapSize: this.emojiMap.size,
        parsedContentCacheSize: this.parsedContentCache.size,
        maxCacheSize: this.maxCacheSize
      };
    },
    // 强制应用表情样式（运行时修复）
    forceApplyEmojiStyles() {
      this.$nextTick(() => {
        try {
          const emojiImages = common_vendor.index.createSelectorQuery().in(this).selectAll("image[data-emoji], img[data-emoji]").exec((res) => {
            if (res && res[0]) {
              res[0].forEach((node, index) => {
                common_vendor.index.createSelectorQuery().in(this).select(`image[data-emoji]:nth-child(${index + 1}), img[data-emoji]:nth-child(${index + 1})`).fields({
                  node: true,
                  size: true
                }).exec((nodeRes) => {
                  if (nodeRes && nodeRes[0] && nodeRes[0].node) {
                    const node2 = nodeRes[0].node;
                    node2.style.width = "32rpx";
                    node2.style.height = "32rpx";
                    node2.style.maxWidth = "32rpx";
                    node2.style.maxHeight = "32rpx";
                    node2.style.minWidth = "32rpx";
                    node2.style.minHeight = "32rpx";
                    node2.style.objectFit = "cover";
                  }
                });
              });
            }
          });
        } catch (error) {
          common_vendor.index.__f__("warn", "at pages/note/video.vue:1264", "强制应用表情样式失败:", error);
        }
      });
    },
    // 返回上一页
    navBack() {
      if (getCurrentPages().length > 1) {
        common_vendor.index.navigateBack();
      } else {
        common_vendor.index.switchTab({
          url: "//pages/index/index"
        });
      }
    },
    // 显示提示
    opTipsPopup(msg, back = false) {
      let self = this;
      common_vendor.index.__f__("log", "at pages/note/video.vue:1284", "显示提示", msg, back);
      self.tipsTitle = msg;
      self.$refs.tipsPopup.open();
      setTimeout(() => {
        self.$refs.tipsPopup.close();
        if (back) {
          self.navBack();
        }
      }, 2e3);
    },
    // 页面跳转
    navigateToFun(e) {
      common_vendor.index.navigateTo({
        url: "/pages/" + e.currentTarget.dataset.url
      });
    },
    // 打开位置信息
    openLocationClick() {
      if (!this.noteInfo.latitude || !this.noteInfo.longitude)
        return;
      common_vendor.index.openLocation({
        latitude: parseFloat(this.noteInfo.latitude),
        longitude: parseFloat(this.noteInfo.longitude),
        name: this.noteInfo.location_name
      });
    },
    // 打开分享面板
    openShare() {
      this.isShareVisible = true;
    },
    // 关闭分享面板
    closeShare() {
      this.isShareVisible = false;
    },
    // 处理编辑笔记
    handleEdit(noteId) {
      common_vendor.index.navigateTo({
        url: `/pages/note/add?id=${noteId}`
      });
    },
    // 处理删除笔记
    handleDelete(noteId) {
      this.delDynamic();
    },
    // 处理举报笔记
    handleReport(reportData) {
      this.reasonClick(reportData.reason);
    },
    // 处理不感兴趣
    handleDislike(noteId) {
      common_vendor.index.__f__("log", "at pages/note/video.vue:1344", "标记不感兴趣:", noteId);
    },
    // 点赞笔记
    likeDynamic() {
      if (this.actionInProgress)
        return;
      this.actionInProgress = true;
      const currentLikeState = this.noteInfo.is_like ? 1 : 0;
      const newLikeState = currentLikeState ? 0 : 1;
      const oldLikes = this.noteInfo.likes;
      this.noteInfo.is_like = newLikeState;
      this.noteInfo.likes = newLikeState ? oldLikes + 1 : oldLikes - 1;
      api_social.likeDynamic({
        id: this.noteInfo.id,
        is_like: newLikeState
        // 直接使用0或1
      }).then((res) => {
      }).catch((err) => {
        this.noteInfo.is_like = currentLikeState;
        this.noteInfo.likes = oldLikes;
        this.opTipsPopup("操作失败，请重试");
      }).finally(() => {
        setTimeout(() => {
          this.actionInProgress = false;
        }, 500);
      });
    },
    // 关注/取消关注用户
    followUser() {
      if (!this.noteInfo.uid || this.actionInProgress)
        return;
      this.actionInProgress = true;
      const newFollowState = !this.isFollowing;
      const oldFollowState = this.isFollowing;
      this.isFollowing = newFollowState;
      if (this.noteInfo.user_info) {
        this.noteInfo.user_info.is_follow = newFollowState;
      }
      api_social.followUser({
        follow_uid: this.noteInfo.uid,
        is_follow: newFollowState ? 1 : 0
      }).then((res) => {
      }).catch((err) => {
        this.isFollowing = oldFollowState;
        if (this.noteInfo.user_info) {
          this.noteInfo.user_info.is_follow = oldFollowState;
        }
        this.opTipsPopup("操作失败，请重试");
      }).finally(() => {
        setTimeout(() => {
          this.actionInProgress = false;
        }, 500);
      });
    },
    // 删除笔记
    delDynamic() {
      let self = this;
      common_vendor.index.showModal({
        content: "确认要永久删除这篇笔记吗？",
        confirmColor: "#FA5150",
        success: function(res) {
          if (res.confirm) {
            common_vendor.index.showLoading({
              mask: true
            });
            api_social.deleteDynamic(self.noteInfo.id).then((res2) => {
              common_vendor.index.hideLoading();
              getApp().globalData.isCenterPage = true;
              if (res2.status === 200) {
                self.opTipsPopup("删除成功", true);
              } else {
                self.opTipsPopup(res2.msg || "删除失败");
              }
            }).catch((err) => {
              common_vendor.index.hideLoading();
              self.opTipsPopup("删除失败");
            });
          }
        }
      });
    },
    // 举报笔记
    reasonClick(reason) {
      let self = this;
      common_vendor.index.showLoading({
        mask: true
      });
      let imageUrl = "";
      if (self.noteInfo.type == 2 && self.noteInfo.images && self.noteInfo.images.length > 0) {
        imageUrl = self.noteInfo.images[0].url;
      } else if (self.noteInfo.type == 3 && self.noteInfo.video && self.noteInfo.video_cover) {
        imageUrl = self.noteInfo.video_cover;
      } else if (self.noteInfo.type == 4 && self.noteInfo.audio && self.noteInfo.audio.cover) {
        imageUrl = self.noteInfo.audio.cover;
      }
      api_social.reportDynamic(
        reason,
        self.noteInfo.id,
        self.noteInfo.uid,
        self.noteInfo.content,
        imageUrl
      ).then((res) => {
        common_vendor.index.hideLoading();
        self.opTipsPopup(res.msg || "举报成功");
        self.menuPopupClick(false);
      }).catch((err) => {
        common_vendor.index.hideLoading();
        self.opTipsPopup("举报失败");
      });
    },
    // 分享设置
    onShareAppMessage: function() {
      return {
        title: this.noteInfo.content,
        imageUrl: this.getShareImageUrl(),
        path: "/pages/note/video?id=" + this.noteInfo.id
      };
    },
    // 分享到朋友圈
    onShareTimeline() {
      return {
        title: this.noteInfo.content,
        imageUrl: this.getShareImageUrl(),
        query: "id=" + this.noteInfo.id
      };
    },
    // 获取分享图片URL
    getShareImageUrl() {
      if (this.noteInfo.type == 2) {
        if (this.noteInfo.images && this.noteInfo.images.length > 0) {
          const firstImage = this.noteInfo.images[0];
          if (typeof firstImage === "string") {
            return firstImage;
          } else if (firstImage.url) {
            return firstImage.url;
          }
        }
        return "";
      } else if (this.noteInfo.type == 3) {
        return this.noteInfo.video_cover || "";
      } else if (this.noteInfo.type == 4) {
        return this.noteInfo.audio_cover || "";
      }
      return "";
    },
    // 格式化数字显示
    formatCount(count) {
      if (!count || count < 1e3)
        return count;
      if (count < 1e4)
        return (count / 1e3).toFixed(1) + "k";
      return (count / 1e4).toFixed(1) + "w";
    },
    // 格式化时间
    formatTime(seconds) {
      if (!seconds || isNaN(seconds))
        return "00:00";
      const mins = Math.floor(seconds / 60);
      const secs = Math.floor(seconds % 60);
      return `${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
    },
    // 格式化日期为字符串
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      const hours = String(date.getHours()).padStart(2, "0");
      const minutes = String(date.getMinutes()).padStart(2, "0");
      return `${year}-${month}-${day} ${hours}:${minutes}`;
    },
    // 获取评论列表 - 优化版本
    getCommentList() {
      if (this.isLoadingComments) {
        this.debugLog("正在加载评论，跳过重复请求");
        return;
      }
      if (this.loadStatus === "no-more" && this.page > 1) {
        this.debugLog("已无更多评论，跳过请求");
        return;
      }
      this.isLoadingComments = true;
      if (this.page === 1) {
        common_vendor.index.showLoading({
          title: "加载中",
          mask: true
        });
      } else {
        this.loadStatus = "loading";
      }
      const params = {
        type: 0,
        // 动态评论类型
        page: this.page || 1,
        sort_type: this.cType || 0
        // 0-默认，1-最新
      };
      this.debugLog("获取评论列表", {
        动态ID: this.noteInfo.id,
        页码: params.page,
        排序方式: params.sort_type === 0 ? "默认" : "最新"
      });
      api_social.getCommentsList(this.noteInfo.id, params).then((res) => {
        if (this.page === 1) {
          common_vendor.index.hideLoading();
        }
        this.isLoadingComments = false;
        if (res.status === 200) {
          this.debugLog("评论列表获取成功", res.data);
          const list = res.data.list || [];
          if (res.data.total !== void 0 && res.data.total !== null) {
            this.noteInfo.comments = parseInt(res.data.total) || 0;
            this.debugLog("从API获取总评论数", this.noteInfo.comments);
          } else if (res.data.count !== void 0 && res.data.count !== null) {
            this.noteInfo.comments = parseInt(res.data.count) || 0;
            this.debugLog("从API获取总评论数(count字段)", this.noteInfo.comments);
          }
          const processedList = list.map((item) => {
            const avatar = item.avatar || item.user_info && item.user_info.avatar || "/static/img/avatar_default.png";
            const nickname = item.nickname || item.user_info && item.user_info.nickname || "用户";
            const uid = item.uid || item.user_info && item.user_info.uid || 0;
            return {
              ...item,
              // 确保基本字段存在
              id: item.id || 0,
              uid,
              nickname,
              avatar,
              // 保存原始用户信息对象，同时确保字段一致性
              user_info: {
                uid,
                nickname,
                avatar
              },
              reply_count: parseInt(item.reply_count) || 0,
              like_count: parseInt(item.like_count) || parseInt(item.likes) || 0,
              likes: parseInt(item.likes) || parseInt(item.like_count) || 0,
              is_like: !!item.is_like,
              create_time: item.create_time || item.add_time || "",
              content: item.content || "",
              image: item.image || "",
              status: item.status || 5,
              delete_time: item.delete_time || null,
              // 处理回复列表
              replies: Array.isArray(item.replies) ? item.replies.map((reply) => {
                const replyAvatar = reply.avatar || reply.user_info && reply.user_info.avatar || "/static/img/avatar_default.png";
                const replyNickname = reply.nickname || reply.user_info && reply.user_info.nickname || "用户";
                const replyUid = reply.uid || reply.user_info && reply.user_info.uid || 0;
                return {
                  ...reply,
                  id: reply.id || 0,
                  uid: replyUid,
                  nickname: replyNickname,
                  avatar: replyAvatar,
                  user_info: {
                    uid: replyUid,
                    nickname: replyNickname,
                    avatar: replyAvatar
                  },
                  reply_uid: reply.reply_uid || reply.to_uid || 0,
                  reply_nickname: reply.reply_nickname || reply.to_nickname || "",
                  like_count: parseInt(reply.like_count) || parseInt(reply.likes) || 0,
                  likes: parseInt(reply.likes) || parseInt(reply.like_count) || 0,
                  is_like: !!reply.is_like,
                  create_time: reply.create_time || reply.add_time || "",
                  content: reply.content || "",
                  image: reply.image || "",
                  status: reply.status || 5,
                  delete_time: reply.delete_time || null
                };
              }) : [],
              // 添加回复分页相关字段
              replyPage: 1,
              has_more_replies: item.reply_count > (item.replies ? item.replies.length : 0)
            };
          });
          if (this.page === 1) {
            this.commentList = processedList;
            this.isEmpty = processedList.length === 0;
          } else {
            this.commentList = [...this.commentList, ...processedList];
          }
          if (list.length < 10) {
            this.loadStatus = "no-more";
          } else {
            this.loadStatus = "more";
          }
          if (this.page === 1) {
            const cacheKey = `comments_${this.noteInfo.id}_${this.cType}_${this.page}`;
            this.commentsCache[cacheKey] = {
              list: processedList,
              isEmpty: this.isEmpty,
              loadStatus: this.loadStatus,
              totalComments: this.noteInfo.comments
            };
          }
          this.updateReplyIndices();
          this.debugLog("评论列表更新完成", {
            当前页码: this.page,
            评论总数: this.commentList.length,
            加载状态: this.loadStatus,
            是否为空: this.isEmpty
          });
        } else {
          this.debugLog("评论列表获取失败", res);
          this.loadStatus = "no-more";
          common_vendor.index.showToast({
            title: res.msg || "获取评论失败",
            icon: "none"
          });
        }
      }).catch((err) => {
        if (this.page === 1) {
          common_vendor.index.hideLoading();
        }
        this.isLoadingComments = false;
        this.loadStatus = "no-more";
        this.debugLog("获取评论列表异常", err);
        common_vendor.index.showToast({
          title: "获取评论失败",
          icon: "none"
        });
      });
    },
    // 切换评论排序
    commentClick(type) {
      if (!this.isThrottling || this.actionInProgress)
        return;
      if (this.cType === type)
        return;
      this.isThrottling = false;
      this.cType = type;
      this.page = 1;
      this.loadStatus = "loading";
      this.commentList = [];
      this.isEmpty = false;
      this.getCommentList();
      setTimeout(() => {
        this.isThrottling = true;
      }, 500);
    },
    // 打开评论框
    openComment(e) {
      e = e || { currentTarget: { dataset: { type: 0 } } };
      e.stopPropagation && e.stopPropagation();
      common_vendor.index.__f__("log", "at pages/note/video.vue:1796", "尝试打开评论框，用户状态:", {
        isUser: this.isUser,
        userId: this.userId,
        userInfo: {
          avatar: this.userAvatar,
          nickname: this.userNickname
        }
      });
      if (!this.isUser) {
        common_vendor.index.__f__("log", "at pages/note/video.vue:1806", "用户未完善资料，无法评论");
        this.opTipsPopup("完善账号资料后即可评论！");
        setTimeout(() => {
          common_vendor.index.navigateTo({
            url: "/pages/center/means"
          });
        }, 1e3);
        return;
      }
      let dataset = e.currentTarget.dataset || {};
      let type = dataset.type || 0;
      let uid = dataset.uid || 0;
      let cid = dataset.cid || 0;
      let name = dataset.name || "";
      this.cIdx = dataset.idx !== void 0 ? dataset.idx : -1;
      this.cI = dataset.i !== void 0 ? dataset.i : -1;
      this.isComment = false;
      this.isSubmittingComment = false;
      if (type == 1) {
        this.cCId = cid;
        this.cUId = uid;
        this.comtips = "回复：" + name;
      } else {
        this.cCId = 0;
        this.cUId = 0;
        this.comtips = "说点什么...";
      }
      this.$nextTick(() => {
        this.isComment = true;
        setTimeout(() => {
          this.isFocus = true;
          if (!this.isComment) {
            this.isComment = true;
          }
        }, 150);
      });
    },
    // 关闭评论框
    closeComment(e) {
      e && e.stopPropagation && e.stopPropagation();
      common_vendor.index.__f__("log", "at pages/note/video.vue:1864", "手动关闭评论框");
      if (this.isSubmittingComment) {
        return;
      }
      if (this.commentBlurTimer) {
        clearTimeout(this.commentBlurTimer);
        this.commentBlurTimer = null;
      }
      this.commentActioning = false;
      this.isComment = false;
      this.isFocus = false;
      this.comtext = "";
    },
    // 处理评论提交
    handleCommentSubmit(commentData) {
      if (this.isSubmittingComment)
        return;
      this.isSubmittingComment = true;
      const content = commentData.content;
      const image = commentData.image;
      if (!content && !image) {
        this.isSubmittingComment = false;
        return this.opTipsPopup("表达你的态度再评论吧！");
      }
      common_vendor.index.showLoading({
        title: "发布中",
        mask: true
      });
      this.isComment = false;
      this.isFocus = false;
      this.showEmoji = false;
      const params = {
        dynamic_id: this.noteInfo.id,
        content,
        pid: this.cCId || 0,
        to_uid: this.cUId || 0
      };
      if (image) {
        params.image = image;
      }
      api_social.addComment(params).then((res) => {
        common_vendor.index.hideLoading();
        if (res.status === 200) {
          const commentData2 = res.data || this.createDefaultCommentData(content, image);
          this.processCommentSuccess(commentData2);
          this.opTipsPopup("评论成功");
          if (this.isEmpty) {
            this.isEmpty = false;
          }
        } else {
          this.opTipsPopup(res.msg || "评论失败");
        }
      }).catch((err) => {
        common_vendor.index.hideLoading();
        this.opTipsPopup("评论失败，请重试");
        common_vendor.index.__f__("error", "at pages/note/video.vue:1953", "评论请求异常", err);
      }).finally(() => {
        this.isSubmittingComment = false;
        this.cCId = 0;
        this.cUId = 0;
        this.comtips = "说点什么...";
        this.comtext = "";
      });
    },
    // 创建默认评论数据（当API返回为空时）
    createDefaultCommentData(content, imageUrl) {
      var _a, _b, _c;
      const tempId = `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const userProvince = ((_a = this.noteInfo) == null ? void 0 : _a.province) || ((_c = (_b = this.$store.state.app) == null ? void 0 : _b.userInfo) == null ? void 0 : _c.province) || "";
      const commentData = {
        id: tempId,
        // 使用更安全的临时ID
        uid: this.userId,
        nickname: this.userNickname || "用户",
        avatar: this.userAvatar || "/static/img/avatar_default.png",
        content: content || "",
        image: imageUrl || "",
        // 评论图片URL
        create_time: this.formatDate(/* @__PURE__ */ new Date()),
        likes: 0,
        like_count: 0,
        // 兼容不同字段名
        is_like: false,
        status: 5,
        // 正常状态：5
        province: userProvince,
        delete_time: null,
        // 删除时间，用于标记是否被删除
        replies: [],
        // 初始化回复数组
        reply_count: 0,
        // 回复数量
        has_more_replies: false,
        // 是否有更多回复
        replyPage: 1,
        // 回复页码
        loading_replies: false
        // 是否正在加载回复
      };
      common_vendor.index.__f__("log", "at pages/note/video.vue:1997", "创建默认评论数据:", commentData);
      return commentData;
    },
    // 处理评论成功
    processCommentSuccess(commentData) {
      common_vendor.index.__f__("log", "at pages/note/video.vue:2003", "处理评论成功", {
        commentData,
        cIdx: this.cIdx,
        cI: this.cI,
        noteInfoComments: this.noteInfo.comments
      });
      if (!commentData) {
        common_vendor.index.__f__("error", "at pages/note/video.vue:2012", "评论数据为空，无法处理");
        return;
      }
      this.noteInfo.comments = (this.noteInfo.comments || 0) + 1;
      if (this.cIdx >= 0) {
        common_vendor.index.__f__("log", "at pages/note/video.vue:2021", "处理回复评论", this.cIdx);
        if (!this.commentList[this.cIdx].replies) {
          this.commentList[this.cIdx].replies = [];
        }
        if (this.commentList[this.cIdx].reply_count === void 0) {
          this.commentList[this.cIdx].reply_count = 0;
        }
        if (this.cUId) {
          const nickname = this.comtips.replace("回复：", "");
          commentData.reply_uid = this.cUId;
          commentData.reply_nickname = nickname;
        }
        this.commentList[this.cIdx].replies.push(commentData);
        this.commentList[this.cIdx].reply_count++;
        this.replyIndices.set(commentData.id, this.commentList[this.cIdx].replies.length - 1);
        this.$forceUpdate();
        common_vendor.index.__f__("log", "at pages/note/video.vue:2048", "回复评论处理完成", {
          回复数量: this.commentList[this.cIdx].reply_count,
          回复列表: this.commentList[this.cIdx].replies.length
        });
      } else {
        common_vendor.index.__f__("log", "at pages/note/video.vue:2055", "处理新评论");
        if (this.isEmpty || this.commentList.length === 0) {
          this.isEmpty = false;
          this.commentList = [];
          common_vendor.index.__f__("log", "at pages/note/video.vue:2060", "重置评论列表");
        }
        commentData.replies = [];
        commentData.reply_count = 0;
        this.commentList.unshift(commentData);
        this.$forceUpdate();
        common_vendor.index.__f__("log", "at pages/note/video.vue:2073", "新评论处理完成", {
          评论列表长度: this.commentList.length,
          评论总数: this.noteInfo.comments
        });
      }
    },
    // 删除评论
    delComment(e) {
      let self = this;
      let idx = e.currentTarget.dataset.idx;
      let i = e.currentTarget.dataset.i;
      let commentId = e.currentTarget.dataset.id;
      if (self.isDeletingComment)
        return;
      common_vendor.index.showModal({
        content: "确定要永久删除该评论吗？",
        confirmColor: "#FA5150",
        success: function(res) {
          if (res.confirm) {
            self.isDeletingComment = true;
            common_vendor.index.showLoading({
              title: "删除中",
              mask: true
            });
            api_social.deleteComment(commentId).then((res2) => {
              common_vendor.index.hideLoading();
              self.isDeletingComment = false;
              if (res2.status === 200) {
                if (self.noteInfo.comments > 0) {
                  self.noteInfo.comments--;
                }
                if (i == -1) {
                  self.commentList[idx].delete_time = (/* @__PURE__ */ new Date()).toISOString();
                  self.commentList[idx].status = 0;
                } else {
                  if (self.commentList[idx].replies && self.commentList[idx].replies.length > i) {
                    self.commentList[idx].replies[i].delete_time = (/* @__PURE__ */ new Date()).toISOString();
                    self.commentList[idx].replies[i].status = 0;
                    if (self.commentList[idx].reply_count > 0) {
                      self.commentList[idx].reply_count--;
                    }
                  }
                }
                self.opTipsPopup("删除成功");
              } else {
                self.opTipsPopup(res2.msg || "删除失败");
              }
            }).catch((err) => {
              common_vendor.index.hideLoading();
              self.isDeletingComment = false;
              self.opTipsPopup("删除失败");
            });
          }
        }
      });
    },
    // 点赞/取消点赞评论
    toggleCommentLike(commentId, isCurrentlyLiked) {
      if (!this.isUser) {
        this.opTipsPopup("请先完善账号资料");
        return;
      }
      if (this.likeThrottling)
        return;
      this.likeThrottling = true;
      setTimeout(() => {
        this.likeThrottling = false;
      }, 500);
      const currentLikeState = isCurrentlyLiked ? 1 : 0;
      const newLikeState = currentLikeState ? 0 : 1;
      for (let i = 0; i < this.commentList.length; i++) {
        const comment = this.commentList[i];
        if (comment.id === commentId) {
          comment.is_like = newLikeState;
          if (newLikeState) {
            if (comment.like_count !== void 0) {
              comment.like_count = (comment.like_count || 0) + 1;
            }
            if (comment.likes !== void 0) {
              comment.likes = (comment.likes || 0) + 1;
            }
          } else {
            if (comment.like_count !== void 0 && comment.like_count > 0) {
              comment.like_count--;
            }
            if (comment.likes !== void 0 && comment.likes > 0) {
              comment.likes--;
            }
          }
          break;
        }
        if (comment.replies && comment.replies.length > 0) {
          for (let j = 0; j < comment.replies.length; j++) {
            const reply = comment.replies[j];
            if (reply.id === commentId) {
              reply.is_like = newLikeState;
              if (newLikeState) {
                if (reply.like_count !== void 0) {
                  reply.like_count = (reply.like_count || 0) + 1;
                }
                if (reply.likes !== void 0) {
                  reply.likes = (reply.likes || 0) + 1;
                }
              } else {
                if (reply.like_count !== void 0 && reply.like_count > 0) {
                  reply.like_count--;
                }
                if (reply.likes !== void 0 && reply.likes > 0) {
                  reply.likes--;
                }
              }
              break;
            }
          }
        }
      }
      if (currentLikeState) {
        api_social.unlikeComment(commentId).then((res) => {
        }).catch((err) => {
          this.restoreCommentLikeStatus(commentId, currentLikeState);
          this.opTipsPopup("操作失败，请重试");
        });
      } else {
        api_social.likeComment(commentId).then((res) => {
        }).catch((err) => {
          this.restoreCommentLikeStatus(commentId, currentLikeState);
          this.opTipsPopup("操作失败，请重试");
        });
      }
    },
    // 恢复评论点赞状态（操作失败时）
    restoreCommentLikeStatus(commentId, originalLikeState) {
      for (let i = 0; i < this.commentList.length; i++) {
        const comment = this.commentList[i];
        if (comment.id === commentId) {
          comment.is_like = originalLikeState;
          if (originalLikeState === 1) {
            if (comment.like_count !== void 0 && comment.like_count > 0) {
              comment.like_count--;
            }
            if (comment.likes !== void 0 && comment.likes > 0) {
              comment.likes--;
            }
          } else {
            if (comment.like_count !== void 0) {
              comment.like_count = (comment.like_count || 0) + 1;
            }
            if (comment.likes !== void 0) {
              comment.likes = (comment.likes || 0) + 1;
            }
          }
          break;
        }
        if (comment.replies && comment.replies.length > 0) {
          for (let j = 0; j < comment.replies.length; j++) {
            const reply = comment.replies[j];
            if (reply.id === commentId) {
              reply.is_like = originalLikeState;
              if (originalLikeState === 1) {
                if (reply.like_count !== void 0 && reply.like_count > 0) {
                  reply.like_count--;
                }
                if (reply.likes !== void 0 && reply.likes > 0) {
                  reply.likes--;
                }
              } else {
                if (reply.like_count !== void 0) {
                  reply.like_count = (reply.like_count || 0) + 1;
                }
                if (reply.likes !== void 0) {
                  reply.likes = (reply.likes || 0) + 1;
                }
              }
              break;
            }
          }
        }
      }
    },
    // 递归更新回复索引映射
    updateReplyIndices() {
      if (!this.commentList || !this.commentList.length)
        return;
      this.replyIndices = /* @__PURE__ */ new Map();
      this.commentList.forEach((comment, commentIndex) => {
        if (comment.replies && comment.replies.length) {
          comment.replies.forEach((reply, replyIndex) => {
            this.replyIndices.set(reply.id, replyIndex);
          });
        }
      });
      this.debugLog("更新回复索引映射完成", {
        索引数量: this.replyIndices.size
      });
    },
    // 加载评论回复 - 与details.vue保持一致
    sonComment(e) {
      let id = parseInt(e.currentTarget.dataset.id) || 0;
      let idx = parseInt(e.currentTarget.dataset.idx) || 0;
      this.debugLog("加载评论回复:", {
        commentId: id,
        commentIndex: idx
      });
      if (this.isLoadingReplies)
        return;
      this.isLoadingReplies = true;
      const commentItem = this.commentList[idx];
      if (commentItem) {
        this.$set(commentItem, "loading_replies", true);
      }
      const currentPage = parseInt(commentItem.replyPage) || 1;
      const cacheKey = `replies_${id}_${currentPage}`;
      if (this.commentCache && this.commentCache[cacheKey]) {
        this.debugLog("使用缓存中的回复数据");
        this.handleAllRepliesData(this.commentCache[cacheKey], idx, currentPage);
        return;
      }
      const params = {
        parent_id: id,
        // 父评论ID
        page: currentPage,
        // 页码
        limit: 10,
        // 每页数量
        sort_type: 1
        // 排序类型：1=最新(创建时间)
      };
      this.debugLog("回复请求参数:", params);
      api_social.getCommentReplies(params).then((res) => {
        if (res.status === 200) {
          this.debugLog("获取到回复数据:", res.data);
          if (!this.commentCache)
            this.commentCache = {};
          this.commentCache[cacheKey] = res.data;
          this.handleAllRepliesData(res.data, idx, currentPage);
        } else {
          if (commentItem) {
            this.$set(commentItem, "loading_replies", false);
          }
          this.isLoadingReplies = false;
          common_vendor.index.showToast({
            title: res.msg || "获取回复失败",
            icon: "none"
          });
        }
      }).catch((err) => {
        this.debugLog("获取回复失败:", err);
        if (commentItem) {
          this.$set(commentItem, "loading_replies", false);
        }
        this.isLoadingReplies = false;
        common_vendor.index.showToast({
          title: "获取回复失败",
          icon: "none"
        });
      });
    },
    // 处理回复数据（分页方式）- 与details.vue保持一致
    handleAllRepliesData(data, idx, page) {
      if (this.commentList[idx]) {
        const commentItem = this.commentList[idx];
        let replies = [];
        if (data.list && Array.isArray(data.list)) {
          replies = data.list;
        } else if (Array.isArray(data)) {
          replies = data;
        } else if (data.data && Array.isArray(data.data)) {
          replies = data.data;
        } else {
          this.debugLog("无法识别的回复数据结构", data);
          replies = [];
        }
        this.debugLog("获取到回复数据:", replies);
        replies = replies.map((reply) => {
          const replyAvatar = reply.avatar || reply.user_info && reply.user_info.avatar || "/static/img/avatar_default.png";
          const replyNickname = reply.nickname || reply.user_info && reply.user_info.nickname || "用户";
          const replyUid = reply.uid || reply.user_info && reply.user_info.uid || 0;
          return {
            ...reply,
            id: reply.id || 0,
            uid: replyUid,
            nickname: replyNickname,
            avatar: replyAvatar,
            user_info: {
              uid: replyUid,
              nickname: replyNickname,
              avatar: replyAvatar
            },
            reply_uid: reply.reply_uid || reply.to_uid || 0,
            reply_nickname: reply.reply_nickname || reply.to_nickname || "",
            like_count: parseInt(reply.like_count) || parseInt(reply.likes) || 0,
            likes: parseInt(reply.likes) || parseInt(reply.like_count) || 0,
            is_like: !!reply.is_like,
            create_time: reply.create_time || reply.add_time || "",
            content: reply.content || "",
            image: reply.image || "",
            status: reply.status || 5,
            delete_time: reply.delete_time || null
          };
        });
        if (page === 1) {
          this.$set(commentItem, "replies", replies);
        } else {
          const existingIds = (commentItem.replies || []).map((r) => r.id);
          const newReplies = replies.filter((r) => !existingIds.includes(r.id));
          this.$set(commentItem, "replies", [...commentItem.replies || [], ...newReplies]);
        }
        this.$set(commentItem, "replyPage", page + 1);
        let replyCount = 0;
        if (data.count !== void 0) {
          replyCount = parseInt(data.count) || 0;
        } else if (data.total !== void 0) {
          replyCount = parseInt(data.total) || 0;
        } else {
          replyCount = commentItem.reply_count || 0;
        }
        const currentLoadedCount = commentItem.replies ? commentItem.replies.length : 0;
        const hasNoMoreReplies = replies.length < 10 || currentLoadedCount >= replyCount;
        this.$set(commentItem, "has_more_replies", !hasNoMoreReplies);
        this.$set(commentItem, "reply_count", Math.max(replyCount, currentLoadedCount));
        this.updateReplyIndices();
        this.$set(commentItem, "loading_replies", false);
        this.isLoadingReplies = false;
      }
    },
    // 评论加载更多 - 优化版本
    commentReachBottom() {
      if (this.isLoadingComments) {
        this.debugLog("正在加载评论，跳过触底加载");
        return;
      }
      if (!this.isEmpty && this.commentList.length && this.loadStatus !== "no-more") {
        this.debugLog("触底加载更多评论", {
          当前页码: this.page,
          评论总数: this.commentList.length
        });
        this.page = this.page + 1;
        this.loadStatus = "loading";
        this.getCommentList();
      } else {
        this.debugLog("无更多评论可加载", {
          isEmpty: this.isEmpty,
          评论数量: this.commentList.length,
          loadStatus: this.loadStatus
        });
      }
    },
    // 优化页面滚动事件处理
    handlePageScroll(e) {
      if (!e || !this.isPageActive)
        return;
      const scrollTop = e.scrollTop;
      const direction = scrollTop > this.lastScrollTop ? "down" : "up";
      this.lastScrollTop = scrollTop;
      if (direction === "down" && !this.actionInProgress && scrollTop > 300 && this.loadStatus === "more") {
        this.preloadComments();
      }
    },
    // 预加载评论 - 性能优化
    preloadComments() {
      if (this.debounceTimer)
        clearTimeout(this.debounceTimer);
      this.debounceTimer = setTimeout(() => {
        if (this.page > 1 && !this.commentCache[this.page + 1]) {
          this.fetchCommentsForPage(this.page + 1, true);
        }
      }, 300);
    },
    // 处理音频URL
    formatAudioUrl(url) {
      if (!url)
        return "";
      if (!url.startsWith("http")) {
        if (url.startsWith("//")) {
          return "https:" + url;
        }
        if (url.startsWith("/")) {
          return "https://yourdomain.com" + url;
        }
        return "https://yourdomain.com/" + url;
      }
      return url;
    },
    // 音频播放 - 只有音频动态时才执行
    audioBgClick() {
      if (!this.isAudioNote) {
        common_vendor.index.__f__("log", "at pages/note/video.vue:2595", "非音频动态，不执行音频播放逻辑");
        return;
      }
      try {
        if (this.bgAudioStatus) {
          this.pauseAudio();
        } else {
          this.playAudio();
        }
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/note/video.vue:2609", "音频控制异常:", e);
        this.handleAudioError();
      }
    },
    // 暂停音频
    pauseAudio() {
      if (!this.isAudioNote || !this.bgAudioManager)
        return;
      try {
        this.bgAudioManager.pause();
        this.bgAudioStatus = false;
        common_vendor.index.__f__("log", "at pages/note/video.vue:2621", "音频已暂停");
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/note/video.vue:2623", "暂停音频失败:", e);
        this.handleAudioError();
      }
    },
    // 播放音频 - 只有音频动态时才执行
    playAudio() {
      if (!this.isAudioNote) {
        common_vendor.index.__f__("log", "at pages/note/video.vue:2632", "非音频动态，不执行音频播放逻辑");
        return;
      }
      if (!this.noteInfo.audio) {
        return this.opTipsPopup("音频资源不可用");
      }
      if (this.bgAudioManager) {
        try {
          common_vendor.index.__f__("log", "at pages/note/video.vue:2644", "继续播放现有音频");
          this.bgAudioManager.play();
          this.bgAudioStatus = true;
          return;
        } catch (e) {
          common_vendor.index.__f__("error", "at pages/note/video.vue:2649", "播放现有音频失败，重新创建:", e);
          this.createAudioInstance();
        }
      } else {
        this.createAudioInstance();
      }
    },
    // 创建音频实例 - 只有音频动态时才执行
    createAudioInstance() {
      if (!this.isAudioNote)
        return;
      try {
        common_vendor.index.showToast({
          title: "加载音频中...",
          icon: "loading",
          mask: true
        });
        this.bgAudioManager = common_vendor.index.getBackgroundAudioManager();
        this.bgAudioManager.title = this.noteInfo.audio_title || "音频";
        this.bgAudioManager.singer = this.noteInfo.user_info.nickname || "未知作者";
        this.bgAudioManager.coverImgUrl = this.noteInfo.audio_cover || "/static/img/audio_default_cover.png";
        this.bgAudioManager.epname = "笔记音频";
        this.audioPlayingId = this.noteInfo.id + "_" + Date.now();
        const currentAudioId = this.audioPlayingId;
        this.setupAudioListeners(currentAudioId);
        const audioUrl = this.formatAudioUrl(this.noteInfo.audio);
        this.bgAudioManager.src = audioUrl;
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/note/video.vue:2703", "创建音频实例异常:", e);
        this.handleAudioError();
      }
    },
    // 设置音频事件监听 - 只有音频动态时才执行
    setupAudioListeners(currentAudioId) {
      if (!this.isAudioNote || !this.bgAudioManager)
        return;
      try {
        this.bgAudioManager.onPlay(() => {
          if (this.audioPlayingId !== currentAudioId)
            return;
          common_vendor.index.hideToast();
          this.bgAudioStatus = true;
          common_vendor.index.__f__("log", "at pages/note/video.vue:2719", "音频开始播放");
        });
        this.bgAudioManager.onError((err) => {
          if (this.audioPlayingId !== currentAudioId)
            return;
          common_vendor.index.__f__("error", "at pages/note/video.vue:2726", "音频播放错误:", err);
          this.handleAudioError(err);
        });
        this.bgAudioManager.onEnded(() => {
          if (this.audioPlayingId !== currentAudioId)
            return;
          common_vendor.index.__f__("log", "at pages/note/video.vue:2732", "音频播放结束");
          this.bgAudioStatus = false;
        });
        this.bgAudioManager.onStop(() => {
          if (this.audioPlayingId !== currentAudioId)
            return;
          common_vendor.index.__f__("log", "at pages/note/video.vue:2738", "音频播放停止");
          this.bgAudioStatus = false;
        });
        this.bgAudioManager.onPause(() => {
          if (this.audioPlayingId !== currentAudioId)
            return;
          common_vendor.index.__f__("log", "at pages/note/video.vue:2744", "音频播放暂停");
          this.bgAudioStatus = false;
        });
        this.bgAudioManager.onWaiting(() => {
          if (this.audioPlayingId !== currentAudioId)
            return;
          common_vendor.index.__f__("log", "at pages/note/video.vue:2750", "音频加载中");
        });
        this.bgAudioManager.onCanplay(() => {
          if (this.audioPlayingId !== currentAudioId)
            return;
          common_vendor.index.__f__("log", "at pages/note/video.vue:2755", "音频可以播放");
          common_vendor.index.hideToast();
        });
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/note/video.vue:2760", "设置音频监听器失败:", e);
        this.handleAudioError();
      }
    },
    // 处理音频错误
    handleAudioError(err = null) {
      if (!this.isAudioNote)
        return;
      common_vendor.index.hideToast();
      this.bgAudioStatus = false;
      let errorMsg = "音频播放失败，请稍后重试";
      if (err && err.errCode) {
        switch (err.errCode) {
          case 10001:
            errorMsg = "系统错误，请重启应用";
            break;
          case 10002:
            errorMsg = "网络错误，请检查网络连接";
            break;
          case 10003:
            errorMsg = "音频文件错误，请更换音频";
            break;
          case 10004:
            errorMsg = "音频格式不支持";
            break;
          default:
            errorMsg = "音频播放失败，错误码: " + err.errCode;
        }
      }
      this.opTipsPopup(errorMsg);
      this.bgAudioManager = null;
      this.audioPlayingId = "";
    },
    // 检查音频状态 - 只有音频动态时才执行
    checkAudioStatus() {
      if (!this.isAudioNote || !this.bgAudioManager)
        return;
      try {
        common_vendor.index.__f__("log", "at pages/note/video.vue:2797", "检查音频状态");
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/note/video.vue:2799", "检查音频状态失败:", e);
      }
    },
    // 销毁音频实例 - 只有音频动态时才执行
    destroyAudioInstance() {
      if (!this.isAudioNote) {
        common_vendor.index.__f__("log", "at pages/note/video.vue:2806", "非音频动态，不执行音频销毁逻辑");
        return;
      }
      common_vendor.index.__f__("log", "at pages/note/video.vue:2810", "销毁音频实例");
      if (this.bgAudioManager) {
        try {
          if (this.bgAudioStatus) {
            this.bgAudioManager.stop();
          }
          try {
            if (this.bgAudioManager.offPlay) {
              this.bgAudioManager.offPlay();
              this.bgAudioManager.offPause();
              this.bgAudioManager.offStop();
              this.bgAudioManager.offEnded();
              this.bgAudioManager.offTimeUpdate();
              this.bgAudioManager.offWaiting();
              this.bgAudioManager.offCanplay();
              this.bgAudioManager.offError();
            }
          } catch (e) {
            common_vendor.index.__f__("error", "at pages/note/video.vue:2840", "微信小程序取消音频事件监听失败:", e);
          }
          this.bgAudioManager = null;
          this.bgAudioStatus = false;
          this.audioPlayingId = "";
          common_vendor.index.__f__("log", "at pages/note/video.vue:2849", "音频实例销毁完成");
        } catch (e) {
          common_vendor.index.__f__("error", "at pages/note/video.vue:2851", "处理音频实例销毁过程中出错:", e);
          this.bgAudioManager = null;
          this.bgAudioStatus = false;
          this.audioPlayingId = "";
        }
      }
    },
    // 音频进度变化
    onAudioProgressChange(e) {
      if (!this.isAudioNote) {
        common_vendor.index.__f__("log", "at pages/note/video.vue:2863", "非音频动态，不处理音频进度变化");
        return;
      }
      if (!this.bgAudioManager || !this.duration)
        return;
      const seekTime = e.detail.value / 100 * this.duration;
      this.bgAudioManager.seek(seekTime);
    },
    // 格式化时间显示
    formatTime(seconds) {
      if (!seconds || isNaN(seconds))
        return "00:00";
      const mins = Math.floor(seconds / 60);
      const secs = Math.floor(seconds % 60);
      return `${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
    },
    // 格式化日期为字符串
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      const hours = String(date.getHours()).padStart(2, "0");
      const minutes = String(date.getMinutes()).padStart(2, "0");
      return `${year}-${month}-${day} ${hours}:${minutes}`;
    },
    // 获取评论列表 - 优化版本
    getCommentList() {
      if (this.isLoadingComments) {
        this.debugLog("正在加载评论，跳过重复请求");
        return;
      }
      if (this.loadStatus === "no-more" && this.page > 1) {
        this.debugLog("已无更多评论，跳过请求");
        return;
      }
      this.isLoadingComments = true;
      if (this.page === 1) {
        common_vendor.index.showLoading({
          title: "加载中",
          mask: true
        });
      } else {
        this.loadStatus = "loading";
      }
      const params = {
        type: 0,
        // 动态评论类型
        page: this.page || 1,
        sort_type: this.cType || 0
        // 0-默认，1-最新
      };
      this.debugLog("获取评论列表", {
        动态ID: this.noteInfo.id,
        页码: params.page,
        排序方式: params.sort_type === 0 ? "默认" : "最新"
      });
      api_social.getCommentsList(this.noteInfo.id, params).then((res) => {
        if (this.page === 1) {
          common_vendor.index.hideLoading();
        }
        this.isLoadingComments = false;
        if (res.status === 200) {
          this.debugLog("评论列表获取成功", res.data);
          const list = res.data.list || [];
          if (res.data.total !== void 0 && res.data.total !== null) {
            this.noteInfo.comments = parseInt(res.data.total) || 0;
            this.debugLog("从API获取总评论数", this.noteInfo.comments);
          } else if (res.data.count !== void 0 && res.data.count !== null) {
            this.noteInfo.comments = parseInt(res.data.count) || 0;
            this.debugLog("从API获取总评论数(count字段)", this.noteInfo.comments);
          }
          const processedList = list.map((item) => {
            const avatar = item.avatar || item.user_info && item.user_info.avatar || "/static/img/avatar_default.png";
            const nickname = item.nickname || item.user_info && item.user_info.nickname || "用户";
            const uid = item.uid || item.user_info && item.user_info.uid || 0;
            return {
              ...item,
              // 确保基本字段存在
              id: item.id || 0,
              uid,
              nickname,
              avatar,
              // 保存原始用户信息对象，同时确保字段一致性
              user_info: {
                uid,
                nickname,
                avatar
              },
              reply_count: parseInt(item.reply_count) || 0,
              like_count: parseInt(item.like_count) || parseInt(item.likes) || 0,
              likes: parseInt(item.likes) || parseInt(item.like_count) || 0,
              is_like: !!item.is_like,
              create_time: item.create_time || item.add_time || "",
              content: item.content || "",
              image: item.image || "",
              status: item.status || 5,
              delete_time: item.delete_time || null,
              // 处理回复列表
              replies: Array.isArray(item.replies) ? item.replies.map((reply) => {
                const replyAvatar = reply.avatar || reply.user_info && reply.user_info.avatar || "/static/img/avatar_default.png";
                const replyNickname = reply.nickname || reply.user_info && reply.user_info.nickname || "用户";
                const replyUid = reply.uid || reply.user_info && reply.user_info.uid || 0;
                return {
                  ...reply,
                  id: reply.id || 0,
                  uid: replyUid,
                  nickname: replyNickname,
                  avatar: replyAvatar,
                  user_info: {
                    uid: replyUid,
                    nickname: replyNickname,
                    avatar: replyAvatar
                  },
                  reply_uid: reply.reply_uid || reply.to_uid || 0,
                  reply_nickname: reply.reply_nickname || reply.to_nickname || "",
                  like_count: parseInt(reply.like_count) || parseInt(reply.likes) || 0,
                  likes: parseInt(reply.likes) || parseInt(reply.like_count) || 0,
                  is_like: !!reply.is_like,
                  create_time: reply.create_time || reply.add_time || "",
                  content: reply.content || "",
                  image: reply.image || "",
                  status: reply.status || 5,
                  delete_time: reply.delete_time || null
                };
              }) : [],
              // 添加回复分页相关字段
              replyPage: 1,
              has_more_replies: item.reply_count > (item.replies ? item.replies.length : 0)
            };
          });
          if (this.page === 1) {
            this.commentList = processedList;
            this.isEmpty = processedList.length === 0;
          } else {
            this.commentList = [...this.commentList, ...processedList];
          }
          if (list.length < 10) {
            this.loadStatus = "no-more";
          } else {
            this.loadStatus = "more";
          }
          if (this.page === 1) {
            const cacheKey = `comments_${this.noteInfo.id}_${this.cType}_${this.page}`;
            this.commentsCache[cacheKey] = {
              list: processedList,
              isEmpty: this.isEmpty,
              loadStatus: this.loadStatus,
              totalComments: this.noteInfo.comments
            };
          }
          this.updateReplyIndices();
          this.debugLog("评论列表更新完成", {
            当前页码: this.page,
            评论总数: this.commentList.length,
            加载状态: this.loadStatus,
            是否为空: this.isEmpty
          });
        } else {
          this.debugLog("评论列表获取失败", res);
          this.loadStatus = "no-more";
          common_vendor.index.showToast({
            title: res.msg || "获取评论失败",
            icon: "none"
          });
        }
      }).catch((err) => {
        if (this.page === 1) {
          common_vendor.index.hideLoading();
        }
        this.isLoadingComments = false;
        this.loadStatus = "no-more";
        this.debugLog("获取评论列表异常", err);
        common_vendor.index.showToast({
          title: "获取评论失败",
          icon: "none"
        });
      });
    },
    // 切换评论排序
    commentClick(type) {
      if (!this.isThrottling || this.actionInProgress)
        return;
      if (this.cType === type)
        return;
      this.isThrottling = false;
      this.cType = type;
      this.page = 1;
      this.loadStatus = "loading";
      this.commentList = [];
      this.isEmpty = false;
      this.getCommentList();
      setTimeout(() => {
        this.isThrottling = true;
      }, 500);
    },
    // 打开评论框
    openComment(e) {
      e = e || { currentTarget: { dataset: { type: 0 } } };
      e.stopPropagation && e.stopPropagation();
      common_vendor.index.__f__("log", "at pages/note/video.vue:3132", "尝试打开评论框，用户状态:", {
        isUser: this.isUser,
        userId: this.userId,
        userInfo: {
          avatar: this.userAvatar,
          nickname: this.userNickname
        }
      });
      if (!this.isUser) {
        common_vendor.index.__f__("log", "at pages/note/video.vue:3142", "用户未完善资料，无法评论");
        this.opTipsPopup("完善账号资料后即可评论！");
        setTimeout(() => {
          common_vendor.index.navigateTo({
            url: "/pages/center/means"
          });
        }, 1e3);
        return;
      }
      let dataset = e.currentTarget.dataset || {};
      let type = dataset.type || 0;
      let uid = dataset.uid || 0;
      let cid = dataset.cid || 0;
      let name = dataset.name || "";
      this.cIdx = dataset.idx !== void 0 ? dataset.idx : -1;
      this.cI = dataset.i !== void 0 ? dataset.i : -1;
      this.isComment = false;
      this.isSubmittingComment = false;
      if (type == 1) {
        this.cCId = cid;
        this.cUId = uid;
        this.comtips = "回复：" + name;
      } else {
        this.cCId = 0;
        this.cUId = 0;
        this.comtips = "说点什么...";
      }
      this.$nextTick(() => {
        this.isComment = true;
        setTimeout(() => {
          this.isFocus = true;
          if (!this.isComment) {
            this.isComment = true;
          }
        }, 150);
      });
    },
    // 关闭评论框
    closeComment(e) {
      e && e.stopPropagation && e.stopPropagation();
      common_vendor.index.__f__("log", "at pages/note/video.vue:3200", "手动关闭评论框");
      if (this.isSubmittingComment) {
        return;
      }
      if (this.commentBlurTimer) {
        clearTimeout(this.commentBlurTimer);
        this.commentBlurTimer = null;
      }
      this.commentActioning = false;
      this.isComment = false;
      this.isFocus = false;
      this.comtext = "";
    },
    // 处理评论提交
    handleCommentSubmit(commentData) {
      if (this.isSubmittingComment)
        return;
      this.isSubmittingComment = true;
      const content = commentData.content;
      const image = commentData.image;
      if (!content && !image) {
        this.isSubmittingComment = false;
        return this.opTipsPopup("表达你的态度再评论吧！");
      }
      common_vendor.index.showLoading({
        title: "发布中",
        mask: true
      });
      this.isComment = false;
      this.isFocus = false;
      this.showEmoji = false;
      const params = {
        dynamic_id: this.noteInfo.id,
        content,
        pid: this.cCId || 0,
        to_uid: this.cUId || 0
      };
      if (image) {
        params.image = image;
      }
      api_social.addComment(params).then((res) => {
        common_vendor.index.hideLoading();
        if (res.status === 200) {
          const commentData2 = res.data || this.createDefaultCommentData(content, image);
          this.processCommentSuccess(commentData2);
          this.opTipsPopup("评论成功");
          if (this.isEmpty) {
            this.isEmpty = false;
          }
        } else {
          this.opTipsPopup(res.msg || "评论失败");
        }
      }).catch((err) => {
        common_vendor.index.hideLoading();
        this.opTipsPopup("评论失败，请重试");
        common_vendor.index.__f__("error", "at pages/note/video.vue:3289", "评论请求异常", err);
      }).finally(() => {
        this.isSubmittingComment = false;
        this.cCId = 0;
        this.cUId = 0;
        this.comtips = "说点什么...";
        this.comtext = "";
      });
    },
    // 创建默认评论数据（当API返回为空时）
    createDefaultCommentData(content, imageUrl) {
      var _a, _b, _c;
      const tempId = `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const userProvince = ((_a = this.noteInfo) == null ? void 0 : _a.province) || ((_c = (_b = this.$store.state.app) == null ? void 0 : _b.userInfo) == null ? void 0 : _c.province) || "";
      const commentData = {
        id: tempId,
        // 使用更安全的临时ID
        uid: this.userId,
        nickname: this.userNickname || "用户",
        avatar: this.userAvatar || "/static/img/avatar_default.png",
        content: content || "",
        image: imageUrl || "",
        // 评论图片URL
        create_time: this.formatDate(/* @__PURE__ */ new Date()),
        likes: 0,
        like_count: 0,
        // 兼容不同字段名
        is_like: false,
        status: 5,
        // 正常状态：5
        province: userProvince,
        delete_time: null,
        // 删除时间，用于标记是否被删除
        replies: [],
        // 初始化回复数组
        reply_count: 0,
        // 回复数量
        has_more_replies: false,
        // 是否有更多回复
        replyPage: 1,
        // 回复页码
        loading_replies: false
        // 是否正在加载回复
      };
      common_vendor.index.__f__("log", "at pages/note/video.vue:3333", "创建默认评论数据:", commentData);
      return commentData;
    },
    // 处理评论成功
    processCommentSuccess(commentData) {
      common_vendor.index.__f__("log", "at pages/note/video.vue:3339", "处理评论成功", {
        commentData,
        cIdx: this.cIdx,
        cI: this.cI,
        noteInfoComments: this.noteInfo.comments
      });
      if (!commentData) {
        common_vendor.index.__f__("error", "at pages/note/video.vue:3348", "评论数据为空，无法处理");
        return;
      }
      this.noteInfo.comments = (this.noteInfo.comments || 0) + 1;
      if (this.cIdx >= 0) {
        common_vendor.index.__f__("log", "at pages/note/video.vue:3357", "处理回复评论", this.cIdx);
        if (!this.commentList[this.cIdx].replies) {
          this.commentList[this.cIdx].replies = [];
        }
        if (this.commentList[this.cIdx].reply_count === void 0) {
          this.commentList[this.cIdx].reply_count = 0;
        }
        if (this.cUId) {
          const nickname = this.comtips.replace("回复：", "");
          commentData.reply_uid = this.cUId;
          commentData.reply_nickname = nickname;
        }
        this.commentList[this.cIdx].replies.push(commentData);
        this.commentList[this.cIdx].reply_count++;
        this.replyIndices.set(commentData.id, this.commentList[this.cIdx].replies.length - 1);
        this.$forceUpdate();
        common_vendor.index.__f__("log", "at pages/note/video.vue:3384", "回复评论处理完成", {
          回复数量: this.commentList[this.cIdx].reply_count,
          回复列表: this.commentList[this.cIdx].replies.length
        });
      } else {
        common_vendor.index.__f__("log", "at pages/note/video.vue:3391", "处理新评论");
        if (this.isEmpty || this.commentList.length === 0) {
          this.isEmpty = false;
          this.commentList = [];
          common_vendor.index.__f__("log", "at pages/note/video.vue:3396", "重置评论列表");
        }
        commentData.replies = [];
        commentData.reply_count = 0;
        this.commentList.unshift(commentData);
        this.$forceUpdate();
        common_vendor.index.__f__("log", "at pages/note/video.vue:3409", "新评论处理完成", {
          评论列表长度: this.commentList.length,
          评论总数: this.noteInfo.comments
        });
      }
    },
    // 删除评论
    delComment(e) {
      let self = this;
      let idx = e.currentTarget.dataset.idx;
      let i = e.currentTarget.dataset.i;
      let commentId = e.currentTarget.dataset.id;
      if (self.isDeletingComment)
        return;
      common_vendor.index.showModal({
        content: "确定要永久删除该评论吗？",
        confirmColor: "#FA5150",
        success: function(res) {
          if (res.confirm) {
            self.isDeletingComment = true;
            common_vendor.index.showLoading({
              title: "删除中",
              mask: true
            });
            api_social.deleteComment(commentId).then((res2) => {
              common_vendor.index.hideLoading();
              self.isDeletingComment = false;
              if (res2.status === 200) {
                if (self.noteInfo.comments > 0) {
                  self.noteInfo.comments--;
                }
                if (i == -1) {
                  self.commentList[idx].delete_time = (/* @__PURE__ */ new Date()).toISOString();
                  self.commentList[idx].status = 0;
                } else {
                  if (self.commentList[idx].replies && self.commentList[idx].replies.length > i) {
                    self.commentList[idx].replies[i].delete_time = (/* @__PURE__ */ new Date()).toISOString();
                    self.commentList[idx].replies[i].status = 0;
                    if (self.commentList[idx].reply_count > 0) {
                      self.commentList[idx].reply_count--;
                    }
                  }
                }
                self.opTipsPopup("删除成功");
              } else {
                self.opTipsPopup(res2.msg || "删除失败");
              }
            }).catch((err) => {
              common_vendor.index.hideLoading();
              self.isDeletingComment = false;
              self.opTipsPopup("删除失败");
            });
          }
        }
      });
    },
    // 点赞/取消点赞评论
    toggleCommentLike(commentId, isCurrentlyLiked) {
      if (!this.isUser) {
        this.opTipsPopup("请先完善账号资料");
        return;
      }
      if (this.likeThrottling)
        return;
      this.likeThrottling = true;
      setTimeout(() => {
        this.likeThrottling = false;
      }, 500);
      const currentLikeState = isCurrentlyLiked ? 1 : 0;
      const newLikeState = currentLikeState ? 0 : 1;
      for (let i = 0; i < this.commentList.length; i++) {
        const comment = this.commentList[i];
        if (comment.id === commentId) {
          comment.is_like = newLikeState;
          if (newLikeState) {
            if (comment.like_count !== void 0) {
              comment.like_count = (comment.like_count || 0) + 1;
            }
            if (comment.likes !== void 0) {
              comment.likes = (comment.likes || 0) + 1;
            }
          } else {
            if (comment.like_count !== void 0 && comment.like_count > 0) {
              comment.like_count--;
            }
            if (comment.likes !== void 0 && comment.likes > 0) {
              comment.likes--;
            }
          }
          break;
        }
        if (comment.replies && comment.replies.length > 0) {
          for (let j = 0; j < comment.replies.length; j++) {
            const reply = comment.replies[j];
            if (reply.id === commentId) {
              reply.is_like = newLikeState;
              if (newLikeState) {
                if (reply.like_count !== void 0) {
                  reply.like_count = (reply.like_count || 0) + 1;
                }
                if (reply.likes !== void 0) {
                  reply.likes = (reply.likes || 0) + 1;
                }
              } else {
                if (reply.like_count !== void 0 && reply.like_count > 0) {
                  reply.like_count--;
                }
                if (reply.likes !== void 0 && reply.likes > 0) {
                  reply.likes--;
                }
              }
              break;
            }
          }
        }
      }
      if (currentLikeState) {
        api_social.unlikeComment(commentId).then((res) => {
        }).catch((err) => {
          this.restoreCommentLikeStatus(commentId, currentLikeState);
          this.opTipsPopup("操作失败，请重试");
        });
      } else {
        api_social.likeComment(commentId).then((res) => {
        }).catch((err) => {
          this.restoreCommentLikeStatus(commentId, currentLikeState);
          this.opTipsPopup("操作失败，请重试");
        });
      }
    },
    // 恢复评论点赞状态（操作失败时）
    restoreCommentLikeStatus(commentId, originalLikeState) {
      for (let i = 0; i < this.commentList.length; i++) {
        const comment = this.commentList[i];
        if (comment.id === commentId) {
          comment.is_like = originalLikeState;
          if (originalLikeState === 1) {
            if (comment.like_count !== void 0 && comment.like_count > 0) {
              comment.like_count--;
            }
            if (comment.likes !== void 0 && comment.likes > 0) {
              comment.likes--;
            }
          } else {
            if (comment.like_count !== void 0) {
              comment.like_count = (comment.like_count || 0) + 1;
            }
            if (comment.likes !== void 0) {
              comment.likes = (comment.likes || 0) + 1;
            }
          }
          break;
        }
        if (comment.replies && comment.replies.length > 0) {
          for (let j = 0; j < comment.replies.length; j++) {
            const reply = comment.replies[j];
            if (reply.id === commentId) {
              reply.is_like = originalLikeState;
              if (originalLikeState === 1) {
                if (reply.like_count !== void 0 && reply.like_count > 0) {
                  reply.like_count--;
                }
                if (reply.likes !== void 0 && reply.likes > 0) {
                  reply.likes--;
                }
              } else {
                if (reply.like_count !== void 0) {
                  reply.like_count = (reply.like_count || 0) + 1;
                }
                if (reply.likes !== void 0) {
                  reply.likes = (reply.likes || 0) + 1;
                }
              }
              break;
            }
          }
        }
      }
    },
    // 递归更新回复索引映射
    updateReplyIndices() {
      if (!this.commentList || !this.commentList.length)
        return;
      this.replyIndices = /* @__PURE__ */ new Map();
      this.commentList.forEach((comment, commentIndex) => {
        if (comment.replies && comment.replies.length) {
          comment.replies.forEach((reply, replyIndex) => {
            this.replyIndices.set(reply.id, replyIndex);
          });
        }
      });
      this.debugLog("更新回复索引映射完成", {
        索引数量: this.replyIndices.size
      });
    },
    // 加载评论回复 - 与details.vue保持一致
    sonComment(e) {
      let id = parseInt(e.currentTarget.dataset.id) || 0;
      let idx = parseInt(e.currentTarget.dataset.idx) || 0;
      this.debugLog("加载评论回复:", {
        commentId: id,
        commentIndex: idx
      });
      if (this.isLoadingReplies)
        return;
      this.isLoadingReplies = true;
      const commentItem = this.commentList[idx];
      if (commentItem) {
        this.$set(commentItem, "loading_replies", true);
      }
      const currentPage = parseInt(commentItem.replyPage) || 1;
      const cacheKey = `replies_${id}_${currentPage}`;
      if (this.commentCache && this.commentCache[cacheKey]) {
        this.debugLog("使用缓存中的回复数据");
        this.handleAllRepliesData(this.commentCache[cacheKey], idx, currentPage);
        return;
      }
      const params = {
        parent_id: id,
        // 父评论ID
        page: currentPage,
        // 页码
        limit: 10,
        // 每页数量
        sort_type: 1
        // 排序类型：1=最新(创建时间)
      };
      this.debugLog("回复请求参数:", params);
      api_social.getCommentReplies(params).then((res) => {
        if (res.status === 200) {
          this.debugLog("获取到回复数据:", res.data);
          if (!this.commentCache)
            this.commentCache = {};
          this.commentCache[cacheKey] = res.data;
          this.handleAllRepliesData(res.data, idx, currentPage);
        } else {
          if (commentItem) {
            this.$set(commentItem, "loading_replies", false);
          }
          this.isLoadingReplies = false;
          common_vendor.index.showToast({
            title: res.msg || "获取回复失败",
            icon: "none"
          });
        }
      }).catch((err) => {
        this.debugLog("获取回复失败:", err);
        if (commentItem) {
          this.$set(commentItem, "loading_replies", false);
        }
        this.isLoadingReplies = false;
        common_vendor.index.showToast({
          title: "获取回复失败",
          icon: "none"
        });
      });
    },
    // 处理回复数据（分页方式）- 与details.vue保持一致
    handleAllRepliesData(data, idx, page) {
      if (this.commentList[idx]) {
        const commentItem = this.commentList[idx];
        let replies = [];
        if (data.list && Array.isArray(data.list)) {
          replies = data.list;
        } else if (Array.isArray(data)) {
          replies = data;
        } else if (data.data && Array.isArray(data.data)) {
          replies = data.data;
        } else {
          this.debugLog("无法识别的回复数据结构", data);
          replies = [];
        }
        this.debugLog("获取到回复数据:", replies);
        replies = replies.map((reply) => {
          const replyAvatar = reply.avatar || reply.user_info && reply.user_info.avatar || "/static/img/avatar_default.png";
          const replyNickname = reply.nickname || reply.user_info && reply.user_info.nickname || "用户";
          const replyUid = reply.uid || reply.user_info && reply.user_info.uid || 0;
          return {
            ...reply,
            id: reply.id || 0,
            uid: replyUid,
            nickname: replyNickname,
            avatar: replyAvatar,
            user_info: {
              uid: replyUid,
              nickname: replyNickname,
              avatar: replyAvatar
            },
            reply_uid: reply.reply_uid || reply.to_uid || 0,
            reply_nickname: reply.reply_nickname || reply.to_nickname || "",
            like_count: parseInt(reply.like_count) || parseInt(reply.likes) || 0,
            likes: parseInt(reply.likes) || parseInt(reply.like_count) || 0,
            is_like: !!reply.is_like,
            create_time: reply.create_time || reply.add_time || "",
            content: reply.content || "",
            image: reply.image || "",
            status: reply.status || 5,
            delete_time: reply.delete_time || null
          };
        });
        if (page === 1) {
          this.$set(commentItem, "replies", replies);
        } else {
          const existingIds = (commentItem.replies || []).map((r) => r.id);
          const newReplies = replies.filter((r) => !existingIds.includes(r.id));
          this.$set(commentItem, "replies", [...commentItem.replies || [], ...newReplies]);
        }
        this.$set(commentItem, "replyPage", page + 1);
        let replyCount = 0;
        if (data.count !== void 0) {
          replyCount = parseInt(data.count) || 0;
        } else if (data.total !== void 0) {
          replyCount = parseInt(data.total) || 0;
        } else {
          replyCount = commentItem.reply_count || 0;
        }
        const currentLoadedCount = commentItem.replies ? commentItem.replies.length : 0;
        const hasNoMoreReplies = replies.length < 10 || currentLoadedCount >= replyCount;
        this.$set(commentItem, "has_more_replies", !hasNoMoreReplies);
        this.$set(commentItem, "reply_count", Math.max(replyCount, currentLoadedCount));
        this.updateReplyIndices();
        this.$set(commentItem, "loading_replies", false);
        this.isLoadingReplies = false;
      }
    },
    // 评论加载更多 - 优化版本
    commentReachBottom() {
      if (this.isLoadingComments) {
        this.debugLog("正在加载评论，跳过触底加载");
        return;
      }
      if (!this.isEmpty && this.commentList.length && this.loadStatus !== "no-more") {
        this.debugLog("触底加载更多评论", {
          当前页码: this.page,
          评论总数: this.commentList.length
        });
        this.page = this.page + 1;
        this.loadStatus = "loading";
        this.getCommentList();
      } else {
        this.debugLog("无更多评论可加载", {
          isEmpty: this.isEmpty,
          评论数量: this.commentList.length,
          loadStatus: this.loadStatus
        });
      }
    },
    // 优化页面滚动事件处理
    handlePageScroll(e) {
      if (!e || !this.isPageActive)
        return;
      const scrollTop = e.scrollTop;
      const direction = scrollTop > this.lastScrollTop ? "down" : "up";
      this.lastScrollTop = scrollTop;
      if (direction === "down" && !this.actionInProgress && scrollTop > 300 && this.loadStatus === "more") {
        this.preloadComments();
      }
    },
    // 预加载评论 - 性能优化
    preloadComments() {
      if (this.debounceTimer)
        clearTimeout(this.debounceTimer);
      this.debounceTimer = setTimeout(() => {
        if (this.page > 1 && !this.commentCache[this.page + 1]) {
          this.fetchCommentsForPage(this.page + 1, true);
        }
      }, 300);
    },
    // 处理音频URL
    formatAudioUrl(url) {
      if (!url)
        return "";
      if (!url.startsWith("http")) {
        if (url.startsWith("//")) {
          return "https:" + url;
        }
        if (url.startsWith("/")) {
          return "https://yourdomain.com" + url;
        }
        return "https://yourdomain.com/" + url;
      }
      return url;
    },
    // 音频播放 - 只有音频动态时才执行
    audioBgClick() {
      if (!this.isAudioNote) {
        common_vendor.index.__f__("log", "at pages/note/video.vue:3931", "非音频动态，不执行音频播放逻辑");
        return;
      }
      try {
        if (this.bgAudioStatus) {
          this.pauseAudio();
        } else {
          this.playAudio();
        }
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/note/video.vue:3945", "音频控制异常:", e);
        this.handleAudioError();
      }
    },
    // 暂停音频
    pauseAudio() {
      if (!this.isAudioNote || !this.bgAudioManager)
        return;
      try {
        this.bgAudioManager.pause();
        this.bgAudioStatus = false;
        common_vendor.index.__f__("log", "at pages/note/video.vue:3957", "音频已暂停");
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/note/video.vue:3959", "暂停音频失败:", e);
        this.handleAudioError();
      }
    },
    // 播放音频 - 只有音频动态时才执行
    playAudio() {
      if (!this.isAudioNote) {
        common_vendor.index.__f__("log", "at pages/note/video.vue:3968", "非音频动态，不执行音频播放逻辑");
        return;
      }
      if (!this.noteInfo.audio) {
        return this.opTipsPopup("音频资源不可用");
      }
      if (this.bgAudioManager) {
        try {
          common_vendor.index.__f__("log", "at pages/note/video.vue:3980", "继续播放现有音频");
          this.bgAudioManager.play();
          this.bgAudioStatus = true;
          return;
        } catch (e) {
          common_vendor.index.__f__("error", "at pages/note/video.vue:3985", "播放现有音频失败，重新创建:", e);
          this.createAudioInstance();
        }
      } else {
        this.createAudioInstance();
      }
    },
    // 创建音频实例 - 只有音频动态时才执行
    createAudioInstance() {
      if (!this.isAudioNote)
        return;
      try {
        common_vendor.index.showToast({
          title: "加载音频中...",
          icon: "loading",
          mask: true
        });
        this.bgAudioManager = common_vendor.index.getBackgroundAudioManager();
        this.bgAudioManager.title = this.noteInfo.audio_title || "音频";
        this.bgAudioManager.singer = this.noteInfo.user_info.nickname || "未知作者";
        this.bgAudioManager.coverImgUrl = this.noteInfo.audio_cover || "/static/img/audio_default_cover.png";
        this.bgAudioManager.epname = "笔记音频";
        this.audioPlayingId = this.noteInfo.id + "_" + Date.now();
        const currentAudioId = this.audioPlayingId;
        this.setupAudioListeners(currentAudioId);
        const audioUrl = this.formatAudioUrl(this.noteInfo.audio);
        this.bgAudioManager.src = audioUrl;
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/note/video.vue:4039", "创建音频实例异常:", e);
        this.handleAudioError();
      }
    },
    // 设置音频事件监听 - 只有音频动态时才执行
    setupAudioListeners(currentAudioId) {
      if (!this.isAudioNote || !this.bgAudioManager)
        return;
      try {
        this.bgAudioManager.onPlay(() => {
          if (this.audioPlayingId !== currentAudioId)
            return;
          common_vendor.index.hideToast();
          this.bgAudioStatus = true;
          common_vendor.index.__f__("log", "at pages/note/video.vue:4055", "音频开始播放");
        });
        this.bgAudioManager.onError((err) => {
          if (this.audioPlayingId !== currentAudioId)
            return;
          common_vendor.index.__f__("error", "at pages/note/video.vue:4062", "音频播放错误:", err);
          this.handleAudioError(err);
        });
        this.bgAudioManager.onEnded(() => {
          if (this.audioPlayingId !== currentAudioId)
            return;
          common_vendor.index.__f__("log", "at pages/note/video.vue:4068", "音频播放结束");
          this.bgAudioStatus = false;
        });
        this.bgAudioManager.onStop(() => {
          if (this.audioPlayingId !== currentAudioId)
            return;
          common_vendor.index.__f__("log", "at pages/note/video.vue:4074", "音频播放停止");
          this.bgAudioStatus = false;
        });
        this.bgAudioManager.onPause(() => {
          if (this.audioPlayingId !== currentAudioId)
            return;
          common_vendor.index.__f__("log", "at pages/note/video.vue:4080", "音频播放暂停");
          this.bgAudioStatus = false;
        });
        this.bgAudioManager.onWaiting(() => {
          if (this.audioPlayingId !== currentAudioId)
            return;
          common_vendor.index.__f__("log", "at pages/note/video.vue:4086", "音频加载中");
        });
        this.bgAudioManager.onCanplay(() => {
          if (this.audioPlayingId !== currentAudioId)
            return;
          common_vendor.index.__f__("log", "at pages/note/video.vue:4091", "音频可以播放");
          common_vendor.index.hideToast();
        });
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/note/video.vue:4096", "设置音频监听器失败:", e);
        this.handleAudioError();
      }
    },
    // 处理音频错误
    handleAudioError(err = null) {
      if (!this.isAudioNote)
        return;
      common_vendor.index.hideToast();
      this.bgAudioStatus = false;
      let errorMsg = "音频播放失败，请稍后重试";
      if (err && err.errCode) {
        switch (err.errCode) {
          case 10001:
            errorMsg = "系统错误，请重启应用";
            break;
          case 10002:
            errorMsg = "网络错误，请检查网络连接";
            break;
          case 10003:
            errorMsg = "音频文件错误，请更换音频";
            break;
          case 10004:
            errorMsg = "音频格式不支持";
            break;
          default:
            errorMsg = "音频播放失败，错误码: " + err.errCode;
        }
      }
      this.opTipsPopup(errorMsg);
      this.bgAudioManager = null;
      this.audioPlayingId = "";
    },
    // 检查音频状态 - 只有音频动态时才执行
    checkAudioStatus() {
      if (!this.isAudioNote || !this.bgAudioManager)
        return;
      try {
        common_vendor.index.__f__("log", "at pages/note/video.vue:4133", "检查音频状态");
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/note/video.vue:4135", "检查音频状态失败:", e);
      }
    },
    // 销毁音频实例 - 只有音频动态时才执行
    destroyAudioInstance() {
      if (!this.isAudioNote) {
        common_vendor.index.__f__("log", "at pages/note/video.vue:4142", "非音频动态，不执行音频销毁逻辑");
        return;
      }
      common_vendor.index.__f__("log", "at pages/note/video.vue:4146", "销毁音频实例");
      if (this.bgAudioManager) {
        try {
          if (this.bgAudioStatus) {
            this.bgAudioManager.stop();
          }
          try {
            if (this.bgAudioManager.offPlay) {
              this.bgAudioManager.offPlay();
              this.bgAudioManager.offPause();
              this.bgAudioManager.offStop();
              this.bgAudioManager.offEnded();
              this.bgAudioManager.offTimeUpdate();
              this.bgAudioManager.offWaiting();
              this.bgAudioManager.offCanplay();
              this.bgAudioManager.offError();
            }
          } catch (e) {
            common_vendor.index.__f__("error", "at pages/note/video.vue:4176", "微信小程序取消音频事件监听失败:", e);
          }
          this.bgAudioManager = null;
          this.bgAudioStatus = false;
          this.audioPlayingId = "";
          common_vendor.index.__f__("log", "at pages/note/video.vue:4185", "音频实例销毁完成");
        } catch (e) {
          common_vendor.index.__f__("error", "at pages/note/video.vue:4187", "处理音频实例销毁过程中出错:", e);
          this.bgAudioManager = null;
          this.bgAudioStatus = false;
          this.audioPlayingId = "";
        }
      }
    },
    // 音频进度变化
    onAudioProgressChange(e) {
      if (!this.isAudioNote) {
        common_vendor.index.__f__("log", "at pages/note/video.vue:4199", "非音频动态，不处理音频进度变化");
        return;
      }
      if (!this.bgAudioManager || !this.duration)
        return;
      const seekTime = e.detail.value / 100 * this.duration;
      this.bgAudioManager.seek(seekTime);
    },
    // 格式化时间显示
    formatTime(seconds) {
      if (!seconds || isNaN(seconds))
        return "00:00";
      const mins = Math.floor(seconds / 60);
      const secs = Math.floor(seconds % 60);
      return `${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
    },
    // 优化后的表情内容解析
    parseEmojiContent(text) {
      if (!text || typeof text !== "string")
        return [];
      const cacheKey = text;
      if (this.parsedContentCache.has(cacheKey)) {
        return this.parsedContentCache.get(cacheKey);
      }
      const nodes = [];
      let lastIndex = 0;
      const regex = /\[([^\[\]]{1,10})\]/g;
      let match;
      try {
        while ((match = regex.exec(text)) !== null) {
          if (match.index > lastIndex) {
            const textContent = text.substring(lastIndex, match.index);
            if (textContent) {
              nodes.push({
                type: "text",
                text: textContent
              });
            }
          }
          const emojiPhrase = match[0];
          const emoji = this.emojiMap.get(emojiPhrase);
          if (emoji && emoji.url) {
            nodes.push({
              type: "image",
              attrs: {
                src: emoji.url,
                class: "emoji-img",
                style: "width: 32rpx !important; height: 32rpx !important; max-width: 32rpx !important; max-height: 32rpx !important; min-width: 32rpx !important; min-height: 32rpx !important; vertical-align: middle; margin: 0 4rpx; display: inline-block; border-radius: 4rpx; object-fit: cover;",
                "data-emoji": emojiPhrase,
                "data-url": emoji.url
              }
            });
          } else {
            nodes.push({
              type: "text",
              text: emojiPhrase
            });
          }
          lastIndex = regex.lastIndex;
        }
        if (lastIndex < text.length) {
          const remainingText = text.substring(lastIndex);
          if (remainingText) {
            nodes.push({
              type: "text",
              text: remainingText
            });
          }
        }
        this.cacheEmojiParseResult(cacheKey, nodes);
        return nodes;
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/note/video.vue:4292", "解析表情内容出错:", error);
        return [{
          type: "text",
          text
        }];
      }
    },
    // 缓存表情解析结果
    cacheEmojiParseResult(cacheKey, nodes) {
      if (this.parsedContentCache.size >= this.maxCacheSize) {
        const firstKey = this.parsedContentCache.keys().next().value;
        this.parsedContentCache.delete(firstKey);
      }
      this.parsedContentCache.set(cacheKey, nodes);
    },
    // 优化后的表情内容解析（用于rich-text组件）
    parseEmojiContentForRichText(text) {
      if (!text || typeof text !== "string")
        return text;
      const cacheKey = `richtext_${text}`;
      if (this.parsedContentCache.has(cacheKey)) {
        return this.parsedContentCache.get(cacheKey);
      }
      try {
        let processedText = text.replace(/\[([^\[\]]{1,10})\]/g, (match) => {
          const emoji = components_emojiPanel_sina.sinaEmoji.find((e) => e.phrase === match);
          if (emoji && emoji.url) {
            return `<img src="${emoji.url}" class="emoji-img-inline" style="width: 32rpx !important; height: 32rpx !important; max-width: 32rpx !important; max-height: 32rpx !important; min-width: 32rpx !important; min-height: 32rpx !important; vertical-align: middle; margin: 0 4rpx; display: inline-block; border-radius: 4rpx; object-fit: cover;" data-emoji="${match}" />`;
          }
          return match;
        });
        if (this.parsedContentCache.size >= this.maxCacheSize) {
          const firstKey = this.parsedContentCache.keys().next().value;
          this.parsedContentCache.delete(firstKey);
        }
        this.parsedContentCache.set(cacheKey, processedText);
        return processedText;
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/note/video.vue:4340", "解析表情内容出错:", error);
        return text;
      }
    },
    // 对回复按时间正序排序（从早到晚）
    sortRepliesByTime(replies) {
      if (!replies || !Array.isArray(replies))
        return [];
      const sortedReplies = [...replies];
      return sortedReplies.sort((a, b) => {
        const timeA = new Date(a.create_time);
        const timeB = new Date(b.create_time);
        return timeA - timeB;
      });
    },
    // 获取回复在原始数组中的索引
    getReplyIndex(replies, replyId) {
      if (!replies || !Array.isArray(replies))
        return -1;
      for (let i = 0; i < replies.length; i++) {
        if (replies[i].id === replyId) {
          return i;
        }
      }
      return -1;
    },
    // 表情点击预览（可选功能）
    onEmojiClick(event) {
      if (this.emojiClickTimer) {
        clearTimeout(this.emojiClickTimer);
      }
      this.emojiClickTimer = setTimeout(() => {
        const target = event.target || event.currentTarget;
        const emojiPhrase = target.getAttribute("data-emoji");
        const emojiUrl = target.getAttribute("data-url");
        if (emojiPhrase && emojiUrl) {
          this.showEmojiPreview(emojiPhrase, emojiUrl);
        }
      }, 300);
    },
    // 显示表情预览
    showEmojiPreview(phrase, url) {
      this.previewEmojiData = {
        phrase,
        url,
        timestamp: Date.now()
      };
      common_vendor.index.showActionSheet({
        itemList: ["复制表情", "查看大图"],
        success: (res) => {
          if (res.tapIndex === 0) {
            common_vendor.index.setClipboardData({
              data: phrase,
              success: () => {
                common_vendor.index.showToast({
                  title: "表情已复制",
                  icon: "success"
                });
              }
            });
          } else if (res.tapIndex === 1) {
            common_vendor.index.previewImage({
              urls: [url],
              current: url
            });
          }
        }
      });
    },
    // 清理表情缓存
    clearEmojiCache() {
      this.parsedContentCache.clear();
      common_vendor.index.__f__("log", "at pages/note/video.vue:4432", "表情缓存已清理");
    },
    // 获取表情缓存统计
    getEmojiCacheStats() {
      return {
        emojiMapSize: this.emojiMap.size,
        parsedContentCacheSize: this.parsedContentCache.size,
        maxCacheSize: this.maxCacheSize
      };
    },
    // 动态详情获取
    dynamicDetails() {
      if (this.isLoadingDetail) {
        this.debugLog("正在加载详情，跳过重复请求");
        return;
      }
      this.isLoadingDetail = true;
      common_vendor.index.showLoading({
        title: "加载中",
        mask: true
      });
      api_social.getDynamicDetail(this.noteInfo.id).then((res) => {
        common_vendor.index.hideLoading();
        this.isLoadingDetail = false;
        if (res.status === 200) {
          this.debugLog("动态详情获取成功", res.data);
          const noteData = res.data.detail || res.data;
          this.noteInfo = {
            ...this.noteInfo,
            ...noteData,
            user_info: noteData.user_info || {
              uid: noteData.uid || 0,
              nickname: noteData.nickname || "用户",
              avatar: noteData.avatar || "/static/img/avatar_default.png"
            },
            comments: parseInt(noteData.comments || 0),
            likes: parseInt(noteData.likes || 0),
            views: parseInt(noteData.views || 0),
            shares: parseInt(noteData.shares || 0),
            is_like: !!noteData.is_like
          };
          this.processCommonData();
          this.processMediaData();
          if (noteData.user_info && noteData.user_info.is_follow !== void 0) {
            this.isFollowing = !!noteData.user_info.is_follow;
            this.followChecked = true;
          }
          this.$nextTick(() => {
            this.checkContentOverflow();
          });
          this.getCommentList();
        } else {
          this.opTipsPopup(res.msg || "获取动态详情失败", true);
        }
      }).catch((err) => {
        common_vendor.index.hideLoading();
        this.isLoadingDetail = false;
        this.opTipsPopup("获取动态详情失败", true);
        this.debugLog("获取动态详情异常", err);
      });
    },
    // 检查内容是否超出两行需要显示展开按钮
    checkContentOverflow() {
      try {
        if (!this.noteInfo.content)
          return;
        common_vendor.index.createSelectorQuery().in(this).select(".content-text").boundingClientRect((rect) => {
          if (rect) {
            const lineHeight = parseInt(common_vendor.index.getSystemInfoSync().fontSizeSetting) * 1.4;
            const maxHeight = lineHeight * 2;
            this.isContentOverflow = rect.height > maxHeight;
            this.debugLog("内容高度检查", {
              实际高度: rect.height,
              最大高度: maxHeight,
              是否超出: this.isContentOverflow
            });
          }
        }).exec();
      } catch (e) {
        this.debugLog("检查内容溢出异常", e);
      }
    },
    // 切换内容展开/收起状态
    toggleContent() {
      this.isExpanded = !this.isExpanded;
    },
    // 预览图片
    previewImage(currentImage, index) {
      if (!currentImage)
        return;
      let images = [];
      if (this.noteInfo.images && this.noteInfo.images.length > 0) {
        images = this.noteInfo.images.map((img) => {
          if (typeof img === "string")
            return img;
          return img.url || "";
        }).filter(Boolean);
      }
      if (images.length === 0 && currentImage) {
        images = [currentImage];
      }
      common_vendor.index.previewImage({
        urls: images,
        current: currentImage,
        longPressActions: {
          itemList: ["保存图片", "收藏", "分享"],
          success: (data) => {
            if (data.tapIndex === 0) {
              common_vendor.index.saveImageToPhotosAlbum({
                filePath: images[data.index],
                success: () => {
                  common_vendor.index.showToast({ title: "保存成功", icon: "success" });
                },
                fail: () => {
                  common_vendor.index.showToast({ title: "保存失败", icon: "none" });
                }
              });
            }
          }
        }
      });
    },
    // 预览评论图片
    previewCommentImage(imageUrl) {
      if (!imageUrl)
        return;
      common_vendor.index.previewImage({
        urls: [imageUrl],
        current: imageUrl,
        longPressActions: {
          itemList: ["保存图片"],
          success: (data) => {
            if (data.tapIndex === 0) {
              common_vendor.index.saveImageToPhotosAlbum({
                filePath: imageUrl,
                success: () => {
                  common_vendor.index.showToast({ title: "保存成功", icon: "success" });
                },
                fail: () => {
                  common_vendor.index.showToast({ title: "保存失败", icon: "none" });
                }
              });
            }
          }
        }
      });
    },
    // 轮播图切换
    onSwiperChange(e) {
      this.currentImageIndex = e.detail.current;
    },
    // 切换音频播放状态
    toggleAudioPlay() {
      this.audioBgClick();
    },
    // 打开评论弹窗
    commentPopupClick(isShow) {
      this.isCommentPopup = isShow;
      if (isShow) {
        if (this.commentList.length === 0) {
          common_vendor.index.__f__("log", "at pages/note/video.vue:4637", "加载评论列表");
          this.page = 1;
          this.getCommentList();
        }
        this.$refs.commentPopup.open();
      } else {
        this.$refs.commentPopup.close();
      }
    },
    // 强制应用表情样式（运行时修复）
    forceApplyEmojiStyles() {
      this.$nextTick(() => {
        try {
          const emojiImages = common_vendor.index.createSelectorQuery().in(this).selectAll("image[data-emoji], img[data-emoji]").exec((res) => {
            if (res && res[0]) {
              res[0].forEach((node, index) => {
                common_vendor.index.createSelectorQuery().in(this).select(`image[data-emoji]:nth-child(${index + 1}), img[data-emoji]:nth-child(${index + 1})`).fields({
                  node: true,
                  size: true
                }).exec((nodeRes) => {
                  if (nodeRes && nodeRes[0] && nodeRes[0].node) {
                    const node2 = nodeRes[0].node;
                    node2.style.width = "32rpx";
                    node2.style.height = "32rpx";
                    node2.style.maxWidth = "32rpx";
                    node2.style.maxHeight = "32rpx";
                    node2.style.minWidth = "32rpx";
                    node2.style.minHeight = "32rpx";
                    node2.style.objectFit = "cover";
                  }
                });
              });
            }
          });
        } catch (error) {
          common_vendor.index.__f__("warn", "at pages/note/video.vue:4684", "强制应用表情样式失败:", error);
        }
      });
    }
  }
};
if (!Array) {
  const _component_lazy_image = common_vendor.resolveComponent("lazy-image");
  const _component_comment_input = common_vendor.resolveComponent("comment-input");
  const _component_SharePanel = common_vendor.resolveComponent("SharePanel");
  const _easycom_uni_popup2 = common_vendor.resolveComponent("uni-popup");
  const _easycom_uni_load_more2 = common_vendor.resolveComponent("uni-load-more");
  (_component_lazy_image + _component_comment_input + _component_SharePanel + _easycom_uni_popup2 + _easycom_uni_load_more2)();
}
const _easycom_uni_popup = () => "../../uni_modules/uni-popup/components/uni-popup/uni-popup.js";
const _easycom_uni_load_more = () => "../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js";
if (!Math) {
  (_easycom_uni_popup + _easycom_uni_load_more)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_2$4,
    b: common_vendor.o((...args) => $options.navBack && $options.navBack(...args)),
    c: $options.isImageNote && $data.noteInfo.images && $data.noteInfo.images.length > 1
  }, $options.isImageNote && $data.noteInfo.images && $data.noteInfo.images.length > 1 ? {
    d: common_vendor.t($data.currentImageIndex + 1),
    e: common_vendor.t($data.noteInfo.images.length)
  } : {}, {
    f: $data.titleBarHeight + "px",
    g: $data.statusBarHeight + "px",
    h: $options.isVideoNote && $data.noteInfo.video
  }, $options.isVideoNote && $data.noteInfo.video ? {
    i: $data.noteInfo.video,
    j: $data.noteInfo.video_cover,
    k: $data.isCommentPopup ? "30vh" : "calc(100vh - " + $data.footerHeight + "px)"
  } : {}, {
    l: $options.isImageNote && $data.noteInfo.images && $data.noteInfo.images.length > 0
  }, $options.isImageNote && $data.noteInfo.images && $data.noteInfo.images.length > 0 ? common_vendor.e({
    m: $data.noteInfo.images.length == 1
  }, $data.noteInfo.images.length == 1 ? {
    n: $data.noteInfo.images[0],
    o: common_vendor.o(($event) => $options.previewImage($data.noteInfo.images[0]))
  } : {
    p: common_vendor.f($data.noteInfo.images, (img, index, i0) => {
      return {
        a: img,
        b: common_vendor.o(($event) => $options.previewImage(img, index), index),
        c: index
      };
    }),
    q: common_vendor.o((...args) => $options.onSwiperChange && $options.onSwiperChange(...args))
  }, {
    r: common_vendor.n($data.noteInfo.images.length == 1 ? "image-box df" : "multi-image-box"),
    s: $data.isCommentPopup ? "30vh" : "calc(100vh - " + $data.footerHeight + "px)"
  }) : {}, {
    t: $options.isAudioNote
  }, $options.isAudioNote ? common_vendor.e({
    v: common_vendor.p({
      src: $data.noteInfo.audio_cover || "/static/img/audio_icon.png",
      mode: "aspectFill"
    }),
    w: common_vendor.p({
      src: $data.noteInfo.audio_cover || "/static/img/audio_icon.png",
      mode: "aspectFill"
    }),
    x: $data.bgAudioStatus ? 1 : "",
    y: $data.bgAudioStatus ? 1 : "",
    z: $data.noteInfo.audio_title
  }, $data.noteInfo.audio_title ? {
    A: common_vendor.t($data.noteInfo.audio_title)
  } : {}, {
    B: $data.noteInfo.user_info
  }, $data.noteInfo.user_info ? {
    C: common_vendor.t($data.noteInfo.user_info.nickname || "未知艺术家")
  } : {}, {
    D: $data.bgAudioStatus
  }, $data.bgAudioStatus ? {
    E: common_assets._imports_1$13
  } : {
    F: common_assets._imports_6
  }, {
    G: common_vendor.o((...args) => $options.toggleAudioPlay && $options.toggleAudioPlay(...args)),
    H: common_vendor.t($options.formatTime($data.currentTime)),
    I: $data.audioProgress,
    J: common_vendor.o((...args) => $options.onAudioProgressChange && $options.onAudioProgressChange(...args)),
    K: common_vendor.t($options.formatTime($data.duration)),
    L: $data.isCommentPopup ? "30vh" : "calc(100vh - " + $data.footerHeight + "px)"
  }) : {}, {
    M: $data.noteInfo.user_info && $data.noteInfo.user_info.avatar ? $data.noteInfo.user_info.avatar : "/static/img/avatar_default.png",
    N: common_vendor.t($data.noteInfo.user_info && $data.noteInfo.user_info.nickname ? $data.noteInfo.user_info.nickname : "用户"),
    O: common_vendor.t($data.noteInfo.create_time),
    P: common_vendor.t($data.noteInfo.location_name || "IP属地"),
    Q: common_vendor.t($data.noteInfo.views || 0),
    R: "user/details?id=" + $data.noteInfo.uid,
    S: common_vendor.o((...args) => $options.navigateToFun && $options.navigateToFun(...args)),
    T: $data.noteInfo.uid && $data.noteInfo.uid != $data.userId
  }, $data.noteInfo.uid && $data.noteInfo.uid != $data.userId ? {
    U: common_vendor.t($data.isFollowing ? "已关注" : "＋关注"),
    V: common_vendor.n($data.isFollowing ? "active" : ""),
    W: common_vendor.o((...args) => $options.followUser && $options.followUser(...args))
  } : {}, {
    X: common_vendor.t($data.noteInfo.content),
    Y: common_vendor.n($data.isExpanded ? "" : "ohto2"),
    Z: $data.isContentOverflow && !$data.isExpanded
  }, $data.isContentOverflow && !$data.isExpanded ? {
    aa: common_vendor.o((...args) => $options.toggleContent && $options.toggleContent(...args))
  } : {}, {
    ab: $data.isExpanded
  }, $data.isExpanded ? {
    ac: common_vendor.o((...args) => $options.toggleContent && $options.toggleContent(...args))
  } : {}, {
    ad: $data.noteInfo.topic_info && $data.noteInfo.topic_info.length > 0 || $data.noteInfo.goods_info || $data.noteInfo.location_name || $options.hasCircle()
  }, $data.noteInfo.topic_info && $data.noteInfo.topic_info.length > 0 || $data.noteInfo.goods_info || $data.noteInfo.location_name || $options.hasCircle() ? common_vendor.e({
    ae: $data.noteInfo.location_name
  }, $data.noteInfo.location_name ? {
    af: common_assets._imports_5$2,
    ag: common_vendor.t($data.noteInfo.location_name),
    ah: common_vendor.o((...args) => $options.openLocationClick && $options.openLocationClick(...args))
  } : {}, {
    ai: $options.hasCircle()
  }, $options.hasCircle() ? {
    aj: $options.getCircleAvatar(),
    ak: common_vendor.t($options.getCircleName()),
    al: "note/circle?id=" + $options.getCircleId(),
    am: common_vendor.o((...args) => $options.navigateToFun && $options.navigateToFun(...args))
  } : {}, {
    an: common_vendor.f($data.noteInfo.topic_info, (topic, topicIndex, i0) => {
      return {
        a: topic.icon || "/static/img/topic_icon.png",
        b: common_vendor.t(topic.title),
        c: "topic-" + topicIndex,
        d: "topic/details?id=" + topic.id,
        e: common_vendor.o((...args) => $options.navigateToFun && $options.navigateToFun(...args), "topic-" + topicIndex)
      };
    }),
    ao: $data.noteInfo.goods_info
  }, $data.noteInfo.goods_info ? {
    ap: $data.noteInfo.goods_info.image,
    aq: common_vendor.t($data.noteInfo.goods_info.store_name),
    ar: "goods/details?id=" + $data.noteInfo.product_id,
    as: common_vendor.o((...args) => $options.navigateToFun && $options.navigateToFun(...args))
  } : {}) : {}, {
    at: $data.footerHeight + "px",
    av: !$data.isUser
  }, !$data.isUser ? {
    aw: common_vendor.o((...args) => $options.navigateToFun && $options.navigateToFun(...args))
  } : {
    ax: common_vendor.t($data.comtext ? $data.comtext : $data.comtips),
    ay: common_vendor.o((...args) => $options.openComment && $options.openComment(...args))
  }, {
    az: common_assets._imports_7$3,
    aA: common_vendor.t($data.noteInfo.comments ? $data.noteInfo.comments : ""),
    aB: common_vendor.o(($event) => $options.commentPopupClick(true)),
    aC: $data.noteInfo.is_like == 1
  }, $data.noteInfo.is_like == 1 ? {
    aD: common_assets._imports_8$3
  } : {
    aE: common_assets._imports_9
  }, {
    aF: $data.noteInfo.likes < 1e4
  }, $data.noteInfo.likes < 1e4 ? {
    aG: common_vendor.t($data.noteInfo.likes ? $data.noteInfo.likes : "")
  } : {
    aH: common_vendor.t($options.formatCount($data.noteInfo.likes))
  }, {
    aI: common_vendor.o((...args) => $options.likeDynamic && $options.likeDynamic(...args)),
    aJ: common_assets._imports_10$1,
    aK: common_vendor.o((...args) => $options.openShare && $options.openShare(...args)),
    aL: $data.isComment
  }, $data.isComment ? {
    aM: common_vendor.o((...args) => $options.closeComment && $options.closeComment(...args))
  } : {}, {
    aN: common_vendor.sr("commentInput", "3b8edd40-2"),
    aO: common_vendor.o($options.closeComment),
    aP: common_vendor.o($options.handleCommentSubmit),
    aQ: common_vendor.p({
      show: $data.isComment,
      placeholder: $data.comtips,
      focus: $data.isFocus
    }),
    aR: common_vendor.o($options.closeShare),
    aS: common_vendor.o($options.handleEdit),
    aT: common_vendor.o($options.handleDelete),
    aU: common_vendor.o($options.handleReport),
    aV: common_vendor.o($options.handleDislike),
    aW: common_vendor.p({
      show: $data.isShareVisible,
      noteInfo: $data.noteInfo,
      userId: $data.userId
    }),
    aX: common_vendor.t($data.tipsTitle),
    aY: common_vendor.sr("tipsPopup", "3b8edd40-4"),
    aZ: common_vendor.p({
      type: "top",
      ["mask-background-color"]: "rgba(0, 0, 0, 0)"
    }),
    ba: $data.previewEmojiData
  }, $data.previewEmojiData ? {
    bb: $data.previewEmojiData.url,
    bc: common_vendor.o(($event) => $data.previewEmojiData = null)
  } : {}, {
    bd: common_vendor.t($data.noteInfo.comments > 0 ? "评论 " + $data.noteInfo.comments : "暂无评论"),
    be: $data.cType == 0 ? "6rpx" : "74rpx",
    bf: $data.cType == 0 ? "#000" : "#999",
    bg: common_vendor.o(($event) => $options.commentClick(0)),
    bh: $data.cType == 1 ? "#000" : "#999",
    bi: common_vendor.o(($event) => $options.commentClick(1)),
    bj: common_assets._imports_0$4,
    bk: common_vendor.o(($event) => $options.commentPopupClick(false)),
    bl: $data.isCommentContent
  }, $data.isCommentContent ? {
    bm: common_vendor.p({
      src: $data.noteInfo.user_info && $data.noteInfo.user_info.avatar ? $data.noteInfo.user_info.avatar : "/static/img/avatar_default.png"
    }),
    bn: "user/details?id=" + $data.noteInfo.uid,
    bo: common_vendor.o((...args) => $options.navigateToFun && $options.navigateToFun(...args)),
    bp: common_vendor.t($data.noteInfo.user_info && $data.noteInfo.user_info.nickname ? $data.noteInfo.user_info.nickname : "用户"),
    bq: "user/details?id=" + $data.noteInfo.uid,
    br: common_vendor.o((...args) => $options.navigateToFun && $options.navigateToFun(...args)),
    bs: common_vendor.t($data.noteInfo.content),
    bt: common_vendor.t($data.noteInfo.create_time),
    bv: common_vendor.t($data.noteInfo.location_name || "IP属地"),
    bw: common_vendor.t($data.noteInfo.views || 0)
  } : {}, {
    bx: $data.isEmpty
  }, $data.isEmpty ? {
    by: common_assets._imports_3$1
  } : {
    bz: common_vendor.f($data.commentList, (item, index, i0) => {
      return common_vendor.e({
        a: "3b8edd40-7-" + i0 + ",3b8edd40-5",
        b: common_vendor.p({
          src: item.user_info && item.user_info.avatar ? item.user_info.avatar : "/static/img/avatar_default.png"
        }),
        c: "user/details?id=" + item.uid,
        d: common_vendor.o((...args) => $options.navigateToFun && $options.navigateToFun(...args), index),
        e: $data.noteInfo.uid == item.uid
      }, $data.noteInfo.uid == item.uid ? {} : $data.userId == item.uid ? {} : {}, {
        f: $data.userId == item.uid,
        g: common_vendor.t(item.user_info && item.user_info.nickname ? item.user_info.nickname : "用户"),
        h: !item.delete_time
      }, !item.delete_time ? common_vendor.e({
        i: item.is_like == 1 ? "/static/img/dz1.png" : "/static/img/dz.png",
        j: item.like_count > 0 || item.likes > 0
      }, item.like_count > 0 || item.likes > 0 ? {
        k: common_vendor.t(item.like_count || item.likes)
      } : {}, {
        l: common_vendor.o(($event) => $options.toggleCommentLike(item.id, item.is_like), index)
      }) : {}, {
        m: "user/details?id=" + item.uid,
        n: common_vendor.o((...args) => $options.navigateToFun && $options.navigateToFun(...args), index),
        o: !item.delete_time
      }, !item.delete_time ? {
        p: $options.parseEmojiContentForRichText(item.content),
        q: common_vendor.o((...args) => $options.onEmojiClick && $options.onEmojiClick(...args), index)
      } : {}, {
        r: item.image && !item.delete_time
      }, item.image && !item.delete_time ? {
        s: item.image,
        t: common_vendor.o(($event) => $options.previewCommentImage(item.image), index)
      } : {}, {
        v: common_vendor.n((item.status != 5 || item.delete_time) && "db"),
        w: common_vendor.o((...args) => $options.openComment && $options.openComment(...args), index),
        x: item.uid,
        y: item.id,
        z: item.user_info && item.user_info.nickname ? item.user_info.nickname : "用户",
        A: index,
        B: common_vendor.t(item.create_time),
        C: common_vendor.t(item.province || ""),
        D: !item.delete_time
      }, !item.delete_time ? {
        E: common_vendor.o((...args) => $options.openComment && $options.openComment(...args), index),
        F: item.uid,
        G: item.id,
        H: item.user_info && item.user_info.nickname ? item.user_info.nickname : "用户",
        I: index
      } : {}, {
        J: $data.userId == item.uid && item.status == 5 && !item.delete_time
      }, $data.userId == item.uid && item.status == 5 && !item.delete_time ? {
        K: common_vendor.o((...args) => $options.delComment && $options.delComment(...args), index),
        L: item.id,
        M: index
      } : {}, {
        N: item.replies && item.replies.length > 0
      }, item.replies && item.replies.length > 0 ? {
        O: common_vendor.f($options.sortRepliesByTime(item.replies), (v, i, i1) => {
          return common_vendor.e({
            a: "3b8edd40-8-" + i0 + "-" + i1 + ",3b8edd40-5",
            b: common_vendor.p({
              src: v.user_info && v.user_info.avatar ? v.user_info.avatar : "/static/img/avatar_default.png"
            }),
            c: "user/details?id=" + v.uid,
            d: common_vendor.o((...args) => $options.navigateToFun && $options.navigateToFun(...args), i),
            e: $data.noteInfo.uid == v.uid
          }, $data.noteInfo.uid == v.uid ? {} : $data.userId == v.uid ? {} : {}, {
            f: $data.userId == v.uid,
            g: common_vendor.t(v.user_info && v.user_info.nickname ? v.user_info.nickname : "用户"),
            h: "user/details?id=" + v.uid,
            i: common_vendor.o((...args) => $options.navigateToFun && $options.navigateToFun(...args), i),
            j: v.reply_uid && v.reply_uid !== item.uid
          }, v.reply_uid && v.reply_uid !== item.uid ? {
            k: common_vendor.t(v.reply_nickname || "用户"),
            l: "user/details?id=" + v.reply_uid,
            m: common_vendor.o((...args) => $options.navigateToFun && $options.navigateToFun(...args), i)
          } : {}, {
            n: !v.delete_time
          }, !v.delete_time ? common_vendor.e({
            o: v.is_like == 1 ? "/static/img/dz1.png" : "/static/img/dz.png",
            p: v.like_count > 0 || v.likes > 0
          }, v.like_count > 0 || v.likes > 0 ? {
            q: common_vendor.t(v.like_count || v.likes)
          } : {}, {
            r: common_vendor.o(($event) => $options.toggleCommentLike(v.id, v.is_like), i)
          }) : {}, {
            s: !v.delete_time
          }, !v.delete_time ? {
            t: $options.parseEmojiContentForRichText(v.content),
            v: common_vendor.o((...args) => $options.onEmojiClick && $options.onEmojiClick(...args), i)
          } : {}, {
            w: v.image && !v.delete_time
          }, v.image && !v.delete_time ? {
            x: v.image,
            y: common_vendor.o(($event) => $options.previewCommentImage(v.image), i)
          } : {}, {
            z: common_vendor.n((v.status != 5 || v.delete_time) && "db"),
            A: common_vendor.o((...args) => $options.openComment && $options.openComment(...args), i),
            B: v.uid,
            C: v.user_info && v.user_info.nickname ? v.user_info.nickname : "用户",
            D: $options.getReplyIndex(item.replies, v.id),
            E: common_vendor.t(v.create_time),
            F: common_vendor.t(v.province || ""),
            G: !v.delete_time
          }, !v.delete_time ? {
            H: common_vendor.o((...args) => $options.openComment && $options.openComment(...args), i),
            I: v.uid,
            J: item.id,
            K: v.user_info && v.user_info.nickname ? v.user_info.nickname : "用户",
            L: index,
            M: $options.getReplyIndex(item.replies, v.id)
          } : {}, {
            N: $data.userId == v.uid && v.status == 5 && !v.delete_time
          }, $data.userId == v.uid && v.status == 5 && !v.delete_time ? {
            O: common_vendor.o((...args) => $options.delComment && $options.delComment(...args), i),
            P: v.id,
            Q: index,
            R: $options.getReplyIndex(item.replies, v.id)
          } : {}, {
            S: i
          });
        }),
        P: item.id,
        Q: index
      } : {}, {
        R: item.reply_count > (item.replies ? item.replies.length : 0)
      }, item.reply_count > (item.replies ? item.replies.length : 0) ? common_vendor.e({
        S: item.loading_replies
      }, item.loading_replies ? {
        T: common_assets._imports_10$2
      } : common_vendor.e({
        U: !item.replies || item.replies.length === 0
      }, !item.replies || item.replies.length === 0 ? {
        V: common_vendor.t(item.reply_count)
      } : item.has_more_replies ? {
        X: common_vendor.t(item.replies.length),
        Y: common_vendor.t(item.reply_count)
      } : {}, {
        W: item.has_more_replies
      }), {
        Z: item.id,
        aa: index,
        ab: common_vendor.o((...args) => $options.sonComment && $options.sonComment(...args), index)
      }) : {}, {
        ac: index
      });
    })
  }, {
    bA: $data.loadStatus != "no-more"
  }, $data.loadStatus != "no-more" ? {
    bB: common_vendor.p({
      status: $data.loadStatus
    })
  } : {}, {
    bC: common_vendor.o((...args) => $options.commentReachBottom && $options.commentReachBottom(...args)),
    bD: $data.userAvatar || "/static/img/avatar_default.png",
    bE: common_vendor.t($data.comtext ? $data.comtext : $data.comtips),
    bF: common_vendor.o((...args) => $options.openComment && $options.openComment(...args)),
    bG: common_vendor.sr("commentPopup", "3b8edd40-5"),
    bH: common_vendor.o(($event) => $options.commentPopupClick(false)),
    bI: common_vendor.p({
      type: "bottom",
      ["safe-area"]: false
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
_sfc_main.__runtimeHooks = 7;
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/note/video.js.map
