"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
let sysHeight = 20;
const _sfc_main = {
  name: "navbar",
  props: {
    bg: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      statusBarHeight: 20,
      titleBarHeight: 44,
      navH: 0,
      sysHeight: sysHeight + "px"
    };
  },
  created() {
    this.initNavBarHeight();
  },
  methods: {
    initNavBarHeight() {
      let that = this;
      const systemInfo = common_vendor.index.getSystemInfoSync();
      this.statusBarHeight = systemInfo.statusBarHeight || 0;
      that.navH = 0;
      if (that.navH > 0) {
        const rpxRatio = 750 / systemInfo.windowWidth;
        const totalPx = that.navH / rpxRatio;
        this.titleBarHeight = totalPx - this.statusBarHeight;
      } else {
        this.titleBarHeight = 44;
      }
      if (this.titleBarHeight < 30) {
        this.titleBarHeight = 44;
      }
    },
    navBack() {
      if (getCurrentPages().length > 1) {
        common_vendor.index.navigateBack();
      } else {
        common_vendor.index.switchTab({
          url: "//pages/index/index"
        });
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_assets._imports_0$7,
    b: $data.titleBarHeight + "px",
    c: common_vendor.o((...args) => $options.navBack && $options.navBack(...args)),
    d: $data.titleBarHeight + "px",
    e: common_vendor.n($props.bg == 0 ? "bfw" : ""),
    f: common_vendor.n($props.bg == 1 ? "bf8" : ""),
    g: $data.statusBarHeight + "px"
  };
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/navbar/navbar.js.map
