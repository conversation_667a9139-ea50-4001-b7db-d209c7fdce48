import { createI18n } from 'vue-i18n'
import Cache from '@/utils/cache.js';

// 默认语言包
const defaultMessages = {
  'zh-CN': {
    // 通用
    '确定': '确定',
    '取消': '取消',
    '保存': '保存',
    '删除': '删除',
    '编辑': '编辑',
    '返回': '返回',
    '刷新': '刷新',
    '加载中': '加载中...',
    '暂无数据': '暂无数据',
    '网络错误': '网络错误，请稍后重试',
    '切换成功': '切换成功',
    '切换失败，请重试': '切换失败，请重试',
    '选择语言': '选择语言',
    '扫一扫': '扫一扫',
    '帮助与客服': '帮助与客服',
    '设置': '设置',

    // 个人中心
    '个人中心': '个人中心',
    '头像': '头像',
    '昵称': '昵称',
    '手机号码': '手机号码',
    'ID号': 'ID号',
    '权限设置': '权限设置',
    '密码': '密码',
    '更换手机号码': '更换手机号码',
    '缓存大小': '缓存大小',
    '当前版本': '当前版本',
    '语言切换': '语言切换',
    '地址管理': '地址管理',
    '发票管理': '发票管理',
    '账号注销': '账号注销',
    '用户协议': '用户协议',
    '隐私协议': '隐私协议',
    '退出登录': '退出登录',
    '保存修改': '保存修改',

    // 操作提示
    '点击绑定手机号': '点击绑定手机号',
    '点击管理': '点击管理',
    '点击修改密码': '点击修改密码',
    '点击更换手机号码': '点击更换手机号码',
    '点击前往': '点击前往',
    '点击查看': '点击查看',
    '注销后无法恢复': '注销后无法恢复',
    '确认退出登录': '确认退出登录',
    '请输入姓名': '请输入姓名',
    '保存失败': '保存失败',
    '当前为最新版本': '当前为最新版本',
    '清除缓存': '清除缓存',
    '确定清楚本地缓存数据吗': '确定清除本地缓存数据吗？',
    '缓存清理完成': '缓存清理完成',
    '切换的账号不存在': '切换的账号不存在',
    '正在切换中': '正在切换中',
    '提示': '提示',

    // 动态相关
    '暂无推荐内容': '暂无推荐内容',
    '去发笔记，或许就上推荐了': '去发笔记，或许就上推荐了',
    '暂无笔记内容': '暂无笔记内容',
    '发笔记，记录灵感日常': '发笔记，记录灵感日常',
    '暂无喜欢的内容': '暂无喜欢的内容',
    '快在推荐中寻找更多笔记吧': '快在推荐中寻找更多笔记吧',

    // 社交功能
    '关注': '关注',
    '粉丝': '粉丝',
    '获赞': '获赞',
    '访客': '访客',
    '笔记': '笔记',
    '赞过': '赞过',
    '圈子': '圈子',
    '购物车': '购物车',
    '订单': '订单',
    '卡券': '卡券',
    '活动': '活动',

    // 状态提示
    '已关注': '已关注',
    '互相关注': '互相关注',
    '点赞成功': '点赞成功',
    '取消点赞': '取消点赞',
    '发布成功': '发布成功',
    '删除成功': '删除成功',
    '复制成功': '复制成功',
    '分享成功': '分享成功'
  },

  'en-US': {
    // Common
    '确定': 'Confirm',
    '取消': 'Cancel',
    '保存': 'Save',
    '删除': 'Delete',
    '编辑': 'Edit',
    '返回': 'Back',
    '刷新': 'Refresh',
    '加载中': 'Loading...',
    '暂无数据': 'No Data',
    '网络错误': 'Network Error, Please Try Again',
    '切换成功': 'Switch Success',
    '切换失败，请重试': 'Switch Failed, Please Try Again',
    '选择语言': 'Select Language',
    '扫一扫': 'Scan',
    '帮助与客服': 'Help & Support',
    '设置': 'Settings',

    // Profile
    '个人中心': 'Profile',
    '头像': 'Avatar',
    '昵称': 'Nickname',
    '手机号码': 'Phone Number',
    'ID号': 'ID Number',
    '权限设置': 'Permission Settings',
    '密码': 'Password',
    '更换手机号码': 'Change Phone Number',
    '缓存大小': 'Cache Size',
    '当前版本': 'Current Version',
    '语言切换': 'Language',
    '地址管理': 'Address Management',
    '发票管理': 'Invoice Management',
    '账号注销': 'Account Cancellation',
    '用户协议': 'User Agreement',
    '隐私协议': 'Privacy Policy',
    '退出登录': 'Logout',
    '保存修改': 'Save Changes',

    // Action Tips
    '点击绑定手机号': 'Tap to Bind Phone',
    '点击管理': 'Tap to Manage',
    '点击修改密码': 'Tap to Change Password',
    '点击更换手机号码': 'Tap to Change Phone',
    '点击前往': 'Tap to Go',
    '点击查看': 'Tap to View',
    '注销后无法恢复': 'Cannot be recovered after cancellation',
    '确认退出登录': 'Confirm to logout',
    '请输入姓名': 'Please enter name',
    '保存失败': 'Save failed',
    '当前为最新版本': 'Current is the latest version',
    '清除缓存': 'Clear Cache',
    '确定清楚本地缓存数据吗': 'Are you sure to clear local cache data?',
    '缓存清理完成': 'Cache cleared',
    '切换的账号不存在': 'Account to switch does not exist',
    '正在切换中': 'Switching...',
    '提示': 'Tips',

    // Dynamic Related
    '暂无推荐内容': 'No Recommended Content',
    '去发笔记，或许就上推荐了': 'Post notes, maybe get recommended',
    '暂无笔记内容': 'No Notes',
    '发笔记，记录灵感日常': 'Post notes, record daily inspiration',
    '暂无喜欢的内容': 'No Liked Content',
    '快在推荐中寻找更多笔记吧': 'Find more notes in recommendations',

    // Social Features
    '关注': 'Following',
    '粉丝': 'Followers',
    '获赞': 'Likes',
    '访客': 'Visitors',
    '笔记': 'Notes',
    '赞过': 'Liked',
    '圈子': 'Circles',
    '购物车': 'Cart',
    '订单': 'Orders',
    '卡券': 'Coupons',
    '活动': 'Activities',

    // Status Tips
    '已关注': 'Following',
    '互相关注': 'Mutual Follow',
    '点赞成功': 'Liked',
    '取消点赞': 'Unliked',
    '发布成功': 'Published',
    '删除成功': 'Deleted',
    '复制成功': 'Copied',
    '分享成功': 'Shared'
  }
}

// 获取当前语言
function getLocale() {
  let lang = '';

  // 优先从缓存获取
  if (Cache.has('locale')) {
    lang = Cache.get('locale');
  } else {
    // #ifdef MP || APP-PLUS
    lang = 'zh-CN';
    // #endif
    // #ifdef H5
    lang = navigator.language || 'zh-CN';
    // #endif
  }

  // 标准化语言代码
  if (lang.startsWith('zh')) {
    lang = 'zh-CN';
  } else if (lang.startsWith('en')) {
    lang = 'en-US';
  } else {
    lang = 'zh-CN'; // 默认中文
  }

  return lang;
}

// 合并语言包
function mergeMessages() {
  const cachedMessages = uni.getStorageSync('localeJson') || {};
  const messages = { ...defaultMessages };

  // 合并缓存的语言包
  Object.keys(cachedMessages).forEach(locale => {
    if (messages[locale]) {
      messages[locale] = { ...messages[locale], ...cachedMessages[locale] };
    } else {
      messages[locale] = cachedMessages[locale];
    }
  });

  return messages;
}

const i18n = createI18n({
	locale: getLocale(),
	fallbackLocale: 'zh-CN',
	messages: mergeMessages(),
	legacy: false, // Vue3 Composition API模式
	globalInjection: true, // 全局注入$t函数
	silentTranslationWarn: true, // 去除国际化警告
})

export default i18n
