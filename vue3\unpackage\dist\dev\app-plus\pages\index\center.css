
.uni-load-more[data-v-9245e42c] {
  display: flex;
  flex-direction: row;
  height: 1.875rem;
  align-items: center;
  justify-content: center;
}
.uni-load-more__text[data-v-9245e42c] {
  font-size: 0.75rem;
  margin-left: 0.5rem;
}
.uni-load-more__img[data-v-9245e42c] {
  width: 1.5rem;
  height: 1.5rem;
}
.uni-load-more__img--nvue[data-v-9245e42c] {
  color: #999;
}
.uni-load-more__img--android[data-v-9245e42c],
.uni-load-more__img--ios[data-v-9245e42c] {
  width: 1.5rem;
  height: 1.5rem;
  transform: rotate(0);
}
.uni-load-more__img--android[data-v-9245e42c] {
  animation: loading-ios 1s 0s linear infinite;
}
.uni-load-more__img--ios-H5[data-v-9245e42c] {
  position: relative;
  animation: loading-ios-H5-9245e42c 1s 0s step-end infinite;
}
.uni-load-more__img--ios-H5 uni-image[data-v-9245e42c] {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
}
@keyframes loading-ios-H5-9245e42c {
0% {
    transform: rotate(0);
}
8% {
    transform: rotate(30deg);
}
16% {
    transform: rotate(60deg);
}
24% {
    transform: rotate(90deg);
}
32% {
    transform: rotate(120deg);
}
40% {
    transform: rotate(150deg);
}
48% {
    transform: rotate(180deg);
}
56% {
    transform: rotate(210deg);
}
64% {
    transform: rotate(240deg);
}
73% {
    transform: rotate(270deg);
}
82% {
    transform: rotate(300deg);
}
91% {
    transform: rotate(330deg);
}
to {
    transform: rotate(360deg);
}
}
.uni-load-more__img--android-MP[data-v-9245e42c] {
  position: relative;
  width: 1.5rem;
  height: 1.5rem;
  transform: rotate(0);
  animation: loading-ios 1s 0s ease infinite;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon[data-v-9245e42c] {
  position: absolute;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: solid 0.125rem transparent;
  border-top: solid 0.125rem #999;
  transform-origin: center;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon[data-v-9245e42c]:nth-child(1) {
  animation: loading-android-MP-1-9245e42c 1s 0s linear infinite;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon[data-v-9245e42c]:nth-child(2) {
  animation: loading-android-MP-2-9245e42c 1s 0s linear infinite;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon[data-v-9245e42c]:nth-child(3) {
  animation: loading-android-MP-3-9245e42c 1s 0s linear infinite;
}
@keyframes loading-android-9245e42c {
0% {
    transform: rotate(0);
}
to {
    transform: rotate(360deg);
}
}
@keyframes loading-android-MP-1-9245e42c {
0% {
    transform: rotate(0);
}
50% {
    transform: rotate(90deg);
}
to {
    transform: rotate(360deg);
}
}
@keyframes loading-android-MP-2-9245e42c {
0% {
    transform: rotate(0);
}
50% {
    transform: rotate(180deg);
}
to {
    transform: rotate(360deg);
}
}
@keyframes loading-android-MP-3-9245e42c {
0% {
    transform: rotate(0);
}
50% {
    transform: rotate(270deg);
}
to {
    transform: rotate(360deg);
}
}


.uni-popup[data-v-4dd3c44b] {
  position: fixed;
  z-index: 99;
}
.uni-popup.top[data-v-4dd3c44b],
.uni-popup.left[data-v-4dd3c44b],
.uni-popup.right[data-v-4dd3c44b] {
  top: 0;
}
.uni-popup .uni-popup__wrapper[data-v-4dd3c44b] {
  display: block;
  position: relative;
  /* iphonex 等安全区设置，底部安全区适配 */
}
.uni-popup .uni-popup__wrapper.left[data-v-4dd3c44b],
.uni-popup .uni-popup__wrapper.right[data-v-4dd3c44b] {
  padding-top: 0;
  flex: 1;
}
.fixforpc-z-index[data-v-4dd3c44b] {

  z-index: 999;
}
.fixforpc-top[data-v-4dd3c44b] {
  top: 0;
}


.lazy-image[data-v-337fa078]{position:relative;width:100%;height:100%;overflow:hidden;background:#f8f8f8;}
.lazy-image uni-image[data-v-337fa078]{width:100%;height:100%;}
.lazy-image .err[data-v-337fa078]{position:absolute;top:0;left:0;width:100%;height:100%;background:rgba(248,248,248,0.7) url('../../static/img/load.png') no-repeat center/50%;}


.gg-box[data-v-8336ab48] {
  position: relative;
  border-bottom: 1px solid #f8f8f8;
  width: calc(100% - 1.875rem);
  padding: 0.9375rem;
  display: flex;
}
.gg-box .gg-avatar[data-v-8336ab48] {
  position: relative;
  z-index: 1;
  width: 2.125rem;
  height: 2.125rem;
  border-radius: 50%;
  border: 1px solid #f8f8f8;
  overflow: hidden;
}
.gg-box .rq[data-v-8336ab48] {
  line-height: 2.125rem;
  font-size: 0.8125rem;
  font-weight: bolder;
  text-align: center;
  color: #fff;
  background: #000;
  border: 1px solid #000;
}
.gg-box .gg-item[data-v-8336ab48] {
  width: calc(100% - 2.75rem - 2px);
  margin-left: 0.625rem;
}
.timeline-line[data-v-8336ab48] {
  position: absolute;
  left: 1.8125rem;
  top: 3.75rem; 
  bottom: 0.9375rem;
  width: 0.0625rem;
  background-color: #e5e5e5; 
  z-index: 0;
}
.timeline-line[data-v-8336ab48]::before {
  content: '';
  position: absolute;
  left: -0.125rem;
  top: -0.125rem;
  width: 0.3125rem;
  height: 0.3125rem;
  border-radius: 50%;
  background-color: #e5e5e5;
}
.gg-box:last-child .timeline-line[data-v-8336ab48] {
  display: none;
}
.gg-item .gg-item-name[data-v-8336ab48] {
  color: #999;
  font-size: 0.6875rem;
  line-height: 0.6875rem;
  font-weight: 300;
  display: flex;
  align-items: flex-end;
}
.gg-item .gg-item-name uni-text[data-v-8336ab48] {
  margin-right: 0.625rem;
  color: #fa5150;
  font-weight: 700;
}
.gg-item .gg-item-name uni-view[data-v-8336ab48] {
  max-width: 10rem;
  margin-right: 0.625rem;
  color: #000;
  font-size: 0.875rem;
  line-height: 0.875rem;
  font-weight: 700;
}
.gg-item .gg-item-content[data-v-8336ab48] {
  margin-top: 0.46875rem;
  font-size: 0.8125rem;
  font-weight: 400;
  line-height: 1.125rem;
  word-break: break-word;
  white-space: pre-line;
}
.gg-item .gg-item-file[data-v-8336ab48] {
  margin-top: 0.625rem;
  display: flex;
  position: relative;
  z-index: 1;
  flex-wrap: wrap;
}
.gg-item-file .file-h[data-v-8336ab48],
.gg-item-file .file-w[data-v-8336ab48],
.gg-item-file .file-img[data-v-8336ab48] {
  border-radius: 0.25rem;
  overflow: hidden;
}
.gg-item-file .file-h[data-v-8336ab48] {
  width: 10rem;
  height: 13.125rem;
}
.gg-item-file .file-w[data-v-8336ab48] {
  width: 13.125rem;
  height: 10rem;
}
.gg-item-file .file-img[data-v-8336ab48] {
  width: 6.125rem;
  height: 6.125rem;
  margin-right: 0.125rem;
  margin-bottom: 0.125rem;
}
.gg-item-file .file-img[data-v-8336ab48]:nth-child(3n) {
  margin-right: 0 !important;
}
.gg-item-file .file-count[data-v-8336ab48] {
  position: absolute;
  right: 0.625rem;
  bottom: 0.9375rem;
  padding: 0 0.3125rem;
  height: 1.25rem;
  color: #fff;
  font-size: 0.625rem;
  font-weight: 700;
  background: rgba(0, 0, 0, 0.6);
  -webkit-backdrop-filter: blur(15px);
  backdrop-filter: blur(15px);
  border-radius: 0.25rem;
}
.gg-item-file .file-count uni-image[data-v-8336ab48],
.gg-item-file .file-video uni-image[data-v-8336ab48] {
  width: 0.625rem;
  height: 0.625rem;
}
.gg-item-file .file-count uni-image[data-v-8336ab48] {
  margin-right: 0.3125rem;
}
.gg-item-file .file-video[data-v-8336ab48] {
  position: absolute;
  top: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  background: rgba(0, 0, 0, 0.6);
  justify-content: center;
  border-radius: 50%;
}
.gg-item-file .file-audio[data-v-8336ab48] {
  width: 100%;
  height: 4.375rem;
  border-radius: 0.25rem;
  color: #fff;
  position: relative;
  overflow: hidden;
}
.file-audio .audio-left[data-v-8336ab48] {
  margin-right: 0.9375rem;
  width: 4.375rem;
  height: 4.375rem;
  position: relative;
}
.file-audio .audio-left .icon[data-v-8336ab48] {
  position: absolute;
  top: 1.40625rem;
  right: 1.40625rem;
  bottom: 1.40625rem;
  left: 1.40625rem;
  width: 1.5625rem;
  height: 1.5625rem;
}
.file-audio .audio-bg[data-v-8336ab48],
.file-audio .audio-mb[data-v-8336ab48] {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.file-audio .audio-mb[data-v-8336ab48] {
  z-index: -1;
  -webkit-backdrop-filter: saturate(150%) blur(25px);
  backdrop-filter: saturate(150%) blur(25px);
  background: rgba(0, 0, 0, 0.5);
}
.file-audio .audio-t1[data-v-8336ab48] {
  font-size: 0.8125rem;
  font-weight: 700;
}
.file-audio .audio-t2[data-v-8336ab48] {
  margin-top: 0.3125rem;
  opacity: 0.8;
  font-size: 0.625rem;
}
.file-audio .audio-play[data-v-8336ab48] {
  margin: 0 0.9375rem;
  width: 3.125rem;
  height: 1.875rem;
  line-height: 1.875rem;
  text-align: center;
  font-size: 0.5625rem;
  font-weight: 700;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 1.875rem;
}
.gg-item .gg-item-g[data-v-8336ab48] {
  margin-top: 0.3125rem;
  width: 100%;
  display: flex;
  flex-wrap: wrap;
}
.gg-item-g .g-item[data-v-8336ab48] {
  margin: 0.3125rem 0.3125rem 0 0;
  padding: 0.25rem;
  font-size: 0.625rem;
  font-weight: 500;
  border-radius: 0.25rem;
  background: #f8f8f8;
}
.g-item .g-item-icon[data-v-8336ab48] {
  width: 1rem;
  height: 1rem;
}
.g-item .g-item-img[data-v-8336ab48] {
  width: 1.25rem;
  height: 1.25rem;
  background: #f8f8f8;
  border-radius: 0.125rem;
  overflow: hidden;
}
.gg-item-unm .gg-item-unm-txt[data-v-8336ab48] {
  margin: 0.625rem 0.625rem 0 0;
  color: #999;
  font-size: 0.625rem;
}
.wlc8[data-v-8336ab48] {
  -webkit-line-clamp: 8 !important;
}
.df[data-v-8336ab48] {
  display: flex;
  align-items: center;
}
.ohto[data-v-8336ab48] {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.ohto2[data-v-8336ab48] {
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
}
/* ==== 投票展示样式（复用add.vue） ==== */
.vote-box[data-v-8336ab48] {
  width: 100%;
  margin-top: 0.5rem;
}
.vote-container[data-v-8336ab48] {
  width: 100%;
  background-color: #f5f5f5;
  border-radius: 0.5rem;
  position: relative;
}
.vote-header[data-v-8336ab48] {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  padding: 0.625rem;
}
.vote-title-container[data-v-8336ab48] {
  display: flex;
  align-items: center;
}
.vote-icon[data-v-8336ab48] {
  width: 1rem;
  height: 1rem;
  margin-right: 0.3125rem;
}
.vote-title[data-v-8336ab48] {
  font-size: 0.875rem;
  font-weight: 500;
  color: #333;
  text-align: left;
  white-space: normal;
  word-break: break-word;
}
.vote-options[data-v-8336ab48] {
  display: flex;
  flex-direction: column;
  padding: 0 0;
}
.vote-option-voted[data-v-8336ab48] {
  background: #f5f5f5;
  border-radius: 1rem;
  margin-bottom: 0.5rem;
  padding: 0;
  border: none;
  position: relative;
  transition: background 0.2s, border 0.2s;
}
.vote-option-voted.selected[data-v-8336ab48] {
  background: #fff;
}
.vote-bar-bg[data-v-8336ab48] {
    border-radius: 18px;
    font-size: 17px;
    color: #333;
    margin-bottom: 10px;
    border: none;
    box-shadow: none;
    text-align: center;
    transition: background 0.2s, border 0.2s;
    margin-left: 11px;
    margin-right: 11px;
    min-height: 27px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 11px;
    box-sizing: border-box;
}
.vote-option-voted.selected .vote-bar-bg[data-v-8336ab48] {
  background: #fff;
}
.vote-bar[data-v-8336ab48] {
  height: 100%;
  border-radius: 1rem;
  transition: width 0.3s;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 1;
}
.vote-row[data-v-8336ab48] {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
  pointer-events: none;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 0.625rem;
}
.vote-left[data-v-8336ab48] {
  display: flex;
  align-items: center;
}
.vote-option-unvoted[data-v-8336ab48] {
  background: #fff;
  border-radius: 1rem;
  font-size: 0.8125rem;
  color: #333;
  margin-bottom: 0.5625rem;
  border: none;
  box-shadow: none;
  text-align: center;
  transition: background 0.2s, border 0.2s;
  margin-left: 0.625rem;
  margin-right: 0.625rem;
  position: relative;
  min-height: 2.0625rem;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 0.625rem;
  box-sizing: border-box;
}
.vote-progress-bar[data-v-8336ab48] {
  position: absolute;
  left: 0;
  bottom: 0;
  height: 0.375rem;
  border-radius: 1rem;
  z-index: 1;
  background: #ffd600;
  transition: width 0.3s;
}
.vote-option-unvoted:not(.selected) .vote-progress-bar[data-v-8336ab48] {
  background: #eaeaea;
}
.vote-checked-icon[data-v-8336ab48] {
  width: 0.875rem;
  height: 0.875rem;
  background: #ffd600;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.3125rem;
}
.vote-checked-icon uni-image[data-v-8336ab48] {
  width: 0.625rem;
  height: 0.625rem;
  display: block;
}
.vote-content[data-v-8336ab48] {
  font-size: 0.8125rem;
  color: #333;
  text-align: left;
  white-space: normal;
  word-break: break-word;
  font-weight: 500;
}
.vote-option-voted.selected .vote-content[data-v-8336ab48] {
  color: #000;
}
.vote-percent[data-v-8336ab48] {
  font-size: 0.8125rem;
  color: #000000;
  text-align: right;
  margin-left: 0.375rem;
  min-width: 1.5rem;
  flex-shrink: 0;
  white-space: nowrap;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  font-weight: 500;
  margin-right: 1.25rem;
  position: relative;
}
.vote-people[data-v-8336ab48] {
  margin-top: 0.5rem;
  color: #999;
  font-size: 0.75rem;
  text-align: left;
  padding-left: 0.625rem;
  padding-bottom: 0.625rem;
}


.gg-box[data-v-feabea4d] {
  border-bottom: 1px solid #f8f8f8;
  width: calc(100% - 1.875rem);
  padding: 0.9375rem;
  display: flex;
}
.gg-box .gg-avatar[data-v-feabea4d] {
  width: 2.125rem;
  height: 2.125rem;
  border-radius: 50%;
  border: 1px solid #f8f8f8;
  position: relative;
}
.gg-avatar .top[data-v-feabea4d] {
  position: absolute;
  right: -0.125rem;
  bottom: -0.125rem;
  width: 0.8125rem;
  height: 0.8125rem;
  border-radius: 50%;
  justify-content: center;
  background: #000;
}
.gg-avatar .top uni-image[data-v-feabea4d] {
  width: 100%;
  height: 100%;
}
.gg-box .gg-item[data-v-feabea4d] {
  width: calc(100% - 2.75rem - 2px);
  margin-left: 0.625rem;
}

/* 用户名容器 */
.gg-item .gg-item-user .name-container[data-v-feabea4d] {
  align-items: center;
}
.gg-item .gg-item-user .name[data-v-feabea4d] {
  color: #000;
  font-size: 0.875rem;
  line-height: 1.0625rem;
  font-weight: 700;
}

/* VIP图标样式 - 参考center.vue */
.gg-item .gg-item-user .status-icon[data-v-feabea4d] {
  width: 2.1875rem;
  height: 0.9375rem;
  margin-right: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
.gg-item .gg-item-user .vip-icon[data-v-feabea4d] {
  border-radius: 0.1875rem;
  padding: 0.0625rem;
}
.gg-item .gg-item-user .vip-icon uni-image[data-v-feabea4d] {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
.gg-item .gg-item-user .tag[data-v-feabea4d] {
  margin-left: 0.375rem;
  padding: 0 0.1875rem;
  height: 1.0625rem;
  border-radius: 0.125rem;
  background: #f5f5f5;
}
.gg-item-user .tag uni-image[data-v-feabea4d] {
  margin: 0 0.09375rem;
  width: 0.625rem;
  height: 0.625rem;
}
.gg-item-user .tag uni-text[data-v-feabea4d] {
  margin: 0 0.09375rem;
  font-size: 0.5625rem;
}
.gg-item .gg-item-content[data-v-feabea4d] {
  margin-top: 0.375rem;
  font-size: 0.8125rem;
  font-weight: 400;
  line-height: 1.125rem;
  word-break: break-word;
  white-space: pre-line;
}
.gg-item .gg-item-file[data-v-feabea4d] {
  margin-top: 0.625rem;
  display: flex;
  position: relative;
  z-index: 1;
  flex-wrap: wrap;
}
.gg-item-file .file-h[data-v-feabea4d],
.gg-item-file .file-w[data-v-feabea4d],
.gg-item-file .file-img[data-v-feabea4d] {
  border-radius: 0.25rem;
  overflow: hidden;
}
.gg-item-file .file-h[data-v-feabea4d] {
  width: 10rem;
  height: 13.125rem;
}
.gg-item-file .file-w[data-v-feabea4d] {
  width: 13.125rem;
  height: 10rem;
}
.gg-item-file .file-img[data-v-feabea4d] {
  width: 6.125rem;
  height: 6.125rem;
  margin-right: 0.125rem;
  margin-bottom: 0.125rem;
}
.gg-item-file .file-img[data-v-feabea4d]:nth-child(3n) {
  margin-right: 0 !important;
}
.gg-item-file .file-count[data-v-feabea4d] {
  position: absolute;
  right: 0.625rem;
  bottom: 0.9375rem;
  padding: 0 0.3125rem;
  height: 1.25rem;
  color: #fff;
  font-size: 0.625rem;
  font-weight: 700;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 0.25rem;
}
.gg-item-file .file-count uni-image[data-v-feabea4d],
.gg-item-file .file-video uni-image[data-v-feabea4d] {
  width: 0.625rem;
  height: 0.625rem;
}
.gg-item-file .file-count uni-image[data-v-feabea4d] {
  margin-right: 0.3125rem;
}
.gg-item-file .file-video[data-v-feabea4d] {
  position: absolute;
  top: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  background: rgba(0, 0, 0, 0.6);
  justify-content: center;
  border-radius: 50%;
}
.gg-item-file .file-audio[data-v-feabea4d] {
  width: 100%;
  height: 4.375rem;
  border-radius: 0.25rem;
  color: #fff;
  position: relative;
  overflow: hidden;
}
.file-audio .audio-left[data-v-feabea4d] {
  margin-right: 0.9375rem;
  width: 4.375rem;
  height: 4.375rem;
  position: relative;
}
.file-audio .audio-left .icon[data-v-feabea4d] {
  position: absolute;
  top: 1.40625rem;
  right: 1.40625rem;
  bottom: 1.40625rem;
  left: 1.40625rem;
  width: 1.5625rem;
  height: 1.5625rem;
}
.file-audio .audio-bg[data-v-feabea4d],
.file-audio .audio-mb[data-v-feabea4d] {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.file-audio .audio-mb[data-v-feabea4d] {
  z-index: -1;
  -webkit-backdrop-filter: saturate(150%) blur(25px);
  backdrop-filter: saturate(150%) blur(25px);
  background: rgba(0, 0, 0, 0.5);
}
.file-audio .audio-t1[data-v-feabea4d] {
  font-size: 0.8125rem;
  font-weight: 700;
}
.file-audio .audio-t2[data-v-feabea4d] {
  margin-top: 0.3125rem;
  opacity: 0.8;
  font-size: 0.625rem;
}
.file-audio .audio-play[data-v-feabea4d] {
  margin: 0 0.9375rem;
  width: 3.125rem;
  height: 1.875rem;
  line-height: 1.875rem;
  text-align: center;
  font-size: 0.5625rem;
  font-weight: 700;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 1.875rem;
}
.gg-item .gg-item-g[data-v-feabea4d] {
  margin-top: 0.3125rem;
  width: 100%;
  display: flex;
  flex-wrap: wrap;
}
.gg-item-g .g-item[data-v-feabea4d] {
  margin: 0.3125rem 0.3125rem 0 0;
  padding: 0.25rem;
  font-size: 0.625rem;
  font-weight: 500;
  border-radius: 0.25rem;
  background: #f8f8f8;
}
.g-item .g-item-img[data-v-feabea4d] {
  width: 1.25rem;
  height: 1.25rem;
  background: #f8f8f8;
  border-radius: 0.125rem;
  overflow: hidden;
}
.gg-item .gg-item-time[data-v-feabea4d] {
  margin-top: 0.25rem;
  margin-bottom: 0.375rem;
  color: #999;
  font-size: 0.625rem;
}
.gg-item .gg-item-comment[data-v-feabea4d] {
  margin-top: 0.625rem;
  width: calc(100% - 1.25rem);
  padding: 0.625rem;
  color: #999;
  font-size: 0.75rem;
  font-weight: 400;
  word-break: break-word;
  white-space: pre-line;
  border-radius: 0.25rem;
  background: #f8f8f8;
}
.gg-item .gg-item-unm[data-v-feabea4d] {
  display: flex;
  align-items: center;
  width: 100%;
}
.gg-item-unm .unm-item[data-v-feabea4d] {
  margin-top: 0.9375rem;
  display: flex;
  align-items: center;
}
.gg-item-unm .unm-item uni-image[data-v-feabea4d] {
  width: 1.375rem;
  height: 1.375rem;
}
.gg-item-unm .unm-item uni-text[data-v-feabea4d] {
  margin: 0 0.9375rem 0 0.1875rem;
  color: #999;
  font-size: 0.5625rem;
  font-weight: 700;
}
.wlc8[data-v-feabea4d] {
  -webkit-line-clamp: 8 !important;
  line-clamp: 8 !important;
}
.df[data-v-feabea4d] {
  display: flex;
  align-items: center;
}
.ohto[data-v-feabea4d] {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.ohto2[data-v-feabea4d] {
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  line-clamp: 4;
  -webkit-box-orient: vertical;
}

/* 关注按钮样式 */
.gg-item-user .follow-btn[data-v-feabea4d] {
  margin-left: auto;
  padding: 0 0.625rem;
  height: 1.5rem;
  line-height: 1.5rem;
  border-radius: 0.25rem;
  font-size: 0.625rem;
  font-weight: 700;
  color: #000;
  background: #f8f8f8;
  text-align: center;
}
.gg-item-user .follow-btn.active[data-v-feabea4d] {
  color: #999;
  background: #f5f5f5;
}
.gg-item-user .follow-btn.mutual[data-v-feabea4d] {
  color: #576b95;
  background: rgba(87, 107, 149, 0.1);
}

/* 移除原有 .topic-tag 样式，话题和圈子统一用 g-item/g-item-img/text 结构 */
.topic-tag[data-v-feabea4d],
.topic-tag uni-text[data-v-feabea4d] {
  /* 移除样式 */
  display: none !important;
}

/* 评论样式 */
.gg-item .gg-item-comments[data-v-feabea4d] {
  margin-top: 0.625rem;
  width: calc(100% - 1.25rem);
  padding: 0.625rem;
  font-size: 0.75rem;
  border-radius: 0.25rem;
  background: #f8f8f8;
}
.gg-item-comments .comment-item[data-v-feabea4d] {
  margin-bottom: 0.46875rem;
  padding-bottom: 0.46875rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}
.gg-item-comments .comment-item[data-v-feabea4d]:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}
.gg-item-comments .comment-user[data-v-feabea4d] {
  font-size: 0.75rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.1875rem;
}
.gg-item-comments .comment-content[data-v-feabea4d] {
  font-size: 0.75rem;
  color: #666;
  line-height: 1rem;
  word-break: break-all;
}
.gg-item-comments .comment-footer[data-v-feabea4d] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 0.3125rem;
}
.gg-item-comments .comment-time[data-v-feabea4d] {
  font-size: 0.625rem;
  color: #999;
}
.gg-item-comments .comment-like[data-v-feabea4d] {
  display: flex;
  align-items: center;
}
.gg-item-comments .comment-like uni-image[data-v-feabea4d] {
  width: 0.875rem;
  height: 0.875rem;
  margin-right: 0.125rem;
}
.gg-item-comments .comment-like uni-text[data-v-feabea4d] {
  font-size: 0.625rem;
  color: #999;
}
.gg-item-comments .comment-like uni-text.active[data-v-feabea4d] {
  color: #FA5150;
}
.gg-item-comments .more-comments[data-v-feabea4d] {
  margin-top: 0.46875rem;
  text-align: center;
  font-size: 0.6875rem;
  color: #576b95;
  padding: 0.3125rem 0;
}

/* ==== 投票展示样式（复用add.vue） ==== */
.vote-box[data-v-feabea4d] {
  width: 100%;
  margin-top: 0.5rem;
}
.vote-container[data-v-feabea4d] {
  width: 100%;
  background-color: #f5f5f5;
  border-radius: 0.5rem;
  position: relative;
}
.vote-header[data-v-feabea4d] {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  padding: 0.625rem;
}
.vote-title-container[data-v-feabea4d] {
  display: flex;
  align-items: center;
}
.vote-icon[data-v-feabea4d] {
  width: 1rem;
  height: 1rem;
  margin-right: 0.3125rem;
}
.vote-title[data-v-feabea4d] {
  font-size: 0.875rem;
  font-weight: 500;
  color: #333;
  text-align: left;
  white-space: normal;
  word-break: break-word;
}
.vote-options[data-v-feabea4d] {
  display: flex;
  flex-direction: column;
  padding: 0 0;
}
.vote-option-voted[data-v-feabea4d] {
  background: #f5f5f5;
  border-radius: 1rem;
  margin-bottom: 0.5rem;
  padding: 0;
  border: none;
  position: relative;
  transition: background 0.2s, border 0.2s;
}
.vote-option-voted.selected[data-v-feabea4d] {
  background: #fff;
}
.vote-bar-bg[data-v-feabea4d] {
    border-radius: 18px;
    font-size: 17px;
    color: #333;
    margin-bottom: 10px;
    border: none;
    box-shadow: none;
    text-align: center;
    transition: background 0.2s, border 0.2s;
    margin-left: 11px;
    margin-right: 11px;
    min-height: 27px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 11px;
    box-sizing: border-box;
}
.vote-option-voted.selected .vote-bar-bg[data-v-feabea4d] {
  background: #fff;
}
.vote-bar[data-v-feabea4d] {
  height: 100%;
  border-radius: 1rem;
  transition: width 0.3s;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 1;
}
.vote-row[data-v-feabea4d] {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
  pointer-events: none;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 0.625rem;
}
.vote-left[data-v-feabea4d] {
  display: flex;
  align-items: center;
}
.vote-option-unvoted[data-v-feabea4d] {
  background: #fff;
  border-radius: 1rem;
  font-size: 0.8125rem;
  color: #333;
  margin-bottom: 0.5625rem;
  border: none;
  box-shadow: none;
  text-align: center;
  transition: background 0.2s, border 0.2s;
  margin-left: 0.625rem;
  margin-right: 0.625rem;
  position: relative;
  min-height: 2.0625rem;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 0.625rem;
  box-sizing: border-box;
}
.vote-progress-bar[data-v-feabea4d] {
  position: absolute;
  left: 0;
  bottom: 0;
  height: 0.375rem;
  border-radius: 1rem;
  z-index: 1;
  background: #ffd600;
  transition: width 0.3s;
}
.vote-option-unvoted:not(.selected) .vote-progress-bar[data-v-feabea4d] {
  background: #eaeaea;
}
.vote-checked-icon[data-v-feabea4d] {
  width: 0.875rem;
  height: 0.875rem;
  background: #ffd600;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.3125rem;
}
.vote-checked-icon uni-image[data-v-feabea4d] {
  width: 0.625rem;
  height: 0.625rem;
  display: block;
}
.vote-content[data-v-feabea4d] {
  font-size: 0.8125rem;
  color: #333;
  text-align: left;
  white-space: normal;
  word-break: break-word;
  font-weight: 500;
}
.vote-option-voted.selected .vote-content[data-v-feabea4d] {
  color: #000;
}
.vote-percent[data-v-feabea4d] {
  font-size: 0.8125rem;
  color: #000000;
  text-align: right;
  margin-left: 0.375rem;
  min-width: 1.5rem;
  flex-shrink: 0;
  white-space: nowrap;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  font-weight: 500;
  margin-right: 1.25rem;
  position: relative;
}
.vote-people[data-v-feabea4d] {
  margin-top: 0.5rem;
  color: #999;
  font-size: 0.75rem;
  text-align: left;
  padding-left: 0.625rem;
  padding-bottom: 0.625rem;
}



/* APP端兼容性优化 - 移除CSS变量，使用具体值 */
.tabbar[data-v-e9b92a61]{
  position: fixed;
  z-index: 998;
  width: 100%;
  /* APP端兼容性：条件编译底部安全区域 */

  bottom: 1.5625rem;




  box-sizing: border-box;
  justify-content: center;
  pointer-events: none; /* 优化性能，只有子元素可点击 */
}
.tabbar-box[data-v-e9b92a61]{
  z-index: 998;
  width: calc(100% - 3.75rem);
  height: 3.125rem;
  border-radius: 1.5625rem;
  justify-content: space-around;
  /* APP端兼容性：条件编译backdrop-filter */




  pointer-events: auto; /* 恢复点击事件 */
  transition: all 0.3s ease; /* 添加过渡动画 */
}
.tb-bs[data-v-e9b92a61]{
  box-shadow: 0 0 0.75rem rgba(0, 0, 0, 0.06);
  border: 1px solid #f8f8f8;
}
.tabbar-box .tabbar-item[data-v-e9b92a61]{
  width: 20%;
  height: 3.125rem;
  justify-content: center;
  position: relative;
  transition: transform 0.2s ease; /* 添加点击反馈动画 */
  cursor: pointer;
}

/* 小程序兼容性：使用class替代:active伪类 */
.tabbar-item.active-state[data-v-e9b92a61] {
  transform: scale(0.95); /* 点击时缩放效果 */
}
.tabbar-item .icon[data-v-e9b92a61]{
  width: 1.5rem;
  height: 1.5rem;
  transition: opacity 0.2s ease; /* 图标切换动画 */
}
.tabbar-item .msg[data-v-e9b92a61]{
  position: absolute;
  top: 0.5625rem;
  left: calc(50% + 0.25rem);
  min-width: 1.0625rem;
  height: 1.0625rem;
  line-height: 1.0625rem;
  text-align: center;
  font-size: 0.5625rem;
  font-weight: 700;
  color: #fff;
  background: #fa5150;
  border-radius: 1.0625rem;
  border: 0.0625rem solid #fff;
}
.tabbar-item .add[data-v-e9b92a61]{
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 50%;
  justify-content: center;
  transition: all 0.3s ease-in-out;
}
.tabbar-item .add uni-image[data-v-e9b92a61]{
  width: 0.5rem;
  height: 0.5rem;
}
.tabbar .tabbar-add[data-v-e9b92a61]{
  position: fixed;
  z-index: 997;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  background-image: linear-gradient(to bottom, rgb(173 173 173 / 95%), rgb(25 25 25 / 95%));
  /* APP端兼容性：条件编译backdrop-filter */
}
.content-wrapper[data-v-e9b92a61] {
  width: 100%;
  padding-top: 5.625rem;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 头部标题区域 */
.add-header[data-v-e9b92a61] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 80%;
  padding-top: 2.8125rem;
}
.header-content[data-v-e9b92a61] {
  flex: 1;
}
.header-image[data-v-e9b92a61] {
  width: 9.375rem;
  height: 9.375rem;
}
.header-image uni-image[data-v-e9b92a61] {
  width: 100%;
  height: 100%;
  border-radius: 0.375rem;
}
.add-title[data-v-e9b92a61] {
  font-size: 1.25rem;
  font-weight: bold;
  color: #fff;
  display: flex;
  align-items: center;
}
.add-plus[data-v-e9b92a61] {
  margin-left: 0.25rem;
  font-weight: normal;
}
.add-subtitle[data-v-e9b92a61] {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 0.25rem;
}

/* 新增卡片样式 */
.card-container[data-v-e9b92a61] {
  width: 80%;
  display: flex;
  flex-direction: column;
  gap: 0.9375rem;
  margin-top: 7.5rem;
  pointer-events: auto; /* 确保容器可以传递点击事件 */
}

/* 卡片通用样式 */
.card[data-v-e9b92a61] {
  display: flex;
  align-items: center;
  border-radius: 0.625rem;
  padding: 0.9375rem;
  background-color: rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  pointer-events: auto; /* 确保卡片可以被点击 */
}

/* 小程序兼容性：使用class替代:active伪类 */
.card.active-state[data-v-e9b92a61] {
  transform: scale(0.98);
  background-color: rgba(255, 255, 255, 0.15);
}
.cream-card[data-v-e9b92a61] {
  background-color: rgba(255, 255, 255, 0.15);
}
.mint-card[data-v-e9b92a61] {
  background-color: rgba(255, 255, 255, 0.15);
}
.card-left[data-v-e9b92a61] {
  width: 1.875rem;
  height: 1.875rem;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.625rem;
}
.card-left uni-image[data-v-e9b92a61] {
  width: 1.25rem;
  height: 1.25rem;
}
.card-content[data-v-e9b92a61] {
  flex: 1;
}
.card-title[data-v-e9b92a61] {
  font-size: 0.875rem;
  font-weight: bold;
  color: #fff;
}
.card-subtitle[data-v-e9b92a61] {
  font-size: 0.6875rem;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 0.1875rem;
}
.card-right[data-v-e9b92a61] {
  width: 1.125rem;
  height: 1.125rem;
  transform: rotate(180deg);
}
.card-right uni-image[data-v-e9b92a61] {
  width: 100%;
  height: 100%;
}

/* 两列卡片布局 */
.two-column-container[data-v-e9b92a61] {
  display: flex;
  width: 100%;
  gap: 0.625rem;
}
.two-column-card[data-v-e9b92a61] {
  flex: 1;
  border-radius: 0.5rem;
  padding: 0.9375rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.3s ease;
  cursor: pointer;
  pointer-events: auto; /* 确保两列卡片可以被点击 */
}

/* 小程序兼容性：使用class替代:active伪类 */
.two-column-card.active-state[data-v-e9b92a61] {
  transform: scale(0.98);
}
.video-card[data-v-e9b92a61] {
  background-color: rgba(76, 130, 219, 0.3);
}
.audio-card[data-v-e9b92a61] {
  background-color: rgba(245, 166, 35, 0.3);
}
.card-content-left[data-v-e9b92a61] {
  flex: 1;
}
.two-column-card-title[data-v-e9b92a61] {
  font-size: 0.8125rem;
  font-weight: bold;
  color: #FFFFFF;
}
.two-column-card-desc[data-v-e9b92a61] {
  font-size: 0.625rem;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 0.1875rem;
}
.card-content-right[data-v-e9b92a61] {
  width: 1.5625rem;
  height: 1.5625rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
.card-content-right uni-image[data-v-e9b92a61] {
  width: 1.25rem;
  height: 1.25rem;
}
.close-btn uni-image[data-v-e9b92a61] {
  width: 0.75rem;
  height: 0.75rem;
}
.df[data-v-e9b92a61]{
  display: flex;
  align-items: center;
}
.bfh[data-v-e9b92a61]{
  background: rgba(0, 0, 0, 0.8);
}
.bfw[data-v-e9b92a61]{
  background: #fff;
}

/* 动画优化 - APP端兼容性 */
.fade-in[data-v-e9b92a61]{
  animation: fadeIn-e9b92a61 0.3s forwards;
}
.fade-out[data-v-e9b92a61]{
  animation: fadeOut-e9b92a61 0.3s forwards;
}
@keyframes fadeIn-e9b92a61{
from{
    opacity: 0;
    transform: translateY(0.3125rem);
}
to{
    opacity: 1;
    transform: translateY(0);
}
}
@keyframes fadeOut-e9b92a61{
from{
    opacity: 1;
    transform: translateY(0);
}
to{
    opacity: 0;
    transform: translateY(0.3125rem);
}
}


.empty-page[data-v-5cea664a] {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 12.5rem;
  padding: 1.875rem 1.25rem;
}
.empty-content[data-v-5cea664a] {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}
.empty-image[data-v-5cea664a] {
  margin-bottom: 1.25rem;
  opacity: 0.6;
}
.empty-title[data-v-5cea664a] {
  font-size: 1rem;
  font-weight: 500;
  color: #333;
  margin-bottom: 0.625rem;
  line-height: 1.4;
}
.empty-description[data-v-5cea664a] {
  font-size: 0.8125rem;
  color: #999;
  line-height: 1.5;
  margin-bottom: 1.25rem;
  max-width: 12.5rem;
}
.empty-button[data-v-5cea664a] {
  padding: 0.625rem 1.25rem;
  background: #007aff;
  color: #fff;
  border-radius: 1.25rem;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.3s ease;
}
.empty-button[data-v-5cea664a]:active {
  background: #0056cc;
  transform: scale(0.95);
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
.empty-title[data-v-5cea664a] {
    color: #fff;
}
.empty-description[data-v-5cea664a] {
    color: #ccc;
}
}


.nav-box{
  position:fixed;
  z-index:99;
  top:0;
  left:0;
  width:100%;
  box-sizing:border-box;
  transition:all .3s ease-in-out
}
.nav-box .nav-item{
  position: relative;
  width: 100%;
  justify-content: center;
  align-items: center;
  height: 100%;
}
.nav-box .nav-item .ohto{
  max-width: 13.125rem;
  font-size: 0.8125rem;
  font-weight: 700;
  transition: all 0.3s ease-in-out;
  align-items: center;
  justify-content: center;
  height: 100%;
}
.nav-user-avatar {
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 50%;
  margin-right: 0.375rem;
}
.user-box{
  width:calc(100% - 1.875rem);
  padding:1.875rem 0.9375rem;
  color:#fff;
  position:relative;
  overflow:hidden
}
.user-box .user-img,
.user-box .user-bg{
  position:absolute;
  top:0;
  left:0;
  width:100%;
  height:100%
}
.user-box .user-bg{
  z-index:-1;
  /* 优化性能：条件编译backdrop-filter */




  background:rgba(0,0,0,.6)
}
.user-box .user-top{
  width:100%;
  justify-content:space-between
}
.user-top .avatar{
  width:4.375rem;
  height:4.375rem;
  border-radius:50%;
  background:#fff;
  border:2px solid #f5f5f5;
  overflow:hidden
}
.user-box .user-name{
  margin:0.625rem 0 0.3125rem;
  width:100%;
  font-size:1.0625rem;
  font-weight:700
}
.user-box .user-intro{
  width:100%;
  word-break:break-word;
  white-space:pre-line
}
.user-box .user-intro uni-text{
  color:#ccc;
  font-size:0.75rem;
  font-weight:400
}
.user-box .user-tag{
  margin:0.625rem 0;
  width:100%
}
.user-tag .tag-item{
  margin-right:0.5rem;
  height:1.375rem;
  padding:0 0.4375rem;
  border-radius:0.25rem;
  background:rgba(255,255,255,.15);
  font-weight:500;
  font-size:0.625rem;
  justify-content:center
}
.user-tag .tag-item uni-image{
  width:0.75rem;
  height:0.75rem
}
.user-num-wrap {
  width: 100%;
  justify-content: space-between;
  align-items: center;
  margin-top: 0.625rem;
}
.user-num {
  flex: 1;
}
.user-num .num-item {
  margin-right: 0.9375rem;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.user-num .num-item .t1 {
  color: #fff;
  font-size: 1rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
}
.user-num .num-item .t2 {
  font-size: 0.625rem;
  font-weight: 300;
  color: #ccc;
}
.visitor-item {
  position: relative;
}
.visitor-item .badge {
  position: absolute;
  top: -0.375rem;
  right: -0.875rem;
  min-width: 1rem;
  height: 1rem;
  padding: 0 0.1875rem;
  background: #ff3a3a;
  color: #fff;
  border-radius: 0.5rem;
  font-size: 0.625rem;
  text-align: center;
  line-height: 1rem;
  z-index: 2;
  box-shadow: 0 0.0625rem 0.125rem rgba(0,0,0,0.2);
}
.user-actions {
  align-items: center;
}
.btn-item {
  padding: 0 0.9375rem;
  height: 2rem;
  line-height: 2rem;
  border-radius: 0.25rem;
  font-size: 0.625rem;
  font-weight: 700;
}
.bg1 {
  color: #fff;
  background: rgba(255,255,255,.15);
}
.btn-icon {
  width: 2rem;
  height: 2rem;
  border-radius: 0.25rem;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 0.625rem;
}
.btn-icon uni-image {
  width: 1rem;
  height: 1rem;
}
.user-block{
  margin-top:-0.9375rem;
  width:100%;
  white-space:nowrap;
  background:#fff;
  border-radius:0.9375rem 0.9375rem 0 0
}
.user-block .block-box{
  width:100%;
  padding:0.9375rem 0.46875rem;
  display:flex
}
.block-box .block-item{
  flex-shrink:0;
  margin:0 0.46875rem;
  padding:0.75rem;
  background:#f8f8f8;
  border-radius:0.5rem;
  justify-content:space-between;
  position:relative
}
.block-item .block-title .t1{
  font-size:0.8125rem;
  font-weight:700
}
.block-item .block-title .t2{
  margin-top:0.125rem;
  color:#999;
  font-size:0.5rem;
  font-weight:300
}
.block-item .cu-group{
  position:relative;
  right:1.1875rem
}
.cu-group .cu-item{
  z-index:3;
  width:2.125rem;
  height:2.125rem;
  border-radius:0.25rem;
  overflow:hidden
}
.cu-group .cu-item .icon{
  margin:0.5625rem;
  width:1rem;
  height:1rem
}
.cu-group .cu-item .img{
  width:100%;
  height:100%
}
.cu-group .cu-lump2{
  position:absolute;
  z-index:2;
  left:0.5625rem;
  width:1.8125rem;
  height:1.8125rem;
  border-radius:0.25rem;
  background:#dbdbdb
}
.cu-group .cu-lump1{
  position:absolute;
  z-index:1;
  left:1.1875rem;
  width:1.5rem;
  height:1.5rem;
  border-radius:0.25rem;
  background:#eaeaea
}
.block-item .block-icon{
  position:absolute;
  right:0.375rem;
  width:0.625rem;
  height:0.625rem;
  transform:rotate(-90deg)
}
.bar-box{
  position:-webkit-sticky;
  position:sticky;
  left:0;
  z-index:99;
  margin-top:-1px;
  width:100%;
  height:2.5rem;
  background:#fff
}
.bar-box .bar-item{
  padding:0 0.9375rem;
  height:100%;
  flex-direction:column;
  justify-content:center;
  position:relative
}
.bar-box .bar-item uni-text{
  font-weight:700;
  transition:all .3s ease-in-out
}
.bar-item .bar-line{
  position:absolute;
  bottom:0.375rem;
  width:0.5625rem;
  height:0.1875rem;
  border-radius:0.1875rem;
  background:#000;
  transition:opacity .3s ease-in-out
}
/* 优化的CSS类名 */
.content-container{
  padding-bottom:5.625rem;
  /* 优化渲染性能 */
  contain: layout style paint;
}

/* 动态列表优化 */
.dynamic-list {
  /* 启用硬件加速 */
  transform: translateZ(0);
  /* 优化滚动性能 */
  -webkit-overflow-scrolling: touch;
}

/* 加载更多容器 */
.load-more-container {
  padding: 0.625rem 0;
  text-align: center;
}
.like-popup{
  background:#fff;
  width:12.5rem;
  padding:0.9375rem;
  border-radius:0.9375rem;
  overflow:hidden
}
.like-popup .like-img{
  margin:0 1.25rem;
  width:10rem;
  height:6.25rem
}
.like-popup .like-content{
  margin:0.625rem 0 1.25rem;
  width:100%;
  color:#333;
  font-size:0.8125rem;
  text-align:center
}
.like-popup .like-btn{
  width:100%;
  height:2.5rem;
  line-height:2.5rem;
  text-align:center;
  font-size:0.75rem;
  font-weight:700;
  color:#fff;
  background:#000;
  border-radius:0.5rem
}
.df {
  display: flex;
  align-items: center;
}
.ohto {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.empty-state {
  flex-direction: column;
  padding: 3.75rem 0;
}
.empty-state uni-image {
  width: 8.75rem;
  height: 8.75rem;
}
.empty-state .empty-title {
  margin-top: 1.25rem;
  font-size: 1rem;
  font-weight: bold;
}
.empty-state .empty-subtitle {
  margin-top: 0.625rem;
  font-size: 0.75rem;
  color: #999;
}

/* 重试按钮样式已删除 */
.loading-state {
  flex-direction: column;
  padding: 3.75rem 0;
}
.loading-text {
  margin-top: 0.625rem;
  font-size: 0.75rem;
  color: #999;
}
.loading-indicator {
  justify-content: center;
}
.nav-menu-btn {
  position: absolute;
  left: 0.9375rem;
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 50%;
  background: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
  -webkit-tap-highlight-color: transparent;
}
.nav-menu-btn uni-image {
  width: 0.75rem;
  height: 0.75rem;
}
.nav-menu-btn:active {
  background: rgba(0,0,0,0.5);
}
.sidebar-menu {
  position: fixed;
  top: 0;
  left: -75%;
  width: 75%;
  height: 100%;
  max-height: 100vh;
  background: #fff;
  z-index: 999;
  box-shadow: 0.0625rem 0 0.3125rem rgba(0,0,0,0.1);
  transform: translateX(0);
  transition: transform 0.3s ease-in-out;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
.sidebar-menu.active {
  transform: translateX(100%);
}
.sidebar-header {
  flex-shrink: 0;
  padding: 0.9375rem;
  border-bottom: 1px solid #f5f5f5;
  position: relative; /* 为关闭按钮提供定位基准 */
}
.sidebar-user-info {
  display: flex;
  align-items: center;
}
.sidebar-avatar {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  margin-right: 0.625rem;
}
.sidebar-user-details {
  flex: 1;
  overflow: hidden;
}
.sidebar-user-name {
  font-size: 0.9375rem;
  font-weight: 500;
  margin-bottom: 0.25rem;
}
.user-status {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.status-item {
  display: flex;
  align-items: center;
  padding: 0.125rem 0.3125rem;
  border-radius: 0.625rem;
  margin-right: 0.375rem;
  font-size: 0.625rem;
  margin-top: 0.125rem;
}
.status-icon {
  width: 2.5rem;
  height: 1.25rem;
  margin-right: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
.status-icon uni-image {
  width: 2.5rem;
  height: 1.125rem;
  display: block; /* 确保图片正确显示 */
}
.member-card {
  flex-shrink: 0;
  margin: 0 0.9375rem 0.625rem;
  padding: 0.9375rem 0.625rem;
  background: #2c2c2c;
  border-radius: 0.5rem;
  color: #fff;
}
.member-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}
.member-label {
  font-size: 0.9375rem;
  font-weight: bold;
}
.member-price {
  padding: 0.1875rem 0.625rem;
  background: #fff;
  color: #333;
  border-radius: 0.9375rem;
  font-size: 0.6875rem;
}
.member-benefits {
  margin-bottom: 0.3125rem;
}
.member-rights {
  font-size: 0.6875rem;
  color: rgba(255,255,255,0.8);
}
.member-desc {
  margin-top: 0.5rem;
  font-size: 0.6875rem;
  color: rgba(255,255,255,0.7);
}
.sidebar-scroll {
  flex: 1;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
}
.sidebar-content {
  padding: 0.5rem 0 6.25rem;
  background-color: #f7f7f7;
}

/* 菜单部分样式 */
.menu-section {
  margin: 0.625rem;
  background-color: #fff;
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 0.0625rem 0.25rem rgba(0,0,0,0.05);
}
.section-title {
  padding: 0.625rem 0.9375rem 0.3125rem;
  font-size: 0.875rem;
  font-weight: bold;
  color: #333;
  border-bottom: 1px solid #f5f5f5;
}

/* 菜单宫格样式 */
.menu-grid {
  display: flex;
  flex-wrap: wrap;
  padding: 0.3125rem;
  background-color: #fff;
}
.grid-item {
  width: 33.33%;
  text-align: center;
  padding: 0.625rem 0;
  position: relative;
}
.grid-item:active {
  background-color: #f8f8f8;
}
.grid-icon-wrapper {
  position: relative;
  margin: 0 auto 0.3125rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
.grid-icon {
  width: 1.5625rem;
  height: 1.5625rem;
}
.grid-badge {
  position: absolute;
  top: -0.1875rem;
  right: -0.1875rem;
  min-width: 1rem;
  height: 1rem;
  padding: 0 0.1875rem;
  background: #ff3a3a;
  color: #fff;
  border-radius: 0.5rem;
  font-size: 0.625rem;
  text-align: center;
  line-height: 1rem;
  z-index: 2;
  box-shadow: 0 0.0625rem 0.125rem rgba(0,0,0,0.2);
}
.grid-text {
  font-size: 0.75rem;
  color: #333;
  display: block;
  padding: 0 0.3125rem;
}
.sidebar-footer {
  flex-shrink: 0;
  background: #fff;
}
.bottom-nav {
  width: 100%;
  height: 3.75rem;
  justify-content: space-around;
  padding: 0;
  background-color: #f7f7f7;
}
.bottom-nav-item {
  flex: 1;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
}
.nav-icon-box {
  width: 1.5625rem;
  height: 1.5625rem;
  justify-content: center;
  align-items: center;
}
.nav-icon {
  width: 1.375rem;
  height: 1.375rem;
}
.nav-text {
  font-size: 0.6875rem;
  color: #666;
  margin-top: 0.25rem;
}
.copyright-text {
  text-align: center;
  color: #999;
  font-size: 0.625rem;
  padding: 0.3125rem 0 0.9375rem;
}
.sidebar-mask {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: rgba(0,0,0,0.5);
  z-index: 998;
  touch-action: none;
}
.no-scroll {
  overflow: hidden !important;
}
.container.no-scroll,
.container[style*="position: fixed"] {
  position: fixed !important;
  left: 0 !important;
  width: 100% !important;
  overflow: hidden !important;
  touch-action: none !important;
  height: 100vh !important;
}
.container[style*="position: fixed"] .user-box,
.container[style*="position: fixed"] .nav-box {
  transform: none !important;
}
@media screen and (max-height: 667px) {
.sidebar-scroll {
    height: calc(100vh - 10.9375rem - 5.3125rem);
}
}
.sidebar-item:active {
  background-color: #f8f8f8;
}
.menu-group {
  margin-bottom: 0;
  margin: 0 0.625rem;
  border-radius: 0.5rem;
  background-color: #fff;
  overflow: hidden;
}
.menu-divider {
  height: 0.5rem;
  background-color: #f7f7f7;
  margin: 0;
  width: 100%;
}
.close-btn {
  position: absolute;
  right: 0.9375rem;
  top: 50%;
  transform: translateY(-50%) rotate(45deg);
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 50%;
  background: #f8f8f8;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}
uni-button, 
.btn, 
.nav-menu-btn, 
uni-view[role="button"] {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}
.user-top {
  width: 100%;
  align-items: flex-start;
  margin-bottom: 0.625rem;
}
.avatar-wrapper {
  position: relative;
  margin-right: 0.625rem;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.avatar {
  width: 4.375rem;
  height: 4.375rem;
  border-radius: 50%;
  background: #fff;
  border: 2px solid #f5f5f5;
  overflow: hidden;
  position: relative;
}
.profile-percent {
  position: absolute;
  left: 50%;
  top: 0;
  transform: translateX(-50%);
  background: #ff6600;
  border-radius: 0.375rem;
  padding: 0.125rem 0.3125rem;
  display: flex;
  align-items: center;
  font-size: 0.625rem;
  color: #fff;
  z-index: 2;
  box-shadow: 0 0.0625rem 0.125rem rgba(0,0,0,0.2);
}
.edit-icon {
  width: 0.75rem;
  height: 0.75rem;
  margin-right: 0.125rem;
}
.user-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding-top: 0.625rem;
}
.user-name-row {
  align-items: center;
  margin-bottom: 0.3125rem;
}
.user-name-text {
  font-size: 1.0625rem;
  font-weight: 700;
  color: #fff;
  margin-right: 0.375rem;
}
.status-icon {
  width: 2.5rem;
  height: 1.25rem;
  margin-right: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
.status-icon uni-image {
  width: 2.5rem;
  height: 1.125rem;
  display: block; /* 确保图片正确显示 */
}
.user-id {
  font-size: 0.6875rem;
  color: rgba(255,255,255,0.6);
}
.user-id-row {
  align-items: center;
  margin-top: 0.25rem;
}
.sex-icon {
  width: 1rem;
  height: 1rem;
  border-radius: 0.1875rem;
  background: rgba(255,255,255,0.15);
  align-items: center;
  justify-content: center;
  margin-right: 0.375rem;
}
.sex-icon uni-image {
  width: 0.625rem;
  height: 0.625rem;
}
.vip-icon {
  border-radius: 0.1875rem;
  padding: 0.0625rem;
}
.verified-icon {
  border-radius: 0.1875rem;
  padding: 0.0625rem;
}
.tag-wrapper {
  position: relative;
  width: 100%;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}
.tag-scroll-view {
  width: calc(100% - 2.1875rem);
  white-space: nowrap;
}
.tag-add-btn {
  position: absolute;
  right: 0;
  width: 1.4375rem;
  height: 1.4375rem;
  border-radius: 1.4375rem;
  background: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
}
.tag-add-btn uni-text {
  font-size: 1.25rem;
  color: #666;
  font-weight: 300;
}
.tag-add-empty {
  width: 100%;
  height: 1.875rem;
  background: #f8f8f8;
  border-radius: 0.9375rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
.tag-add-empty uni-text {
  color: #999;
  font-size: 0.8125rem;
}
.tag-empty {
  flex: 1;
  height: 1.875rem;
  background: rgba(255,255,255,.15);
  border-radius: 0.9375rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.625rem;
}
.tag-empty uni-text {
  color: rgba(255,255,255,0.6);
  font-size: 0.75rem;
}
.user-tag {
  flex-wrap: nowrap;
  padding: 0;
  margin-top: 0;
}
.tag-wrapper .user-tag {
  display: inline-flex;
  padding-right: 0.625rem;
}
.tag-wrapper .tag-item {
  margin-right: 0.625rem;
  flex-shrink: 0;
}
.menu-grid {
  display: flex;
  flex-wrap: wrap;
  padding: 0.625rem 0.3125rem;
  background-color: #fff;
}
.grid-item {
  width: 33.33%;
  text-align: center;
  padding: 0.625rem 0;
  position: relative;
}
.grid-icon {
  width: 1.5625rem;
  height: 1.5625rem;
}
.grid-badge {
  position: absolute;
  top: -0.1875rem;
  right: -0.1875rem;
  min-width: 1rem;
  height: 1rem;
  padding: 0 0.1875rem;
  background: #ff3a3a;
  color: #fff;
  border-radius: 0.5rem;
  font-size: 0.625rem;
  text-align: center;
  line-height: 1rem;
  z-index: 2;
  box-shadow: 0 0.0625rem 0.125rem rgba(0,0,0,0.2);
}
.grid-text {
  font-size: 0.75rem;
  color: #333;
  display: block;
  padding: 0 0.3125rem;
}
.profile-progress {
  position: absolute;
  top: -0.25rem;
  left: -0.25rem;
  right: -0.25rem;
  bottom: -0.25rem;
  z-index: 10;
}
.progress-circle {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}
.progress-inner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0,0,0,0.6);
  border-radius: 0.625rem;
  padding: 0.125rem 0.25rem;
  -webkit-backdrop-filter: blur(0.3125rem);
          backdrop-filter: blur(0.3125rem);
}
.progress-text {
  color: #fff;
  font-size: 0.625rem;
  font-weight: 700;
  line-height: 1;
}
.avatar {
  position: relative;
}
.user-box .user-intro{
  width:100%;
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}
.user-box .user-intro .intro-text{
  color:#ccc;
  font-size:0.75rem;
  font-weight:400;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
  word-break: break-all;
  flex: 1;
}
.user-box .user-intro .more-btn{
  position: absolute;
  right: 0;
  top: 0;
  width: 1.5625rem;
  height: 1.5625rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
.user-box .user-intro .more-btn uni-image{
  width: 1rem;
  height: 1rem;
}
.user-box .user-top{
  width:100%;
  justify-content:space-between;
  position: relative;
  padding-right: 1.25rem;
}
.user-box .user-top .right-arrow {
  position: absolute;
  right: 0;
  top: 50%;
  display: flex;
  transform: translateY(-50%) rotate(270deg)
}
.user-box .user-top .right-arrow uni-image {
  width: 4.375rem;
  height: 1.25rem;
}
.stat-box {
  display: flex;
  justify-content: space-around;
  align-items: center;
  width: calc(100% - 1.875rem);
  padding: 0.625rem 0;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 0.625rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.2);
  margin: 0.625rem auto;
  position: relative;
}
.stat-divider {
  width: 1px;
  height: 1.875rem;
  background-color: rgba(255, 255, 255, 0.2);
}
.stat-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  text-align: center;
  padding: 0.3125rem 0;
}
.stat-icon {
  width: 1.25rem;
  height: 1.25rem;
  margin-bottom: 0.5rem;
  display: flex;
  justify-content: center;
  align-items: center;
}
.stat-icon .iconfont {
  font-size: 1.125rem;
}
.stat-value {
  font-size: 1.125rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 0.25rem;
  line-height: 1;
}
.stat-label {
  font-size: 0.6875rem;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1;
}
.user-num-wrap {
  width: 100%;
}
.stat-badge {
  position: absolute;
  top: -0.25rem;
  right: -0.3125rem;
  min-width: 0.9375rem;
  height: 0.9375rem;
  line-height: 0.9375rem;
  padding: 0 0.1875rem;
  background-color: #ff3a3a;
  color: #ffffff;
  font-size: 0.5rem;
  border-radius: 0.46875rem;
  text-align: center;
}
.stat-icon .icon-like {
  color: #ff7ca8;
  font-size: 1.3125rem;
}
.stat-icon .icon-eye {
  color: #e3d6ff;
  font-size: 1.3125rem;
}
.stat-icon .icon-heart {
  color: #e3d6ff;
  font-size: 1.3125rem;
}
.stat-like-text {
  color: #ff7ca8;
  font-size: 1.3125rem;
}
.stat-eye-text {
  color: #e3d6ff;
  font-size: 1.3125rem;
}

/* 空菜单提示样式 */
.empty-menu-tip {
  padding: 1.875rem 0;
  text-align: center;
  color: #999;
  font-size: 0.75rem;
}
.stat-heart-text {
  color: #e3d6ff;
  font-size: 1.3125rem;
}
