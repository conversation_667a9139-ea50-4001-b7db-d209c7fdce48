{"version": 3, "file": "dynamic.js", "sources": ["/pages/index/dynamic.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvdGFiYmFyL2R5bmFtaWMudnVl"], "sourcesContent": ["<template>\n  <view class=\"container\">\n    <view class=\"nav-box\" :style=\"{'padding-top': statusBarHeight + 'px'}\">\n      <view class=\"bar-box df\" :style=\"{'height': titleBarHeight + 'px'}\">\n        <!-- 水平滚动导航 -->\n        <view class=\"nav-scroll-box\">\n          <scroll-view \n            id=\"nav-scroll\" \n            scroll-x=\"true\" \n            show-scrollbar=\"false\" \n            class=\"nav-scroll\" \n            :scroll-into-view=\"'tab' + navIdx\"\n            scroll-with-animation=\"true\"\n          >\n            <view class=\"nav-scroll-content\">\n              <view \n                v-for=\"(item, index) in navList\" \n                :key=\"index\" \n                class=\"nav-item\" \n                :class=\"{'active': navIdx === index}\"\n                :data-idx=\"index\" \n                :id=\"'tab' + index\"\n                @tap=\"navClick\"\n              >\n                <text>{{item}}</text>\n                <view v-if=\"navIdx === index\" class=\"active-line\"></view>\n              </view>\n            </view>\n          </scroll-view>\n        </view>\n          </view>\n        </view>\n    \n    <view class=\"content-box\" :style=\"{'padding-top': (statusBarHeight + titleBarHeight) + 'px'}\">\n      <swiper \n        class=\"content-swiper\" \n        :current=\"navIdx\" \n        @change=\"swiperChange\"\n        :style=\"{'height': contentHeight + 'px'}\"\n        :duration=\"300\"\n        :easing-function=\"'easeInOutCubic'\"\n        :skip-hidden-item-layout=\"true\"\n        :disable-touch=\"false\"\n      >\n        <!-- 关注页 -->\n        <swiper-item>\n          <scroll-view \n            scroll-y=\"true\" \n            @scrolltolower=\"onReachEnd\" \n            @scroll=\"onScroll\"\n            class=\"content-scroll\"\n            :show-scrollbar=\"true\"\n          >\n            <!-- 未登录状态 -->\n            <view v-if=\"!isLogin && navIdx === 0\" class=\"empty-box df\">\n              <image src=\"/static/img/empty.png\"/>\n              <view class=\"e1\">登录后浏览关注内容</view>\n              <view class=\"e2\">登录账号，查看关注的好友动态</view>\n              <view class=\"login-action\">\n                <button class=\"login-btn\" @tap=\"toLogin\">立即登录</button>\n                <!-- #ifdef MP -->\n                <view class=\"login-tips\">微信用户可一键登录</view>\n                <!-- #endif -->\n                <!-- #ifdef H5 -->\n                <view class=\"login-tips\">{{isWeixin ? '微信用户可一键登录' : '登录后享受更多功能'}}</view>\n                <!-- #endif -->\n                <!-- #ifdef APP-PLUS -->\n                <view class=\"login-tips\">登录后享受更多功能</view>\n                <!-- #endif -->\n              </view>\n            </view>\n            \n            <!-- 空数据状态 -->\n            <view v-else-if=\"isEmpty && navIdx === 0\" class=\"empty-box df\">\n              <image src=\"/static/img/empty.png\"/>\n              <view class=\"e1\">暂无关注内容</view>\n              <view class=\"e2\">去探索，发现新朋友</view>\n            </view>\n\n            <!-- 有数据状态 -->\n            <view v-else-if=\"!isEmpty && list.length > 0\">\n                <block v-for=\"(item, index) in displayItems\" :key=\"index\">\n                  <card-gg :item=\"item\" :idx=\"index\" @likeback=\"likeClick\" @followback=\"followClick\" @update=\"onCardUpdate\"></card-gg>\n              </block>\n              <uni-load-more :status=\"loadStatus\"></uni-load-more>\n            </view>\n          </scroll-view>\n        </swiper-item>\n        \n        <!-- 推荐页 -->\n        <swiper-item>\n          <scroll-view \n            scroll-y=\"true\" \n            @scrolltolower=\"onReachEnd\" \n            @scroll=\"onScroll\"\n            class=\"content-scroll\"\n            :show-scrollbar=\"true\"\n          >\n            <!-- 圈子推荐区域 -->\n            <scroll-view v-if=\"navIdx === 1 && circle.length > 0\" scroll-x=\"true\" class=\"scroll-box\" style=\"height: 246rpx\">\n              <view class=\"circle-box\">\n                <view v-for=\"(item, index) in circle\" :key=\"index\" class=\"circle-item\" :data-url=\"'note/circle?id=' + item.id\" @tap=\"navigateToFun\">\n                  <view class=\"circle-item-top\">\n                    <image :src=\"item.circle_avatar || item.avatar\" mode=\"aspectFill\"></image>\n                    <view v-if=\"item.is_official == 1\" class=\"circle-item-tag\" style=\"background: url(/static/img/gf.png) 0% 0% / 100% 100%;\"></view>\n                    <view v-else-if=\"item.is_hot == 1\" class=\"circle-item-tag\" style=\"background: url(/static/img/tj.png) 0% 0% / 100% 100%;\"></view>\n                  </view>\n                  \n                  <view class=\"circle-name ohto\">{{item.circle_name || item.name}}</view>\n                  <view class=\"circle-tips\">\n                    <text v-if=\"item.dynamic_count\">{{item.dynamic_count}}更新</text>\n                    <text v-else-if=\"item.member_count || item.user_count\">{{item.member_count || item.user_count}}新圈友</text>\n                    <text v-else>推荐的圈子</text>\n                  </view>\n                </view>\n                <view class=\"circle-item\" data-url=\"center/circle\" @tap=\"navigateToFun\">\n                  <view class=\"circle-item-top\">\n                    <image class=\"icon\" src=\"/static/img/more.png\"></image>\n                  </view>\n                  <view class=\"circle-name\">更多圈子</view>\n                </view>\n                <view class=\"circle-item\" style=\"width:10rpx\"></view>\n              </view>\n            </scroll-view>\n            \n            <!-- 空数据状态 -->\n            <view v-if=\"isEmpty && navIdx === 1\" class=\"empty-box df\">\n        <image src=\"/static/img/empty.png\"/>\n              <view class=\"e1\">暂无推荐内容</view>\n              <view class=\"e2\">去发笔记，或许就上推荐了</view>\n      </view>\n\n            <!-- 有数据状态 -->\n      <view v-else-if=\"!isEmpty && list.length > 0\" :class=\"[isWaterfall ? 'dynamic-box' : '']\">\n              <waterfall v-if=\"isWaterfall\" :activity=\"[]\" :note=\"displayItems\" :page=\"page\" @likeback=\"waterfallLikeClick\" @followback=\"followClick\"></waterfall>\n        <block v-else>\n                <block v-for=\"(item, index) in displayItems\" :key=\"index\">\n                  <card-gg :item=\"item\" :idx=\"index\" @likeback=\"likeClick\" @followback=\"followClick\" @refresh=\"fetchList\"></card-gg>\n          </block>\n        </block>\n        <uni-load-more :status=\"loadStatus\"></uni-load-more>\n      </view>\n          </scroll-view>\n        </swiper-item>\n        \n        <!-- 附近页 -->\n        <swiper-item>\n          <scroll-view \n            scroll-y=\"true\" \n            @scrolltolower=\"onReachEnd\" \n            @scroll=\"onScroll\"\n            class=\"content-scroll\"\n            :show-scrollbar=\"true\"\n          >\n            <!-- 空数据状态 -->\n            <view v-if=\"isEmpty && navIdx === 2\" class=\"empty-box df\">\n              <image src=\"/static/img/empty.png\"/>\n              <view class=\"e1\">附近暂无内容</view>\n              <view class=\"e2\">去发笔记，让大家看到你</view>\n            </view>\n\n            <!-- 有数据状态 -->\n            <view v-else-if=\"!isEmpty && list.length > 0\" :class=\"[isWaterfall ? 'dynamic-box' : '']\">\n              <waterfall v-if=\"isWaterfall\" :activity=\"[]\" :note=\"displayItems\" :page=\"page\" @likeback=\"waterfallLikeClick\" @followback=\"followClick\"></waterfall>\n              <block v-else>\n                <block v-for=\"(item, index) in displayItems\" :key=\"index\">\n                  <card-gg :item=\"item\" :idx=\"index\" @likeback=\"likeClick\" @followback=\"followClick\" @refresh=\"fetchList\"></card-gg>\n                </block>\n              </block>\n              <uni-load-more :status=\"loadStatus\"></uni-load-more>\n            </view>\n          </scroll-view>\n        </swiper-item>\n      </swiper>\n    </view>\n    <tabbar :currentPage=\"1\" :currentMsg=\"currentMsg\"></tabbar>\n  </view>\n</template>\n\n<script>\nimport lazyImage from '@/components/lazyImage/lazyImage'\nimport uniLoadMore from '@/uni_modules/uni-load-more/components/uni-load-more/uni-load-more'\nimport waterfall from '@/components/waterfall/waterfall'\nimport cardGg from '@/components/card-gg/card-gg'\nimport tabbar from '@/components/tabbar/tabbar'\n\n// 直接导入所需的API函数，移除未使用的API\nimport { getDynamicList, getHotCircles } from '@/api/social.js'\nimport { toLogin } from '@/libs/login.js'\nimport { useUserStore } from '@/stores/user.js'\n\n// 移除未使用的可见性检测工具\nexport default {\n  components: {\n    lazyImage,\n    uniLoadMore,\n    waterfall,\n    cardGg,\n    tabbar\n  },\n  data() {\n    return {\n      userStore: useUserStore(),\n      statusBarHeight: 20,\n      titleBarHeight: 44,\n      currentMsg: 0,\n      userCity: \"\",\n      userUid: 0,\n      userLatitude: 0,\n      userLongitude: 0,\n      // #ifdef H5\n      isWeixin: false,\n      // #endif\n      navList: [\"关注\", \"推荐\", \"附近\"],\n      navIdx: 1,\n      list: [],\n\n      // 优化后的缓存系统\n      cachedData: {\n        0: { list: [], page: 1, hasMore: true, lastUpdate: 0, renderItems: 15, totalCount: 0 },\n        1: { list: [], page: 1, hasMore: true, lastUpdate: 0, renderItems: 15, totalCount: 0 },\n        2: { list: [], page: 1, hasMore: true, lastUpdate: 0, renderItems: 15, totalCount: 0 }\n      },\n\n      circle: [], // 圈子数据\n      circleLoaded: false, // 圈子是否已加载\n      page: 1,\n      totalCount: 0, // 总数据量\n      isEmpty: false,\n      loadStatus: \"more\",\n      isWaterfall: false,\n      contentHeight: 0,\n\n      // 加载状态管理 - 参考center.vue\n      loadingStates: {\n        refresh: false,\n        loadMore: false,\n        initial: false\n      },\n\n      // 防抖定时器 - 提升流畅度\n      requestTimers: {\n        loadData: null\n      },\n\n      // 简化的加载状态管理\n      isLoading: false,\n      isRefreshing: false,\n      isSwitching: false,\n\n      // 优化渲染性能\n      renderItems: 15,\n      renderStep: 10,\n      maxRenderItems: 100,\n\n      // 基础状态\n      _firstLoad: true,\n      _lastLoginState: null,\n      _navigateLock: false,\n\n      // 缓存配置\n      cacheConfig: {\n        maxAge: 5 * 60 * 1000,    // 5分钟缓存过期\n        maxItems: 50,             // 每个tab最多缓存50条\n        preloadThreshold: 3       // 剩余3条时预加载\n      },\n\n      // 性能优化\n      debounceTimer: null,\n      scrollTimer: null,\n      scrollRAF: null,\n\n      // 格式化缓存\n      _formattedCache: null,\n      _lastListLength: 0,\n      _reachEndThrottle: false,\n\n      // 性能监控\n      _performanceStart: 0\n    }\n  },\n  mounted() {\n    this.calcContentHeight();\n    this.initPlatformFeatures();\n    this.getUserLocation();\n  },\n  beforeDestroy() {\n    // 不再解绑事件，统一到onUnload\n  },\n  async onLoad() {\n    // 初始化页面基础设置\n    this.calcContentHeight();\n\n    // 注册事件监听\n    uni.$on('flowSettingsUpdated', this.handleFlowSettingsUpdate);\n    uni.$on('loginStateChanged', this.handleLoginStateChanged);\n\n    // 初始化用户信息\n    this.initUserInfoFromCache();\n    this._firstLoad = true;\n    this._lastLoginState = this.isLogin;\n\n    // 设置导航栏样式\n    uni.setNavigationBarColor({\n      frontColor: \"#000000\",\n      backgroundColor: \"#ffffff\",\n      animation: { duration: 400, timingFunc: \"easeIn\" }\n    });\n\n    // 异步加载数据\n    this.$nextTick(() => {\n      this.initializePageData().catch(err => {\n        this.handleInitError();\n      });\n    });\n  },\n  onShow() {\n    // 检查登录状态变化\n    const token = this.userStore.token;\n    const isLoggedIn = this.isLogin && token;\n\n    if (this._lastLoginState !== isLoggedIn) {\n      this._lastLoginState = isLoggedIn;\n      this.handleLoginStateChange(isLoggedIn);\n    }\n\n    // 更新消息数量\n    if (isLoggedIn) {\n      const userInfo = this.getUserInfoFromCache();\n      this.currentMsg = userInfo.service_num || 0;\n    }\n\n    // 检查缓存是否需要刷新\n    this.checkCacheExpiry();\n\n    // 设置导航栏样式\n    uni.setNavigationBarColor({\n      frontColor: \"#000000\",\n      backgroundColor: \"#ffffff\",\n      animation: { duration: 400, timingFunc: \"easeIn\" }\n    });\n  },\n  onPullDownRefresh() {\n    this.page = 1;\n    this.loadStatus = \"more\";\n    this.isEmpty = false;\n    this.isRefreshing = true;\n\n    this.loadData(true)\n      .catch(() => {\n        uni.showToast({\n          title: '刷新失败，请稍后再试',\n          icon: 'none',\n          duration: 2000\n        });\n      })\n      .finally(() => {\n        setTimeout(() => {\n          this.isRefreshing = false;\n          uni.stopPullDownRefresh();\n        }, 300);\n      });\n  },\n  onReachBottom() {\n    // 直接实现上拉加载逻辑，避免多层调用影响性能\n    // 防止重复加载或已经加载完所有数据\n    if (this.isLoading || this.loadingStates.loadMore || this.loadStatus === 'noMore') {\n      return;\n    }\n\n    // 检查登录状态\n    const isLoggedIn = this.isLogin && this.$store.state.app.token;\n\n    // 关注页面未登录不加载\n    if (this.navIdx === 0 && !isLoggedIn) {\n      return;\n    }\n\n    // 检查是否还有更多数据\n    // 条件：有数据 且 (总数未知 或 当前数据量小于总数) 且 加载状态允许\n    const canLoadMore = this.list.length > 0 &&\n                       (this.totalCount === 0 || this.list.length < this.totalCount) &&\n                       this.loadStatus !== 'noMore';\n\n    if (canLoadMore) {\n      this.loadingStates.loadMore = true;\n      this.loadStatus = 'loading';\n      const currentPage = this.page; // 保存当前页码\n      this.page++;\n\n      this.loadData(false, true).catch(err => {\n        // 加载失败时恢复页码和状态\n        this.page = currentPage;\n        this.loadStatus = 'more';\n\n        // 显示友好的错误提示\n        if (err.message !== '正在加载中') {\n          this.showErrorToast('加载更多失败，请稍后再试', 2000);\n        }\n      }).finally(() => {\n        this.loadingStates.loadMore = false;\n      });\n    } else if (this.list.length >= this.totalCount && this.list.length > 0) {\n      this.loadStatus = 'noMore';\n    }\n  },\n  onShareAppMessage() {\n    return {\n      title: this.$store.state.appXx[1] || '小程序示例',\n      imageUrl: this.$store.state.appXx[2] || '/static/img/avatar.png'\n    }\n  },\n  onShareTimeline() {\n    return {\n      title: this.$store.state.appXx[1] || '小程序示例',\n      imageUrl: this.$store.state.appXx[2] || '/static/img/avatar.png'\n    }\n  },\n  onUnload() {\n    try {\n      // 移除事件监听\n      if (uni.$off) {\n        uni.$off('flowSettingsUpdated', this.handleFlowSettingsUpdate);\n        uni.$off('loginStateChanged', this.handleLoginStateChanged);\n      }\n\n      // 平台特定的清理\n      if (this.cleanupPlatformFeatures) {\n        this.cleanupPlatformFeatures();\n      }\n\n      // 重置状态\n      if (this.resetLoadingState) {\n        this.resetLoadingState();\n      }\n\n      // 清理定时器和内存\n      if (this.clearTimers) {\n        this.clearTimers();\n      }\n\n      if (this.performMemoryCleanup) {\n        this.performMemoryCleanup();\n      }\n\n      // 清理所有缓存\n      this.cachedData = {};\n      this._formattedCache = null;\n    } catch (error) {\n    }\n  },\n  methods: {\n    toLogin() {\n      toLogin();\n    },\n\n    // 初始化页面数据\n    async initializePageData() {\n      try {\n        this.isLoading = true;\n\n        // 加载基础设置\n        this.loadFlowSettings();\n        this.getUserLocation();\n\n        // 加载页面内容\n        await this.loadData(true);\n\n      } catch (error) {\n        throw error;\n      } finally {\n        this.isLoading = false;\n      }\n    },\n\n    // 处理初始化错误\n    handleInitError() {\n      this.isEmpty = true;\n      this.loadStatus = 'more';\n\n      uni.showModal({\n        title: '加载失败',\n        content: '页面初始化失败，是否重试？',\n        confirmText: '重试',\n        cancelText: '取消',\n        success: (res) => {\n          if (res.confirm) {\n            this.initializePageData();\n          }\n        }\n      });\n    },\n\n    // 处理登录状态变化\n    handleLoginStateChange(isLoggedIn) {\n      if (isLoggedIn) {\n        // 登录后重新加载关注页数据\n        if (this.navIdx === 0) {\n          this.clearTabCache(0);\n          this.page = 1;\n          this.isEmpty = false;\n          this.loadStatus = 'loading';\n          this.loadData(true);\n        }\n      } else {\n        // 退出登录后清空关注页数据\n        if (this.navIdx === 0) {\n          this.list = [];\n          this.isEmpty = false;\n          this.loadStatus = 'more';\n        }\n      }\n    },\n\n    // 检查缓存过期\n    checkCacheExpiry() {\n      const now = Date.now();\n      const currentCache = this.cachedData[this.navIdx];\n\n      if (currentCache && currentCache.lastUpdate) {\n        const age = now - currentCache.lastUpdate;\n        if (age > this.cacheConfig.maxAge) {\n          // 缓存过期，静默刷新\n          this.refreshCurrentTab(true);\n        }\n      }\n    },\n\n    // 刷新当前tab\n    async refreshCurrentTab(silent = false) {\n      if (this.loadingStates.refresh && !silent) return;\n\n      try {\n        this.loadingStates.refresh = true;\n        this.page = 1;\n        await this.loadData(true);\n      } catch (error) {\n        if (!silent) {\n        }\n      } finally {\n        this.loadingStates.refresh = false;\n      }\n    },\n\n    // 清空指定tab缓存\n    clearTabCache(tabIndex) {\n      if (this.cachedData[tabIndex]) {\n        this.cachedData[tabIndex] = {\n          list: [],\n          page: 1,\n          hasMore: true,\n          lastUpdate: 0,\n          renderItems: 15\n        };\n      }\n    },\n    \n    getUserSocialData() {\n      if (!this.isLogin || !this.$store.state.app.token) {\n        this.currentMsg = 0;\n        this.userUid = 0;\n        return Promise.resolve();\n      }\n      \n      const userInfo = this.getUserInfoFromCache();\n      this.currentMsg = userInfo.service_num || 0;\n      this.userUid = this.userStore.uid || 0;\n      \n      return Promise.resolve(userInfo);\n    },\n    \n    handleLoginStateChanged(isLoggedIn) {\n      if (isLoggedIn) {\n        this.userUid = this.$store.state.app.uid || 0;\n        \n        const userInfo = this.getUserInfoFromCache();\n        if (userInfo) {\n          this.userCity = userInfo.residence_name || userInfo.city || '广州';\n          this.userAvatar = userInfo.avatar || '/static/img/avatar.png';\n          this.currentMsg = userInfo.service_num || 0;\n        }\n        \n        this.refreshFlowSettings()\n          .then(() => {\n            if (this.navIdx === 0) {\n              this.cachedData[0] = [];\n              this.page = 1;\n              this.isEmpty = false;\n              this.loadStatus = 'loading';\n              this.loadData(true);\n            }\n          })\n          .catch(() => {});\n      } else if (!isLoggedIn && this.navIdx === 0) {\n        this.list = [];\n        this.isEmpty = false;\n        this.isWaterfall = false;\n        this.currentMsg = 0;\n      }\n    },\n    \n    calcContentHeight() {\n      const windowHeight = uni.getSystemInfoSync().windowHeight\n      const navHeight = this.statusBarHeight + this.titleBarHeight\n      this.contentHeight = windowHeight - navHeight\n    },\n    \n    async swiperChange(e) {\n      const current = e.detail.current;\n\n      // 避免重复处理相同页签\n      if (this.navIdx === current || this.isSwitching) return;\n\n      try {\n        this.isSwitching = true;\n\n        // 缓存当前页签数据\n        this.saveCurrentTabData();\n\n        // 更新当前页签索引\n        this.navIdx = current;\n\n        // 加载瀑布流设置\n        this.loadFlowSettings();\n\n        const isLoggedIn = this.isLogin && this.$store.state.app.token;\n\n        // 关注页面未登录特殊处理\n        if (current === 0 && !isLoggedIn) {\n          this.list = [];\n          this.isEmpty = false;\n          this.loadStatus = 'more';\n          return;\n        }\n\n        // 更新用户信息\n        if (isLoggedIn) {\n          this.initUserInfoFromCache();\n        }\n\n        // 推荐页只首次加载圈子\n        if (current === 1 && !this.circleLoaded) {\n          this.getHotCircles();\n          this.circleLoaded = true;\n        }\n\n        // 尝试从缓存加载数据\n        const cachedData = this.getCachedData();\n        if (cachedData && cachedData.list.length > 0) {\n          this.list = cachedData.list;\n          this.page = cachedData.page;\n          this.totalCount = cachedData.totalCount || 0;\n          this.isEmpty = false;\n          this.loadStatus = cachedData.hasMore ? 'more' : 'noMore';\n          // 恢复渲染数量\n          this.renderItems = cachedData.renderItems || this.list.length;\n\n          // 检查缓存是否需要刷新\n          const now = Date.now();\n          const age = now - cachedData.lastUpdate;\n          if (age > this.cacheConfig.maxAge / 2) {\n            this.refreshCurrentTab(true);\n          }\n        } else {\n          // 没有缓存，重新加载\n          this.page = 1;\n          this.isEmpty = false;\n          this.loadStatus = 'loading';\n          this.list = [];\n          this.renderItems = 15; // 重置渲染数量\n          await this.loadData(true);\n        }\n\n      } catch (error) {\n        this.handleTabSwitchError();\n      } finally {\n        this.isSwitching = false;\n      }\n    },\n\n    // 保存当前tab数据\n    saveCurrentTabData() {\n      if (this.list.length > 0) {\n        const cache = this.cachedData[this.navIdx];\n        cache.list = [...this.list];\n        cache.page = this.page;\n        cache.hasMore = this.loadStatus !== 'noMore';\n        cache.lastUpdate = Date.now();\n        cache.renderItems = this.renderItems; // 保存当前渲染数量\n        cache.totalCount = this.totalCount; // 保存总数据量\n      }\n    },\n\n    // 处理tab切换错误\n    handleTabSwitchError() {\n      this.list = [];\n      this.isEmpty = true;\n      this.loadStatus = 'more';\n\n      uni.showToast({\n        title: '切换失败，请重试',\n        icon: 'none',\n        duration: 2000\n      });\n    },\n\n    onScroll() {\n      // 使用 requestAnimationFrame 优化滚动性能\n      if (this.scrollRAF) return;\n\n      this.scrollRAF = requestAnimationFrame(() => {\n        this.checkInViewItems();\n        this.scrollRAF = null;\n      });\n    },\n\n    checkInViewItems() {\n      if (!this.list || this.list.length === 0) return;\n\n      // 只有当需要加载更多时才处理\n      if (this.list.length > this.renderItems) {\n        const oldRenderItems = this.renderItems;\n        this.renderItems = Math.min(this.list.length, this.renderItems + this.renderStep);\n\n        // 避免不必要的强制更新，让 Vue 的响应式系统处理\n        if (oldRenderItems !== this.renderItems) {\n          // 清除格式化缓存，让计算属性重新计算\n          this._formattedCache = null;\n        }\n      }\n    },\n    \n\n    \n    cancelAllRequests() {\n      this.requestQueue = []\n    },\n    \n    // 优化的数据加载方法 - 提升上拉加载流畅度\n    async loadData(force = false, isLoadMore = false) {\n      // 防止重复加载\n      if (!force && (this.isLoading || this.isSwitching)) {\n        return Promise.resolve(null);\n      }\n\n      const isLoggedIn = this.isLogin && this.$store.state.app.token;\n\n      // 关注页面未登录特殊处理\n      if (this.navIdx === 0 && !isLoggedIn) {\n        this.list = [];\n        this.isEmpty = false;\n        this.loadStatus = 'more';\n        return Promise.resolve(null);\n      }\n\n      try {\n        // 优化加载状态设置 - 上拉加载时不显示loading\n        if (this.page === 1 && !isLoadMore) {\n          this.isRefreshing = true;\n          if (!this.isLoading) {\n            this.showLoadingWithMessage();\n          }\n        }\n\n        // 上拉加载时不重复设置loading状态\n        if (!isLoadMore) {\n          this.loadStatus = 'loading';\n        }\n\n        // 检查缓存 - 只在首次加载时使用\n        const cachedData = this.getCachedData();\n        if (cachedData && !force && this.page === 1 && !isLoadMore) {\n          this.list = cachedData.list;\n          this.totalCount = cachedData.totalCount || 0;\n          this.isEmpty = cachedData.list.length === 0;\n          this.loadStatus = cachedData.hasMore ? 'more' : 'noMore';\n          // 恢复渲染数量\n          this.renderItems = cachedData.renderItems || this.list.length;\n          return Promise.resolve(cachedData);\n        }\n\n        // 优化瀑布流设置加载 - 上拉加载时跳过\n        if (isLoggedIn && !isLoadMore) {\n          this.refreshFlowSettings();\n        }\n\n        // 添加防抖机制 - 提升流畅度\n        if (this.requestTimers.loadData) {\n          clearTimeout(this.requestTimers.loadData);\n        }\n\n        // 上拉加载时减少延迟，提升响应速度\n        const delay = isLoadMore ? 50 : 100;\n        await new Promise(resolve => {\n          this.requestTimers.loadData = setTimeout(resolve, delay);\n        });\n\n        // 根据当前tab加载数据\n        let result;\n        if (this.navIdx === 0) {\n          // 关注页面\n          const userInfo = this.getUserInfoFromCache();\n          this.userUid = userInfo.uid || 0;\n          result = await this.dynamicFollow('follow', force);\n        } else if (this.navIdx === 1) {\n          // 推荐页面\n          if (!this.circleLoaded) {\n            this.getHotCircles();\n            this.circleLoaded = true;\n          }\n          this.userUid = isLoggedIn ? (this.$store.state.app.uid || 0) : 0;\n          result = this.isWaterfall ?\n            await this.dynamicRecommendWaterfall(force) :\n            await this.dynamicRecommend(force);\n        } else if (this.navIdx === 2) {\n          // 附近页面\n          this.userUid = isLoggedIn ? (this.$store.state.app.uid || 0) : 0;\n          result = this.isWaterfall ?\n            await this.dynamicRecommendWaterfall(force) :\n            await this.dynamicFollow('nearby', force);\n        }\n\n        // 清除格式化缓存，确保使用新数据\n        this._formattedCache = null;\n\n        // 更新缓存\n        this.updateCache(result);\n        return result;\n\n      } catch (error) {\n        this.handleLoadError(error);\n        throw error;\n\n      } finally {\n        // 优化状态重置 - 上拉加载时避免不必要的操作\n        if (!isLoadMore) {\n          this.isRefreshing = false;\n          uni.hideLoading();\n        }\n      }\n    },\n\n    // 获取缓存数据\n    getCachedData() {\n      const cache = this.cachedData[this.navIdx];\n      if (!cache || !cache.list.length) return null;\n\n      const now = Date.now();\n      const age = now - cache.lastUpdate;\n\n      if (age > this.cacheConfig.maxAge) {\n        return null; // 缓存过期\n      }\n\n      return cache;\n    },\n\n    // 更新缓存\n    updateCache(data) {\n      if (!data) return;\n\n      const cache = this.cachedData[this.navIdx];\n      cache.lastUpdate = Date.now();\n\n      if (this.page === 1) {\n        cache.list = [...this.list];\n        cache.page = 1;\n      } else {\n        cache.list = [...this.list];\n        cache.page = this.page;\n      }\n\n      cache.hasMore = this.loadStatus !== 'noMore';\n      cache.renderItems = this.renderItems; // 更新渲染数量\n\n      // 限制缓存大小\n      if (cache.list.length > this.cacheConfig.maxItems) {\n        cache.list = cache.list.slice(0, this.cacheConfig.maxItems);\n        cache.renderItems = Math.min(cache.renderItems, cache.list.length);\n      }\n    },\n\n    // 增强的错误处理\n    handleLoadError(error) {\n      this.loadStatus = 'more';\n\n      if (this.page === 1) {\n        this.isEmpty = true;\n        this.list = [];\n      }\n\n      // 根据错误类型显示不同的提示\n      const errorMessage = this.getErrorMessage(error);\n\n      // 显示错误提示\n      if (error !== 'loadCancelled') {\n        setTimeout(() => {\n          uni.showModal({\n            title: '加载失败',\n            content: errorMessage,\n            confirmText: '重试',\n            cancelText: '取消',\n            success: (res) => {\n              if (res.confirm) {\n                this.retryLoad();\n              }\n            }\n          });\n        }, 300);\n      }\n    },\n\n    // 获取错误信息\n    getErrorMessage(error) {\n      // 如果是字符串，直接返回\n      if (typeof error === 'string') {\n        return error;\n      }\n\n      // 优先使用 msg 字段\n      if (error?.msg) {\n        return error.msg;\n      }\n\n      // 根据错误代码返回对应信息\n      if (error?.code) {\n        switch (error.code) {\n          case 'NETWORK_ERROR':\n            return '网络连接异常，请检查网络设置';\n          case 'TIMEOUT':\n            return '请求超时，请稍后重试';\n          case 'SERVER_ERROR':\n            return '服务器异常，请稍后重试';\n          default:\n            return '加载失败，请重试';\n        }\n      }\n\n      // 根据状态码返回信息\n      if (error?.status) {\n        if (error.status === 400) {\n          return error.msg || '请求参数错误';\n        } else if (error.status === 401) {\n          return '登录已过期，请重新登录';\n        } else if (error.status === 403) {\n          return '没有权限访问';\n        } else if (error.status === 404) {\n          return '请求的资源不存在';\n        } else if (error.status >= 500) {\n          return '服务器内部错误';\n        }\n      }\n\n      return error?.message || '无法获取数据，请检查网络连接';\n    },\n\n    // 重试加载\n    retryLoad() {\n      // 重置状态\n      this.resetLoadingState();\n\n      // 延迟重试，避免频繁请求\n      setTimeout(() => {\n        this.loadData(true).catch(err => {\n          uni.showToast({\n            title: '重试失败，请稍后再试',\n            icon: 'none',\n            duration: 2000\n          });\n        });\n      }, 500);\n    },\n    \n    likeClick(data) {\n      // 点赞回调处理 - 更新对应数据项的点赞状态\n\n      if (data && typeof data.index !== 'undefined' && data.id) {\n        // 在原始 list 数据中查找并更新对应项\n        const listItem = this.list.find(item => item.id === data.id);\n\n        if (listItem) {\n          // 更新点赞状态\n          listItem.is_like = data.isLike ? 1 : 0;\n\n          // 更新点赞数量\n          if (data.isLike) {\n            listItem.likes = (parseInt(listItem.likes) || 0) + 1;\n          } else {\n            listItem.likes = Math.max(0, (parseInt(listItem.likes) || 0) - 1);\n          }\n\n          // 标记该项需要重新格式化\n          listItem._formatted = false;\n\n          // 清除格式化缓存，让计算属性重新计算\n          this._formattedCache = null;\n\n          // 强制触发响应式更新\n          this.$forceUpdate();\n\n          // 延迟再次强制更新，确保组件接收到新数据\n          this.$nextTick(() => {\n            this.$forceUpdate();\n          });\n        }\n      }\n    },\n\n    followClick(data) {\n      // 关注回调处理 - 更新对应数据项的关注状态\n\n      if (data && data.uid) {\n        // 在原始 list 数据中查找并更新对应用户的所有动态\n        this.list.forEach(item => {\n          if (item.uid === data.uid) {\n            // 更新关注状态\n            if (item.user_info) {\n              item.user_info.is_follow = data.is_follow;\n              item.user_info.is_mutual_follow = data.is_mutual || 0;\n            }\n\n            // 如果有 user 字段也要更新\n            if (item.user) {\n              item.user.is_follow = data.is_follow;\n              item.user.is_mutual_follow = data.is_mutual || 0;\n            }\n          }\n        });\n\n        // 清除格式化缓存，让计算属性重新计算\n        this._formattedCache = null;\n\n        // 强制触发响应式更新\n        this.$forceUpdate();\n      }\n    },\n\n    waterfallLikeClick(data) {\n      // 瀑布流点赞回调处理 - 与普通点赞处理相同\n      this.likeClick(data);\n    },\n    \n    navigateToFun(e) {\n      if (!e || !e.currentTarget || !e.currentTarget.dataset || !e.currentTarget.dataset.url) {\n        return\n      }\n      \n      if (this._navigateLock) return\n      this._navigateLock = true\n      \n      const url = \"/pages/\" + e.currentTarget.dataset.url\n      \n      uni.navigateTo({\n        url: url,\n        complete: () => {\n          setTimeout(() => {\n            this._navigateLock = false\n          }, 500)\n        }\n      })\n    },\n    \n\n    \n    loadFlowSettings() {\n      try {\n        const userInfo = this.getUserInfoFromCache();\n        let flowSettings = userInfo.flow_settings;\n        \n        // 简化flowSettings解析逻辑\n        if (!flowSettings) {\n          flowSettings = { dynamicFlow: false, circleFlow: false };\n        } else if (typeof flowSettings === 'string') {\n          try {\n            let jsonStr = flowSettings.trim();\n            // 处理转义字符\n              jsonStr = jsonStr.replace(/\\\\\"/g, '\"');\n            // 移除外层引号\n            if ((jsonStr.startsWith('\"') && jsonStr.endsWith('\"')) || \n                (jsonStr.startsWith(\"'\") && jsonStr.endsWith(\"'\"))) {\n              jsonStr = jsonStr.slice(1, -1);\n            }\n            flowSettings = JSON.parse(jsonStr);\n          } catch (e) {\n            flowSettings = { dynamicFlow: false, circleFlow: false };\n          }\n        } else if (typeof flowSettings !== 'object' || flowSettings === null) {\n          flowSettings = { dynamicFlow: false, circleFlow: false };\n        }\n        \n        // 确保dynamicFlow属性存在\n        if (!flowSettings.hasOwnProperty('dynamicFlow')) {\n          flowSettings.dynamicFlow = false;\n        }\n        \n        const oldWaterfall = this.isWaterfall;\n        \n        // 关注页面不使用瀑布流，其他页面根据设置决定\n        if (this.navIdx === 0) {\n          // 关注页面始终不使用瀑布流\n          this.isWaterfall = false;\n        } else {\n          // 其他页面根据设置决定\n        this.isWaterfall = flowSettings.dynamicFlow === true;\n        }\n        \n        if (oldWaterfall !== this.isWaterfall) {\n          this.$forceUpdate();\n        }\n        \n        return flowSettings;\n      } catch (e) {\n        this.isWaterfall = false;\n        return { dynamicFlow: false, circleFlow: false };\n      }\n    },\n    \n    refreshFlowSettings() {\n      return new Promise((resolve) => {\n        try {\n          this.loadFlowSettings();\n          resolve(true);\n        } catch (error) {\n          this.isWaterfall = false;\n          resolve(false);\n        }\n      });\n    },\n    \n    dynamicRecommend(forceLoad = false) {\n      if (!forceLoad && this.isLoading) {\n        return Promise.reject('正在加载中');\n      }\n      \n      this.isLoading = true;\n      \n      const params = {\n        page: this.page,\n        limit: 10, // 添加limit参数\n        type: 'hot'\n      };\n      \n      return getDynamicList(params)\n        .then(res => {\n          uni.hideLoading();\n          this.isLoading = false;\n\n\n\n          if (res.status === 200) {\n            const list = res.data.list || [];\n\n            if (this.page === 1) {\n              this.list = list;\n            } else {\n              // 使用push.apply比扩展运算符更高效\n              this.list.push(...list);\n            }\n\n            // 更新总数据量\n            if (res.data.count !== undefined) {\n              this.totalCount = res.data.count;\n            }\n\n            // 优化加载状态判断\n            if (list.length === 0) {\n              // 当前页没有数据，说明没有更多了\n              this.loadStatus = 'noMore';\n            } else if (this.totalCount > 0 && this.list.length >= this.totalCount) {\n              // 已加载的数据量达到或超过总数\n              this.loadStatus = 'noMore';\n            } else if (list.length < 10) {\n              // 返回的数据量少于请求的数量，说明没有更多了\n              this.loadStatus = 'noMore';\n            } else {\n              // 还有更多数据\n              this.loadStatus = 'more';\n            }\n\n            this.isEmpty = list.length === 0 && this.page === 1;\n\n            // 确保renderItems足够显示所有数据\n            if (this.list.length > this.renderItems) {\n              this.renderItems = this.list.length;\n            }\n\n            this.renderMode();\n\n            return res.data;\n          } else {\n            this.handleApiError(res);\n            return Promise.reject(res.msg || '获取推荐失败');\n          }\n        })\n        .catch(err => {\n          this.handleApiError(err);\n          return Promise.reject(err);\n        });\n    },\n    \n    dynamicRecommendWaterfall(forceLoad = false) {\n      if (!forceLoad && this.isLoading) {\n        return Promise.reject('正在加载中');\n      }\n      \n      this.isLoading = true;\n      \n      const params = {\n        page: this.page,\n        limit: 10, // 添加limit参数\n        type: 'hot',\n        waterfall: 1\n      };\n      \n      return getDynamicList(params).then(res => {\n        uni.hideLoading();\n        this.isLoading = false;\n        \n        if (res.status === 200) {\n          const list = res.data.list || [];\n          \n          if (this.page === 1) {\n            this.list = list;\n          } else {\n            // 使用push.apply比扩展运算符更高效\n            this.list.push(...list);\n          }\n\n          // 更新总数据量\n          if (res.data.count !== undefined) {\n            this.totalCount = res.data.count;\n          }\n\n          // 优化加载状态判断\n          if (list.length === 0) {\n            // 当前页没有数据，说明没有更多了\n            this.loadStatus = 'noMore';\n          } else if (this.totalCount > 0 && this.list.length >= this.totalCount) {\n            // 已加载的数据量达到或超过总数\n            this.loadStatus = 'noMore';\n          } else if (list.length < 10) {\n            // 返回的数据量少于请求的数量，说明没有更多了\n            this.loadStatus = 'noMore';\n          } else {\n            // 还有更多数据\n            this.loadStatus = 'more';\n          }\n\n          this.isEmpty = list.length === 0 && this.page === 1;\n\n          // 确保renderItems足够显示所有数据\n          if (this.list.length > this.renderItems) {\n            this.renderItems = this.list.length;\n          }\n\n          this.renderMode();\n\n          return res.data;\n        } else {\n          this.loadStatus = 'more';\n          if (this.page === 1) {\n            this.isEmpty = true;\n            this.list = [];\n          }\n          \n          uni.showToast({\n            title: res.msg || '获取推荐失败',\n            icon: 'none',\n            duration: 2000\n          });\n          \n          return Promise.reject(res.msg || '获取瀑布流数据失败');\n        }\n      }).catch(err => {\n        uni.hideLoading();\n        this.isLoading = false;\n        \n        this.loadStatus = 'more';\n        if (this.page === 1) {\n          this.isEmpty = true;\n          this.list = [];\n        }\n        \n        uni.showToast({\n          title: '网络连接异常，请检查网络设置',\n          icon: 'none',\n          duration: 3000\n        });\n        \n        return Promise.reject(err);\n      });\n    },\n    \n    dynamicFollow(type = 'follow', forceLoad = false) {\n      // 关注页面已在loadData中处理了未登录状态，这里不需要再次判断\n      // 防止重复加载\n      if (!forceLoad && this.isLoading) {\n        return Promise.resolve(null); // 返回已解决的Promise，避免未捕获的拒绝\n      }\n      \n      this.isLoading = true;\n      \n      const params = {\n        page: this.page,\n        limit: 10, // 添加limit参数\n        type: type\n      };\n      \n      if (type === 'nearby') {\n        params.longitude = this.userLongitude || 113.26436;\n        params.latitude = this.userLatitude || 23.12908;\n      }\n      \n      return getDynamicList(params).then(res => {\n        // 确保在任何情况下都重置加载状态\n        this.isLoading = false;\n        uni.hideLoading();\n\n\n\n        if (res.status === 200) {\n          const list = res.data.list || [];\n\n          if (this.page === 1) {\n            this.list = list;\n          } else {\n            // 使用push.apply比扩展运算符更高效\n            this.list.push(...list);\n          }\n\n          // 更新总数据量\n          if (res.data.count !== undefined) {\n            this.totalCount = res.data.count;\n          }\n\n          // 优化加载状态判断\n          if (list.length === 0) {\n            // 当前页没有数据，说明没有更多了\n            this.loadStatus = 'noMore';\n          } else if (this.totalCount > 0 && this.list.length >= this.totalCount) {\n            // 已加载的数据量达到或超过总数\n            this.loadStatus = 'noMore';\n          } else if (list.length < 10) {\n            // 返回的数据量少于请求的数量，说明没有更多了\n            this.loadStatus = 'noMore';\n          } else {\n            // 还有更多数据\n            this.loadStatus = 'more';\n          }\n\n          this.isEmpty = list.length === 0 && this.page === 1;\n\n          // 确保renderItems足够显示所有数据\n          if (this.list.length > this.renderItems) {\n            this.renderItems = this.list.length;\n          }\n\n          this.renderMode();\n\n          return res.data;\n        } else {\n          this.loadStatus = 'more';\n          if (this.page === 1) {\n            this.isEmpty = true;\n            this.list = [];\n          }\n          \n          uni.showToast({\n            title: res.msg || '获取动态失败',\n            icon: 'none',\n            duration: 2000\n          });\n          \n          return Promise.reject(res.msg || '获取动态失败');\n        }\n      }).catch(err => {\n        // 确保在任何情况下都重置加载状态\n        this.isLoading = false;\n        uni.hideLoading();\n        \n        this.loadStatus = 'more';\n        if (this.page === 1) {\n          this.isEmpty = true;\n          this.list = [];\n        }\n        \n        // 只在非取消加载的情况下显示错误提示\n        if (err !== 'loadCancelled') {\n        uni.showToast({\n          title: '网络连接异常，请检查网络设置',\n          icon: 'none',\n          duration: 3000\n        });\n        }\n        \n        return Promise.reject(err);\n      });\n    },\n    \n    renderMode() {\n      if (!this.renderItems || this.renderItems < 10) {\n        this.renderItems = 10;\n      }\n      if (!this.renderStep) {\n        this.renderStep = 5;\n      }\n      \n      if (!this.list || this.list.length === 0) {\n        return;\n      }\n      \n      this.renderItems = Math.min(this.list.length, this.renderItems);\n    },\n    \n    onReachEnd() {\n      // 使用节流避免频繁触发\n      if (this._reachEndThrottle) return;\n      this._reachEndThrottle = true;\n\n      setTimeout(() => {\n        this._reachEndThrottle = false;\n      }, 300);\n\n      // 防止重复触发或在已无更多数据时加载\n      if (this.isLoading || this.isRefreshing || this.loadStatus === 'noMore') {\n        return;\n      }\n\n      const isLoggedIn = this.isLogin && this.$store.state.app.token;\n\n      // 关注页面未登录不加载\n      if (this.navIdx === 0 && !isLoggedIn) {\n        return;\n      }\n\n      // 检查是否需要预加载\n      const remainingItems = this.list.length - this.renderItems;\n      if (remainingItems > this.cacheConfig.preloadThreshold) {\n        return;\n      }\n\n      // 确保用户ID正确\n      if (isLoggedIn && !this.userUid) {\n        this.initUserInfoFromCache();\n      }\n\n      // 更新页码和状态\n      const currentPage = this.page;\n      this.page++;\n      this.loadStatus = 'loading';\n\n      // 异步加载更多数据，避免阻塞UI\n      this.$nextTick(() => {\n        this.loadData(true).catch(error => {\n\n          // 恢复页码和状态\n          this.page = currentPage;\n          this.loadStatus = 'more';\n\n          // 显示错误提示\n          uni.showToast({\n            title: '加载失败，请重试',\n            icon: 'none',\n            duration: 2000\n          });\n        });\n      });\n    },\n\n    // 显示错误提示\n    showErrorToast(message, duration = 2000) {\n      uni.showToast({\n        title: message,\n        icon: 'none',\n        duration: duration\n      });\n    },\n\n    formatDynamicData(data) {\n      // 快速检查，避免重复格式化\n      if (!data || typeof data !== 'object' || data._formatted) {\n        return data;\n      }\n\n      // 使用浅拷贝减少内存开销\n      const result = Object.assign({}, data);\n\n      // 批量设置基础属性，减少属性访问次数\n      const id = result.id || Date.now() + Math.floor(Math.random() * 1000);\n      const type = this.determineContentType(result);\n      const user = this.formatUserInfo(result);\n      const user_id = result.uid || result.user_id || user?.id || 0;\n\n      // 一次性设置所有属性\n      Object.assign(result, {\n        id,\n        type,\n        user,\n        user_id,\n        uid: user_id,\n        like_count: result.like_count || result.likes || 0,\n        likes: result.likes || result.like_count || 0,\n        comment_count: result.comment_count || result.comments || 0,\n        create_time_str: result.create_time_str || result.time_str || result.create_time || '刚刚',\n        province: result.province || '',\n        browse: result.browse || result.view_count || result.views || 0,\n        is_like: result.is_like ? 1 : 0\n      });\n\n      // 设置字符串格式\n      result.like_count_str = String(result.like_count);\n      result.comment_count_str = String(result.comment_count);\n\n      // 格式化媒体内容\n      this.formatMediaContent(result);\n\n      // 格式化位置和其他信息\n      if (result.adds_name) {\n        result.lat = result.lat || 0;\n        result.lng = result.lng || 0;\n      }\n\n      result.circle_id = result.circle_id || 0;\n      result.activity_id = result.activity_id || 0;\n      result.order_id = result.order_id || 0;\n\n      // 格式化评论信息\n      if (result.comment && typeof result.comment === 'object') {\n        if (!result.comment.user_name && result.comment.user) {\n          result.comment.user_name = result.comment.user.name || '';\n        }\n      }\n\n      result._formatted = true;\n      return result;\n    },\n\n    // 确定内容类型\n    determineContentType(data) {\n      if (data.type !== undefined) {\n        return data.type;\n      }\n\n      if (data.video || data.video_url) {\n        return 3; // 视频\n      } else if (data.audio) {\n        return 4; // 音频\n      } else if ((data.images && Array.isArray(data.images) && data.images.length > 0) ||\n                (data.imgs && Array.isArray(data.imgs) && data.imgs.length > 0)) {\n        return 2; // 图片\n      } else {\n        return 1; // 文本\n      }\n    },\n\n    // 格式化用户信息\n    formatUserInfo(data) {\n      if (data.user) {\n        return data.user;\n      }\n\n      return {\n        id: data.uid || data.user_id || 0,\n        avatar: data.user_info?.avatar || data.user_avatar || '/static/img/avatar.png',\n        name: data.user_info?.nickname || data.user_name || '用户',\n        gender: data.user_gender || 1,\n        age: data.user_age || ''\n      };\n    },\n\n    // 格式化媒体内容\n    formatMediaContent(result) {\n      if (result.type === 2) {\n        this.formatImageContent(result);\n      } else if (result.type === 3) {\n        this.formatVideoContent(result);\n      } else if (result.type === 4) {\n        this.formatAudioContent(result);\n      }\n    },\n\n    // 格式化图片内容\n    formatImageContent(result) {\n      if (!result.imgs || !Array.isArray(result.imgs)) {\n        result.imgs = [];\n\n        if (result.images && Array.isArray(result.images)) {\n          result.imgs = result.images.map(url => {\n            return typeof url === 'string' ? { url } : url;\n          });\n        }\n\n        if (result.img && result.img.url) {\n          const found = result.imgs.some(img => img.url === result.img.url);\n          if (!found) {\n            result.imgs.unshift(result.img);\n          }\n        }\n      }\n\n      result.imgs = result.imgs.map(img => {\n        if (typeof img === 'string') {\n          return { url: img, wide: 800, high: 600 };\n        }\n        return {\n          url: img.url || '',\n          wide: img.wide || 800,\n          high: img.high || 600\n        };\n      });\n\n      result.img_count = result.imgs.length;\n    },\n\n    // 格式化视频内容\n    formatVideoContent(result) {\n      if (!result.video || typeof result.video === 'string') {\n        result.video = {\n          url: result.video || result.video_url || '',\n          cover: result.video_cover || '',\n          wide: result.video_width || 720,\n          high: result.video_height || 1280\n        };\n      }\n    },\n\n    // 格式化音频内容\n    formatAudioContent(result) {\n      if (!result.audio) {\n        result.audio = {\n          name: result.audio_name || '未知音频',\n          intro: result.audio_intro || '暂无简介',\n          cover: result.audio_cover || '/static/img/default_bg.jpg'\n        };\n      }\n    },\n    touchStart(e) {\n      this.touchStartY = e.touches[0].pageY;\n    },\n\n    touchMove(e) {\n      // 使用 passive 事件监听器优化性能\n      e.stopPropagation();\n    },\n    navClick(e) {\n      if (!e?.currentTarget?.dataset) return;\n\n      const index = parseInt(e.currentTarget.dataset.idx || 0);\n\n      // 避免重复处理相同页签\n      if (this.navIdx === index || this.isSwitching) return;\n\n      this.switchToTab(index);\n    },\n\n    // 切换到指定标签页\n    async switchToTab(index) {\n      try {\n        this.isSwitching = true;\n\n        // 缓存当前数据\n        this.saveCurrentTabData();\n\n        // 更新页签索引\n        this.navIdx = index;\n\n        // 清除格式化缓存\n        this._formattedCache = null;\n\n        // 重置页面状态\n        this.resetTabState();\n\n        // 关注页面未登录特殊处理\n        if (index === 0 && !this.isLogin) {\n          this.isEmpty = false;\n          this.loadStatus = 'more';\n          return;\n        }\n\n        // 推荐页只首次加载圈子\n        if (index === 1 && !this.circleLoaded) {\n          this.getHotCircles();\n          this.circleLoaded = true;\n        }\n\n        // 加载设置和数据\n        this.loadFlowSettings();\n        await this.loadData(true);\n\n      } catch (error) {\n      } finally {\n        this.isSwitching = false;\n      }\n    },\n\n    // 重置标签页状态\n    resetTabState() {\n      this.page = 1;\n      this.isEmpty = false;\n      this.loadStatus = 'loading';\n      this.list = [];\n      this.renderItems = 15; // 重置渲染数量\n    },\n    \n    clearCache(tabIndex = null) {\n      if (tabIndex !== null && typeof tabIndex === 'number') {\n        this.cachedData[tabIndex] = {\n          list: [],\n          page: 1,\n          hasMore: true,\n          lastUpdate: 0,\n          renderItems: 15\n        };\n      } else {\n        this.cachedData = {\n          0: { list: [], page: 1, hasMore: true, lastUpdate: 0, renderItems: 15 },\n          1: { list: [], page: 1, hasMore: true, lastUpdate: 0, renderItems: 15 },\n          2: { list: [], page: 1, hasMore: true, lastUpdate: 0, renderItems: 15 }\n        };\n      }\n    },\n    \n    // 处理用户信息更新\n    handleUserInfoUpdate() {\n      // 重新加载瀑布流设置\n      this.loadFlowSettings();\n      \n      // 如果设置发生变化，可能需要刷新数据\n      if (this.list.length === 0) {\n        this.loadData(true);\n      }\n    },\n    \n    // 简化的处理瀑布流设置更新\n    handleFlowSettingsUpdate(settings) {\n      // 保存当前瀑布流状态\n      const oldWaterfall = this.isWaterfall;\n      \n      // 关注页面不使用瀑布流，其他页面根据设置决定\n      if (this.navIdx === 0) {\n        this.isWaterfall = false;\n      } else {\n      this.isWaterfall = settings.dynamicFlow === true;\n      }\n      \n      // 如果设置发生变化，刷新数据\n      if (oldWaterfall !== this.isWaterfall) {\n        // 清除当前标签页缓存\n        this.clearTabCache(this.navIdx);\n\n        // 重置加载状态\n        this.resetLoadingState();\n\n        // 重新加载数据\n        this.page = 1;\n        this.renderItems = 15; // 重置渲染数量\n        this.loadData(true).catch(err => {\n        });\n      }\n    },\n    // 添加检查微信环境的方法\n    // #ifdef H5\n    checkWeixinEnvironment() {\n      const ua = navigator.userAgent.toLowerCase();\n      this.isWeixin = ua.indexOf('micromessenger') !== -1;\n    },\n    // #endif\n    // 获取并解析用户信息缓存的统一方法\n    getUserInfoFromCache() {\n      let userInfo = uni.getStorageSync('USER_INFO') || {};\n      \n      // 如果缓存返回的是字符串，先解析为对象\n      if (typeof userInfo === 'string') {\n        try {\n          userInfo = JSON.parse(userInfo);\n        } catch (e) {\n          userInfo = {};\n        }\n      }\n      \n      return userInfo;\n    },\n    \n    // 添加一个新方法，用于从缓存初始化用户信息\n    initUserInfoFromCache() {\n      // 统一从Vuex获取用户ID\n      this.userUid = this.$store.state.app.uid || 0;\n      \n      // 设置其他用户信息\n      const userInfo = this.getUserInfoFromCache();\n      this.userCity = userInfo.residence_name || userInfo.city || '广州';\n      this.userAvatar = userInfo.avatar || '/static/img/avatar.png';\n      this.currentMsg = userInfo.service_num || 0;\n      \n      return userInfo;\n    },\n    \n    // 添加刷新用户信息的方法\n    refreshUserInfo() {\n      const userInfo = this.getUserInfoFromCache();\n      this.userAvatar = userInfo.avatar || '/static/img/avatar.png';\n      this.userCity = userInfo.residence_name || userInfo.city || '广州';\n      this.userUid = this.$store.state.app.uid || 0;\n      this.currentMsg = userInfo.service_num || 0;\n    },\n    // 重置加载状态\n    resetLoadingState() {\n      this.isLoading = false;\n      this.isRefreshing = false;\n      this.isSwitching = false;\n      uni.hideLoading();\n    },\n\n    // 显示带消息的加载提示\n    showLoadingWithMessage() {\n      const messages = [\n        '加载中...',\n        '获取最新内容...',\n        '正在为您推荐...',\n        '加载精彩内容...'\n      ];\n\n      const randomMessage = messages[Math.floor(Math.random() * messages.length)];\n\n      uni.showLoading({\n        title: randomMessage,\n        mask: true\n      });\n    },\n\n    // 显示成功提示\n    showSuccessToast(message = '操作成功') {\n      uni.showToast({\n        title: message,\n        icon: 'success',\n        duration: 1500\n      });\n    },\n\n    // 显示错误提示\n    showErrorToast(message = '操作失败') {\n      uni.showToast({\n        title: message,\n        icon: 'none',\n        duration: 2000\n      });\n    },\n\n    // 显示网络错误提示\n    showNetworkError() {\n      uni.showModal({\n        title: '网络异常',\n        content: '网络连接不稳定，请检查网络设置后重试',\n        confirmText: '重试',\n        cancelText: '取消',\n        success: (res) => {\n          if (res.confirm) {\n            this.retryLoad();\n          }\n        }\n      });\n    },\n\n    // 初始化平台特性\n    initPlatformFeatures() {\n      // #ifdef H5\n      this.checkWeixinEnvironment();\n      this.initH5Features();\n      // #endif\n\n      // #ifdef MP\n      this.initMiniProgramFeatures();\n      // #endif\n\n      // #ifdef APP-PLUS\n      this.initAppFeatures();\n      // #endif\n    },\n\n    // #ifdef H5\n    initH5Features() {\n      // 监听窗口大小变化\n      window.addEventListener('resize', this.calcContentHeight);\n\n      // 监听网络状态变化\n      window.addEventListener('online', this.handleNetworkChange);\n      window.addEventListener('offline', this.handleNetworkChange);\n\n      // 优化H5滚动性能\n      this.optimizeH5Scroll();\n    },\n\n    optimizeH5Scroll() {\n      // 添加滚动优化\n      const style = document.createElement('style');\n      style.textContent = `\n        .content-scroll {\n          -webkit-overflow-scrolling: touch;\n          scroll-behavior: smooth;\n        }\n      `;\n      document.head.appendChild(style);\n    },\n\n    handleNetworkChange() {\n      // 检查 navigator 是否存在（H5 环境）\n      if (typeof navigator !== 'undefined' && typeof navigator.onLine !== 'undefined') {\n        if (navigator.onLine) {\n          this.showSuccessToast('网络已连接');\n          // 网络恢复时重新加载数据\n          if (this.isEmpty) {\n            this.retryLoad();\n          }\n        } else {\n          this.showErrorToast('网络连接断开');\n        }\n      }\n    },\n    // #endif\n\n    // #ifdef MP\n    initMiniProgramFeatures() {\n      // 小程序特有的初始化\n      this.optimizeMiniProgramPerformance();\n    },\n\n    optimizeMiniProgramPerformance() {\n      // 小程序性能优化\n      this.renderItems = Math.min(this.renderItems, 20); // 限制渲染数量\n      this.renderStep = Math.min(this.renderStep, 5);    // 减少渲染步长\n    },\n    // #endif\n\n    // #ifdef APP-PLUS\n    initAppFeatures() {\n      // APP特有的初始化\n      this.optimizeAppPerformance();\n    },\n\n    optimizeAppPerformance() {\n      // APP性能优化\n      this.renderItems = Math.min(this.renderItems, 30); // APP可以渲染更多\n      this.renderStep = Math.min(this.renderStep, 10);   // 更大的渲染步长\n    },\n    // #endif\n\n    // 清理平台特性\n    cleanupPlatformFeatures() {\n      // #ifdef H5\n      window.removeEventListener('resize', this.calcContentHeight);\n      window.removeEventListener('online', this.handleNetworkChange);\n      window.removeEventListener('offline', this.handleNetworkChange);\n      // #endif\n    },\n\n    // 清理定时器和内存\n    clearTimers() {\n      try {\n        if (this.debounceTimer) {\n          clearTimeout(this.debounceTimer);\n          this.debounceTimer = null;\n        }\n\n        if (this.scrollTimer) {\n          clearTimeout(this.scrollTimer);\n          this.scrollTimer = null;\n        }\n\n        if (this.scrollRAF) {\n          if (typeof cancelAnimationFrame !== 'undefined') {\n            cancelAnimationFrame(this.scrollRAF);\n          }\n          this.scrollRAF = null;\n        }\n      } catch (error) {\n      }\n    },\n\n    // 内存回收机制\n    performMemoryCleanup() {\n      try {\n        // 清理格式化缓存\n        this._formattedCache = null;\n\n        // 清理过期的缓存数据\n        if (this.cachedData && this.cacheConfig) {\n          const now = Date.now();\n          Object.keys(this.cachedData).forEach(key => {\n            const cache = this.cachedData[key];\n            if (cache && cache.lastUpdate && (now - cache.lastUpdate) > this.cacheConfig.maxAge * 2) {\n              // 清理过期缓存\n              cache.list = [];\n              cache.lastUpdate = 0;\n            }\n          });\n        }\n\n        // 强制垃圾回收（如果支持）\n        // #ifdef H5\n        if (typeof window !== 'undefined' && window.gc && typeof window.gc === 'function') {\n          try {\n            window.gc();\n          } catch (gcError) {\n          }\n        }\n        // #endif\n\n        // #ifdef APP-PLUS\n        if (typeof plus !== 'undefined' && plus.runtime && plus.runtime.gc) {\n          try {\n            plus.runtime.gc();\n          } catch (gcError) {\n          }\n        }\n        // #endif\n      } catch (error) {\n      }\n    },\n\n    // 获取热门圈子\n    getHotCircles() {\n      // 调用热门圈子接口\n      getHotCircles() // 移除固定的limit参数，使用接口默认值\n        .then(res => {\n          if (res.status === 200 && res.data) {\n            // 处理圈子数据，确保字段兼容\n            this.circle = res.data.map(item => {\n              return {\n                id: item.id,\n                circle_name: item.circle_name || item.name,\n                circle_avatar: item.circle_avatar || item.avatar,\n                name: item.circle_name || item.name, // 兼容字段\n                avatar: item.circle_avatar || item.avatar, // 兼容字段\n                is_hot: item.is_hot || false,\n                is_new: item.is_new || false,\n                dynamic_count: item.dynamic_count || 0,\n                member_count: item.member_count || 0,\n                user_count: item.member_count || 0, // 兼容字段\n                circle_description: item.circle_description || ''\n              };\n            });\n          } else {\n            this.circle = [];\n          }\n        })\n        .catch(() => {\n          this.circle = [];\n        });\n    },\n    \n    // 添加统一的API错误处理方法\n    handleApiError(err) {\n      // 隐藏加载提示\n      uni.hideLoading();\n      \n      // 重置加载状态\n      this.isLoading = false;\n      \n      // 处理错误\n      this.loadStatus = 'more';\n      if (this.page === 1) {\n        this.isEmpty = true;\n        this.list = [];\n      }\n      \n      // 显示错误提示\n      uni.showToast({\n        title: err.msg || '网络连接异常，请检查网络设置',\n        icon: 'none',\n        duration: 2000\n      });\n    },\n    \n    // 获取用户位置信息 - 参考order_confirm页面的实现\n    getUserLocation() {\n      // 先尝试从缓存获取\n      const cachedLatitude = uni.getStorageSync('user_latitude') || uni.getStorageSync('residence_lat');\n      const cachedLongitude = uni.getStorageSync('user_longitude') || uni.getStorageSync('residence_lng');\n\n      if (cachedLatitude && cachedLongitude) {\n        this.userLatitude = parseFloat(cachedLatitude);\n        this.userLongitude = parseFloat(cachedLongitude);\n\n        // 如果有缓存的地址信息，也获取一下\n        const cachedAddress = uni.getStorageSync('residence_name');\n        if (cachedAddress) {\n          this.userCity = cachedAddress;\n        } else {\n          // 根据经纬度获取地址\n          this.getAddressFromLocation(this.userLatitude, this.userLongitude);\n        }\n        return;\n      }\n\n      // 缓存中没有位置信息，尝试获取当前位置\n      this.requestLocation();\n    },\n\n    // 请求位置信息 - 参考order_confirm的实现方式\n    requestLocation() {\n      // #ifdef H5\n      // 检查是否在微信环境中\n      if (this.$wechat && this.$wechat.isWeixin()) {\n        // 微信环境下使用微信API获取位置\n        this.$wechat.location().then(res => {\n          this.handleLocationSuccess(res);\n        }).catch(err => {\n          console.log('微信定位失败:', err);\n          this.fallbackToUniLocation();\n        });\n      } else {\n        this.fallbackToUniLocation();\n      }\n      // #endif\n\n      // #ifndef H5\n      // 非H5环境直接使用uni.getLocation\n      this.fallbackToUniLocation();\n      // #endif\n    },\n\n    // 使用uni.getLocation获取位置\n    fallbackToUniLocation() {\n      uni.getLocation({\n        type: 'wgs84',\n        geocode: true, // 启用逆地理编码\n        success: (res) => {\n          this.handleLocationSuccess(res);\n        },\n        fail: (err) => {\n          console.log('uni.getLocation失败:', err);\n          this.handleLocationError(err);\n        }\n      });\n    },\n\n    // 处理定位成功\n    handleLocationSuccess(res) {\n      this.userLatitude = res.latitude;\n      this.userLongitude = res.longitude;\n\n      // 保存到缓存 - 使用order_confirm相同的key\n      uni.setStorageSync('user_latitude', res.latitude);\n      uni.setStorageSync('user_longitude', res.longitude);\n      // 同时保存到原有的key，保持兼容性\n      uni.setStorageSync('residence_lat', res.latitude);\n      uni.setStorageSync('residence_lng', res.longitude);\n\n      // 如果返回了地址信息，直接使用\n      if (res.address) {\n        this.userCity = res.address.city || res.address.district || res.address.province || '';\n        if (this.userCity) {\n          uni.setStorageSync('residence_name', this.userCity);\n        }\n      } else {\n        // 根据经纬度获取地址\n        this.getAddressFromLocation(res.latitude, res.longitude);\n      }\n    },\n\n    // 处理定位失败\n    handleLocationError(err) {\n      console.log('定位失败:', err);\n\n      // 根据错误类型给出不同提示\n      let errorMsg = '定位失败';\n      if (err.errMsg) {\n        if (err.errMsg.includes('auth deny')) {\n          errorMsg = '定位权限被拒绝，将使用默认位置';\n        } else if (err.errMsg.includes('timeout')) {\n          errorMsg = '定位超时，将使用默认位置';\n        } else if (err.errMsg.includes('fail')) {\n          errorMsg = '定位服务不可用，将使用默认位置';\n        }\n      }\n\n      // 显示提示（可选）\n      // uni.showToast({\n      //   title: errorMsg,\n      //   icon: 'none',\n      //   duration: 2000\n      // });\n\n      // 设置默认位置\n      this.setDefaultLocation();\n    },\n    \n    // 设置默认位置（广州）\n    setDefaultLocation() {\n      this.userLatitude = 23.12908; // 广州纬度\n      this.userLongitude = 113.26436; // 广州经度\n      this.userCity = \"广东省广州市\";\n      // 保存到缓存\n      uni.setStorageSync('residence_lat', this.userLatitude);\n      uni.setStorageSync('residence_lng', this.userLongitude);\n      uni.setStorageSync('residence_name', this.userCity);\n\n    },\n    \n    // 强制刷新位置信息\n    refreshLocation() {\n\n      // 清除缓存的位置信息\n      uni.removeStorageSync('residence_lat');\n      uni.removeStorageSync('residence_lng');\n      uni.removeStorageSync('residence_name');\n      // 重新获取位置\n      this.getUserLocation();\n    },\n    \n    // 根据经纬度获取地址信息（逆地理编码）\n    getAddressFromLocation(latitude, longitude) {\n\n      \n      // 使用腾讯地图逆地理编码API\n      uni.request({\n        url: 'https://apis.map.qq.com/ws/geocoder/v1/',\n        data: {\n          location: `${latitude},${longitude}`,\n          key: 'F7LBZ-NLU6D-6524Z-PK6ZQ-D47AJ-KRB2I', // 腾讯地图API key\n          get_poi: 0\n        },\n        success: (res) => {\n\n          if (res.data.status === 0 && res.data.result) {\n            const result = res.data.result;\n            const province = result.ad_info.province;\n            const city = result.ad_info.city;\n            const district = result.ad_info.district;\n            \n            // 组合地址信息\n            let fullAddress = '';\n            if (province && city) {\n              if (province === city) {\n                // 直辖市情况\n                fullAddress = province + (district || '');\n              } else {\n                // 普通省市情况\n                fullAddress = province + city + (district || '');\n              }\n            } else {\n              // 备用方案\n              fullAddress = result.address || '未知位置';\n            }\n            \n            // 更新地址信息\n            this.userCity = fullAddress;\n            \n            // 保存到缓存\n            uni.setStorageSync('residence_name', fullAddress);\n            \n            \n          } else {\n            \n            this.setFallbackAddress();\n          }\n        },\n        fail: () => {\n          this.setFallbackAddress();\n        }\n      });\n    },\n    \n    // 设置备用地址\n    setFallbackAddress() {\n      // 如果逆地理编码失败，使用默认地址\n      const defaultAddress = \"广东省广州市\";\n      this.userCity = defaultAddress;\n\n    },\n    // 处理标签更新\n    handleTagsUpdate({ tags, userId }) {\n      // 更新列表中对应用户的标签\n      if (this.displayItems && this.displayItems.length > 0) {\n        this.displayItems = this.displayItems.map(item => {\n          if (item.user_id === userId || item.id === userId) {\n            return {\n              ...item,\n              interest_tags: tags\n            };\n          }\n          return item;\n        });\n      }\n      \n      // 如果需要重新加载列表数据\n      this.loadData(true);\n    },\n    fetchList() {\n      this.page = 1;\n      this.loadData(true);\n    },\n    onCardUpdate({ vote_info, idx }) {\n      if (this.list[idx]) {\n        // Vue3兼容：直接赋值替代$set\n        this.list[idx].vote_info = vote_info;\n      }\n    },\n  },\n  computed: {\n    isLogin() {\n      return this.userStore.isLoggedIn;\n    },\n    // 优化后的显示数据计算，使用缓存避免重复格式化\n    displayItems() {\n      // 使用缓存的格式化数据，避免重复计算\n      if (!this._formattedCache || this._formattedCache.length !== this.list.length) {\n        this._formattedCache = this.list.map(item => {\n          if (!item._formatted) {\n            return this.formatDynamicData(item);\n          }\n          return item;\n        });\n      }\n\n      // 只返回当前渲染数量的项目，实现虚拟滚动\n      return this._formattedCache.slice(0, this.renderItems);\n    }\n  },\n  watch: {\n    // 监听isWaterfall变化，清除缓存让Vue自动重新渲染\n    isWaterfall() {\n      // 清除格式化缓存，让计算属性重新计算\n      this._formattedCache = null;\n    }\n  }\n}\n</script>\n\n<style>\n.nav-box{\n  position:fixed;\n  top:0;\n  width:100%;\n  z-index:99;\n  box-sizing:border-box;\n  background:#fff;\n  /* 添加硬件加速 */\n  transform: translateZ(0);\n  will-change: transform;\n}\n.nav-box .bar-box{\n  width:100%;\n  position:relative\n}\n.bar-box .bar-title{\n  padding:0 30rpx;\n  font-size:32rpx;\n  font-weight:700\n}\n.bar-box .bar-title image{\n  margin-left:10rpx;\n  width:15rpx;\n  height:15rpx;\n  transition:transform .3s ease-in-out\n}\n.bar-box .bar-search{\n  padding:0 20rpx;\n  width:180rpx;\n  height:calc(100% - 24rpx);\n  font-size:24rpx;\n  font-weight:700;\n  color:#999;\n  background:#f8f8f8;\n  border-radius:50rpx;\n  border:1px solid #f5f5f5\n}\n.bar-box .bar-search image{\n  margin-right:10rpx;\n  width:30rpx;\n  height:30rpx\n}\n\n.bar-box .bar-item{\n  position:absolute;\n  z-index:101;\n  left:30rpx;\n  padding:0 20rpx;\n  flex-direction:column;\n  justify-content:center;\n  background:#fff;\n  border-radius:24rpx;\n  box-shadow:0 0 24rpx rgba(0,0,0,.06);\n  transition:height .3s ease-in-out;\n  overflow:hidden\n}\n.bar-box .bar-item view{\n  padding:0 25rpx 0 15rpx;\n  width:140rpx;\n  height:70rpx;\n  font-size:26rpx;\n  font-weight:500;\n  border-radius:12rpx;\n  justify-content:space-between\n}\n.bar-box .bar-item image{\n  width:24rpx;\n  height:24rpx\n}\n.bar-box .bar-item view:hover{\n  background:#f8f8f8\n}\n.bar-mask{\n  position:fixed;\n  z-index:100;\n  top:0;\n  right:0;\n  bottom:0;\n  left:0;\n  width:100%;\n  height:100%\n}\n.content-box{\n  width:100%\n}\n\n.scroll-box{\n  width:100%;\n  white-space:nowrap;\n  overflow:hidden;\n  transition:height .45s ease-in-out\n}\n.content-box .circle-box{\n  width:100%;\n  display:flex;\n  padding:30rpx 10rpx\n}\n.circle-box .circle-item{\n  flex-shrink:0\n}\n.circle-item .circle-item-top{\n  margin:0 20rpx;\n  width:116rpx;\n  height:116rpx;\n  border-radius:50%;\n  background:#f8f8f8;\n  border:2rpx solid #f5f5f5;\n  position:relative\n}\n.circle-item-top image{\n  width:100%;\n  height:100%;\n  border-radius:50%\n}\n.circle-item-top .icon{\n  margin:34rpx;\n  width:48rpx;\n  height:48rpx\n}\n.circle-item-top .circle-item-tag{\n  position:absolute;\n  right:0;\n  bottom:0;\n  width:24rpx;\n  height:24rpx;\n  border-radius:50%;\n  border:6rpx solid #fff\n}\n.circle-item .circle-name{\n  margin:20rpx 0 10rpx;\n  width:160rpx;\n  color:#000;\n  font-weight:500;\n  font-size:24rpx;\n  line-height:24rpx;\n  text-align:center\n}\n.circle-item .circle-tips{\n  width:160rpx;\n  color:#999;\n  font-size:18rpx;\n  line-height:18rpx;\n  font-weight:300;\n  text-align:center\n}\n.circle-item .circle-all{\n  background:#f8f8f8\n}\n.content-box .home-title{\n  width:calc(100% - 60rpx);\n  padding:0 30rpx\n}\n.home-title .home-title-txt{\n  font-weight:700;\n  font-size:32rpx\n}\n.home-title .home-title-all{\n  margin-left:12rpx;\n  padding:0 12rpx;\n  height:40rpx;\n  line-height:40rpx;\n  font-size:16rpx;\n  font-weight:700;\n  color:#999;\n  background:#f8f8f8;\n  border-radius:8rpx\n}\n.home-activity-item .home-activity-img,\n.home-activity-item .home-activity-data{\n  width:275rpx;\n  height:220rpx;\n  border-radius:8rpx;\n  position:relative;\n  overflow:hidden\n}\n.home-activity-item .home-activity-data{\n  padding:0 30rpx 0 20rpx;\n  position:relative\n}\n.home-activity-img .home-activity-state{\n  position:absolute;\n  top:16rpx;\n  left:16rpx;\n  width:68rpx;\n  height:38rpx;\n  color:#fff;\n  font-size:16rpx;\n  font-weight:700;\n  background:rgba(0,0,0,.4);\n  border:1px solid rgba(255,255,255,.16);\n  border-radius:8rpx;\n  justify-content:center\n}\n.home-activity-data .home-activity-title{\n  font-size:28rpx;\n  line-height:28rpx;\n  font-weight:700;\n  padding-bottom:12rpx\n}\n.home-activity-data .home-activity-tag view{\n  width:calc(100% - 26rpx);\n  color:#999;\n  font-size:20rpx;\n  font-weight:500\n}\n.home-activity-data .home-cu-img-group{\n  margin:8rpx 0 16rpx 16rpx;\n  direction:ltr;\n  unicode-bidi:bidi-override;\n  display:inline-block\n}\n.home-cu-img-group .home-cu-img{\n  width:32rpx;\n  height:32rpx;\n  display:inline-flex;\n  position:relative;\n  margin-left:-16rpx;\n  border:2rpx solid #fff;\n  background:#eee;\n  vertical-align:middle;\n  border-radius:50%\n}\n.home-cu-img-group .home-cu-tit{\n  display:inline-flex;\n  margin-left:8rpx;\n  color:#999;\n  font-size:20rpx;\n  font-weight:500\n}\n.home-activity-data .home-activity-btn{\n  position:absolute;\n  bottom:0;\n  width:calc(100% - 50rpx);\n  height:60rpx;\n  font-size:20rpx;\n  font-weight:700;\n  color:#000;\n  background:#f8f8f8;\n  border-radius:8rpx;\n  justify-content:center\n}\n.content-box .dynamic-box{\n  width:calc(100% - 16rpx);\n  padding:8rpx 8rpx 180rpx\n}\n.df {\n  display: flex;\n  align-items: center;\n}\n.ohto {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n.empty-box {\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 3.125rem 0;\n}\n.empty-box image {\n  width: 320rpx;\n  height: 320rpx;\n  opacity: 0.8;\n}\n.empty-box .e1 {\n  margin-top: 50rpx;\n  font-size: 36rpx;\n  font-weight: 600;\n  color: #333;\n}\n.empty-box .e2 {\n  margin-top: 24rpx;\n  font-size: 28rpx;\n  color: #666;\n  line-height: 1.4;\n}\n.heio {\n  justify-content: center;\n}\n\n/* 添加新的导航样式 */\n.nav-scroll-box {\n  flex: 1;\n  overflow: hidden;\n}\n\n.nav-scroll {\n  white-space: nowrap;\n  width: 100%;\n}\n\n.nav-scroll-content {\n  display: flex;\n  padding: 0 20rpx;\n}\n\n.nav-item {\n  position: relative;\n  padding: 0 15rpx;\n  font-size: 32rpx;\n  display: inline-block;\n  margin-right: 30rpx;\n  /* 添加过渡效果 */\n  transition: all 0.3s ease;\n  text-align: center;\n}\n\n.nav-item.active {\n  font-weight: 700;\n  color: #000;\n  transform: scale(1.05);\n}\n\n.nav-item:not(.active) {\n  color: #999;\n}\n\n.active-line {\n  position: absolute;\n  bottom: -5rpx;\n  left: 50%;\n  transform: translateX(-50%);\n  width: 30rpx;\n  height: 6rpx;\n  background-color: #000;\n  border-radius: 3rpx;\n  /* 添加过渡效果 */\n  transition: all 0.3s ease;\n}\n\n.content-swiper {\n  width: 100%;\n  will-change: transform;\n  background: #fafafa;\n  /* 启用硬件加速 */\n  transform: translateZ(0);\n}\n\n.content-scroll {\n  height: 100%;\n  -webkit-overflow-scrolling: touch;\n  background: #ffffff;\n  /* 优化滚动性能 */\n  contain: layout style paint;\n  transform: translateZ(0);\n}\n\n/* 添加性能优化相关类 */\n.hardware-accelerated {\n  transform: translateZ(0);\n  will-change: transform;\n  backface-visibility: hidden;\n}\n\n/* 优化动画效果 */\n@keyframes fade-in {\n  from {\n    opacity: 0;\n    transform: translateY(30rpx) scale(0.95);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0) scale(1);\n  }\n}\n\n@keyframes slide-in-left {\n  from {\n    opacity: 0;\n    transform: translateX(-50rpx);\n  }\n  to {\n    opacity: 1;\n    transform: translateX(0);\n  }\n}\n\n@keyframes pulse {\n  0%, 100% {\n    transform: scale(1);\n  }\n  50% {\n    transform: scale(1.05);\n  }\n}\n\n.card-gg-item {\n  animation: fade-in 0.4s cubic-bezier(0.4, 0, 0.2, 1);\n  will-change: opacity, transform;\n  /* 优化渲染性能 */\n  contain: layout style paint;\n  transform: translateZ(0);\n}\n\n\n\n.circle-item {\n  animation: fade-in 0.5s ease-out;\n}\n\n.login-btn:hover {\n  animation: pulse 1s infinite;\n}\n\n/* 图片懒加载占位样式 */\n.lazy-image-placeholder {\n  background: #f0f0f0;\n}\n\n/* 虚拟列表相关样式 */\n.virtual-list-container {\n  position: relative;\n  width: 100%;\n}\n\n.virtual-list-item {\n  position: absolute;\n  width: 100%;\n  will-change: transform;\n}\n\n/* 提高渲染层级，减少重绘 */\n.content-box {\n  position: relative;\n  z-index: 1;\n  will-change: transform;\n}\n\n/* 优化图像渲染 */\nimage {\n  will-change: transform;\n}\n\n/* 添加页面切换动画 */\n.page-transition-enter-active,\n.page-transition-leave-active {\n  transition: opacity 0.3s;\n}\n.page-transition-enter,\n.page-transition-leave-to {\n  opacity: 0;\n}\n\n/* 响应式设计 */\n@media screen and (max-width: 750rpx) {\n  .nav-item {\n    font-size: 30rpx;\n    padding: 0 12rpx;\n    margin-right: 25rpx;\n  }\n\n  .empty-box {\n    padding: 120rpx 30rpx 100rpx;\n    margin: 30rpx 15rpx;\n  }\n\n  .empty-box image {\n    width: 280rpx;\n    height: 280rpx;\n  }\n\n  .login-btn {\n    width: 260rpx;\n    height: 80rpx;\n    line-height: 80rpx;\n    font-size: 30rpx;\n  }\n}\n\n/* 暗色模式支持 */\n@media (prefers-color-scheme: dark) {\n  .nav-box {\n    background: #1a1a1a;\n  }\n\n  .nav-item.active {\n    color: #fff;\n    background: rgba(255, 255, 255, 0.1);\n  }\n\n  .nav-item:not(.active) {\n    color: #ccc;\n  }\n\n  .content-swiper,\n  .content-scroll {\n    background: #121212;\n  }\n\n  .empty-box {\n    background: #1e1e1e;\n  }\n\n  .empty-box .e1 {\n    color: #fff;\n  }\n\n  .empty-box .e2 {\n    color: #aaa;\n  }\n}\n\n/* 调试信息样式 */\n.debug-info {\n  position: fixed;\n  top: 100rpx;\n  right: 10rpx;\n  background: rgba(0, 0, 0, 0.7);\n  color: #fff;\n  padding: 8rpx 16rpx;\n  font-size: 22rpx;\n  border-radius: 12rpx;\n  z-index: 999;\n  backdrop-filter: blur(10rpx);\n}\n\n.swiper-box {\n  flex: 1;\n  width: 100%;\n  height: 100%;\n  position: relative;\n}\n\n.swiper-item {\n  flex: 1;\n  flex-direction: row;\n  position: relative;\n  overflow: visible;\n}\n\n.scroll-v {\n  flex: 1;\n  flex-direction: column;\n  width: 100%;\n  height: 100%;\n  overflow: visible;\n}\n\n/* 修复页面高度和滚动问题 */\n.content-box {\n  width: 100%;\n  height: 100%;\n  overflow: visible;\n  position: relative;\n}\n\n/* 优化登录按钮样式 */\n.login-btn {\n  margin-top: 50rpx;\n  width: 280rpx;\n  height: 88rpx;\n  line-height: 88rpx;\n  background: linear-gradient(135deg, #000 0%, #333 100%);\n  color: #fff;\n  font-size: 32rpx;\n  font-weight: 600;\n  text-align: center;\n  border-radius: 44rpx;\n  box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.2);\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  margin-bottom: 20rpx;\n  padding: 0;\n  border: none;\n  position: relative;\n  overflow: hidden;\n}\n\n.login-btn::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);\n  transition: left 0.5s;\n}\n\n.login-btn:active {\n  transform: scale(0.95);\n  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.3);\n}\n\n.login-btn:active::before {\n  left: 100%;\n}\n\n.login-action {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  margin-top: 30rpx;\n  margin-bottom: 30rpx;\n}\n\n.login-tips {\n  font-size: 26rpx;\n  color: #888;\n  margin-top: 16rpx;\n  opacity: 0.8;\n}\n\n/* 圈子相关样式 */\n.scroll-box{\n  width:100%;\n  white-space:nowrap;\n  overflow:hidden;\n  transition:height .45s ease-in-out\n}\n\n.circle-box{\n  width:100%;\n  display:flex;\n  padding:30rpx 10rpx\n}\n\n.circle-box .circle-item{\n  flex-shrink:0\n}\n\n.circle-item .circle-item-top{\n  margin:0 20rpx;\n  width:120rpx;\n  height:120rpx;\n  border-radius:50%;\n  background: linear-gradient(135deg, #f8f8f8 0%, #f0f0f0 100%);\n  border:3rpx solid #fff;\n  position:relative;\n  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);\n  transition: all 0.3s ease;\n}\n\n.circle-item .circle-item-top:active {\n  transform: scale(0.95);\n  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.15);\n}\n\n.circle-item-top image{\n  width:100%;\n  height:100%;\n  border-radius:50%;\n  object-fit: cover;\n}\n\n.circle-item-top .icon{\n  margin:36rpx;\n  width:48rpx;\n  height:48rpx;\n  opacity: 0.7;\n}\n\n.circle-item-top .circle-item-tag{\n  position:absolute;\n  right:-2rpx;\n  bottom:-2rpx;\n  width:28rpx;\n  height:28rpx;\n  border-radius:50%;\n  border:4rpx solid #fff;\n  box-shadow: 0 2rpx 6rpx rgba(0,0,0,0.15);\n}\n\n.circle-item .circle-name{\n  margin:24rpx 0 12rpx;\n  width:160rpx;\n  color:#333;\n  font-weight:600;\n  font-size:26rpx;\n  line-height:28rpx;\n  text-align:center;\n}\n\n.circle-item .circle-tips{\n  width:160rpx;\n  color:#888;\n  font-size:20rpx;\n  line-height:22rpx;\n  font-weight:400;\n  text-align:center;\n  opacity: 0.8;\n}\n</style>\n", "import MiniProgramPage from 'Z:/WWW/shejiao/vue3//pages/index/dynamic.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "<PERSON><PERSON><PERSON><PERSON>", "getDynamicList", "getHotCircles"], "mappings": ";;;;;;AAoLA,MAAA,YAAA,MAAA;AACA,MAAA,cAAA,MAAA;AACA,MAAA,YAAA,MAAA;AACA,MAAA,SAAA,MAAA;AACA,MAAA,SAAA,MAAA;AAQA,MAAA,YAAA;AAAA;IAEI;AAAA;IAEA;AAAA,IACA;AAAA;;EAGF,OAAA;AACE,WAAA;AAAA;MAEE,iBAAA;AAAA,MACA,gBAAA;AAAA;;MAGA,SAAA;AAAA,MACA,cAAA;AAAA,MACA,eAAA;AAAA;MAKA,QAAA;AAAA,MACA,MAAA,CAAA;AAAA;AAAA;QAIE,GAAA,EAAA,MAAA,CAAA,GAAA,MAAA,GAAA,SAAA,MAAA,YAAA,GAAA,aAAA,IAAA,YAAA,EAAA;AAAA,QACA,GAAA,EAAA,MAAA,CAAA,GAAA,MAAA,GAAA,SAAA,MAAA,YAAA,GAAA,aAAA,IAAA,YAAA,EAAA;AAAA,QACA,GAAA,EAAA,MAAA,CAAA,GAAA,MAAA,GAAA,SAAA,MAAA,YAAA,GAAA,aAAA,IAAA,YAAA,EAAA;AAAA;MAGF,QAAA,CAAA;AAAA;AAAA;;MAEA,MAAA;AAAA,MACA,YAAA;AAAA;AAAA;MAEA,YAAA;AAAA,MACA,aAAA;AAAA,MACA,eAAA;AAAA;AAAA,MAGA,eAAA;AAAA;QAEE,UAAA;AAAA;;;MAKF,eAAA;AAAA;;;MAKA,WAAA;AAAA,MACA,cAAA;AAAA,MACA,aAAA;AAAA;AAAA,MAGA,aAAA;AAAA;MAEA,gBAAA;AAAA;AAAA,MAGA,YAAA;AAAA,MACA,iBAAA;AAAA,MACA,eAAA;AAAA;AAAA;;;;;;;;;MAUA,eAAA;AAAA,MACA,aAAA;AAAA,MACA,WAAA;AAAA;AAAA,MAGA,iBAAA;AAAA,MACA,iBAAA;AAAA;;MAIA,mBAAA;AAAA,IACF;AAAA;EAEF,UAAA;;;;;EAKA,gBAAA;AAAA;;;;;;AAaE,SAAA,aAAA;AACA,SAAA,kBAAA,KAAA;;MAIE,YAAA;AAAA;MAEA,WAAA,EAAA,UAAA,KAAA,YAAA,SAAA;AAAA,IACF,CAAA;AAGA,SAAA,UAAA,MAAA;AACE,WAAA,mBAAA,EAAA,MAAA,SAAA;;MAEA,CAAA;AAAA,IACF,CAAA;AAAA;EAEF,SAAA;AAEE,UAAA,QAAA,KAAA,UAAA;AACA,UAAA,aAAA,KAAA,WAAA;AAEA,QAAA,KAAA,oBAAA,YAAA;AACE,WAAA,kBAAA;AACA,WAAA,uBAAA,UAAA;AAAA,IACF;AAGA,QAAA,YAAA;AACE,YAAA,WAAA,KAAA;AACA,WAAA,aAAA,SAAA,eAAA;AAAA,IACF;;;MAOE,YAAA;AAAA;MAEA,WAAA,EAAA,UAAA,KAAA,YAAA,SAAA;AAAA,IACF,CAAA;AAAA;EAEF,oBAAA;;;AAGE,SAAA,UAAA;;AAGA,SAAA,SAAA,IAAA;AAEIA,oBAAAA,MAAA,UAAA;AAAA,QACE,OAAA;AAAA;;MAGF,CAAA;AAAA;AAGA,iBAAA,MAAA;;;MAGA,GAAA,GAAA;AAAA,IACF,CAAA;AAAA;EAEJ,gBAAA;AAGE,QAAA,KAAA,aAAA,KAAA,cAAA,YAAA,KAAA,eAAA,UAAA;AACE;AAAA,IACF;;AAMA,QAAA,KAAA,WAAA,KAAA,CAAA,YAAA;AACE;AAAA,IACF;AAIA,UAAA,cAAA,KAAA,KAAA,SAAA;AAIA,QAAA,aAAA;AACE,WAAA,cAAA,WAAA;;AAEA,YAAA,cAAA,KAAA;;AAGA,WAAA,SAAA,OAAA,IAAA,EAAA,MAAA,SAAA;AAEE,aAAA,OAAA;;;AAKE,eAAA,eAAA,gBAAA,GAAA;AAAA,QACF;AAAA,MACF,CAAA,EAAA,QAAA,MAAA;AACE,aAAA,cAAA,WAAA;AAAA,MACF,CAAA;AAAA,IACF,WAAA,KAAA,KAAA,UAAA,KAAA,cAAA,KAAA,KAAA,SAAA,GAAA;;IAEA;AAAA;EAEF,oBAAA;AACE,WAAA;AAAA,MACE,OAAA,KAAA,OAAA,MAAA,MAAA,CAAA,KAAA;AAAA;IAEF;AAAA;EAEF,kBAAA;AACE,WAAA;AAAA,MACE,OAAA,KAAA,OAAA,MAAA,MAAA,CAAA,KAAA;AAAA;IAEF;AAAA;;AAGA,QAAA;;;;MAKE;AAGA,UAAA,KAAA,yBAAA;AACE,aAAA,wBAAA;AAAA,MACF;;;MAKA;AAGA,UAAA,KAAA,aAAA;AACE,aAAA,YAAA;AAAA,MACF;;;MAIA;AAGA,WAAA,aAAA;;;IAGF;AAAA;EAEF,SAAA;AAAA,IACE,UAAA;AACEC,iBAAAA;;;;AAKA,UAAA;AACE,aAAA,YAAA;;;;;;MAWF,UAAA;AACE,aAAA,YAAA;AAAA,MACF;AAAA;;IAIF,kBAAA;AACE,WAAA,UAAA;;AAGAD,oBAAAA,MAAA,UAAA;AAAA;;QAGE,aAAA;AAAA,QACA,YAAA;AAAA,QACA,SAAA,CAAA,QAAA;AACE,cAAA,IAAA,SAAA;;UAEA;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;IAIF,uBAAA,YAAA;AACE,UAAA,YAAA;AAEE,YAAA,KAAA,WAAA,GAAA;AACE,eAAA,cAAA,CAAA;;AAEA,eAAA,UAAA;;AAEA,eAAA,SAAA,IAAA;AAAA,QACF;AAAA;AAGA,YAAA,KAAA,WAAA,GAAA;;AAEE,eAAA,UAAA;;QAEF;AAAA,MACF;AAAA;;IAIF,mBAAA;AACE,YAAA,MAAA,KAAA;;AAGA,UAAA,gBAAA,aAAA,YAAA;AACE,cAAA,MAAA,MAAA,aAAA;AACA,YAAA,MAAA,KAAA,YAAA,QAAA;;QAGA;AAAA,MACF;AAAA;;IAIF,MAAA,kBAAA,SAAA,OAAA;AACE,UAAA,KAAA,cAAA,WAAA,CAAA;AAAA;AAEA,UAAA;AACE,aAAA,cAAA,UAAA;;;;MAMF,UAAA;AACE,aAAA,cAAA,UAAA;AAAA,MACF;AAAA;;;;;UAOI,MAAA,CAAA;AAAA,UACA,MAAA;AAAA;;;;MAKJ;AAAA;IAGF,oBAAA;;AAEI,aAAA,aAAA;AACA,aAAA,UAAA;;MAEF;AAEA,YAAA,WAAA,KAAA;AACA,WAAA,aAAA,SAAA,eAAA;AACA,WAAA,UAAA,KAAA,UAAA,OAAA;AAEA,aAAA,QAAA,QAAA,QAAA;AAAA;IAGF,wBAAA,YAAA;AACE,UAAA,YAAA;AACE,aAAA,UAAA,KAAA,OAAA,MAAA,IAAA,OAAA;AAEA,cAAA,WAAA,KAAA;;;;AAIE,eAAA,aAAA,SAAA,eAAA;AAAA,QACF;;AAII,cAAA,KAAA,WAAA,GAAA;AACE,iBAAA,WAAA,CAAA,IAAA;;AAEA,iBAAA,UAAA;;AAEA,iBAAA,SAAA,IAAA;AAAA,UACF;AAAA,WAEF,MAAA,MAAA;AAAA,QAAA,CAAA;AAAA,MACJ,WAAA,CAAA,cAAA,KAAA,WAAA,GAAA;;AAEE,aAAA,UAAA;;AAEA,aAAA,aAAA;AAAA,MACF;AAAA;IAGF,oBAAA;;;AAGE,WAAA,gBAAA,eAAA;AAAA;IAGF,MAAA,aAAA,GAAA;;;;AAME,UAAA;AACE,aAAA,cAAA;;AAMA,aAAA,SAAA;;;;;AAUE,eAAA,UAAA;;AAEA;AAAA,QACF;AAGA,YAAA,YAAA;;QAEA;AAGA,YAAA,YAAA,KAAA,CAAA,KAAA,cAAA;AACE,eAAA,cAAA;;QAEF;AAGA,cAAA,aAAA,KAAA;AACA,YAAA,cAAA,WAAA,KAAA,SAAA,GAAA;;;AAGE,eAAA,aAAA,WAAA,cAAA;AACA,eAAA,UAAA;;;AAMA,gBAAA,MAAA,KAAA;AACA,gBAAA,MAAA,MAAA,WAAA;AACA,cAAA,MAAA,KAAA,YAAA,SAAA,GAAA;;UAEA;AAAA;;AAIA,eAAA,UAAA;;;;;QAKF;AAAA;;MAIF,UAAA;;MAEA;AAAA;;IAIF,qBAAA;;AAEI,cAAA,QAAA,KAAA,WAAA,KAAA,MAAA;;AAEA,cAAA,OAAA,KAAA;AACA,cAAA,UAAA,KAAA,eAAA;;AAEA,cAAA,cAAA,KAAA;AACA,cAAA,aAAA,KAAA;AAAA,MACF;AAAA;;;;AAMA,WAAA,UAAA;;AAGAA,oBAAAA,MAAA,UAAA;AAAA,QACE,OAAA;AAAA;;MAGF,CAAA;AAAA;;;;AAOA,WAAA,YAAA,sBAAA,MAAA;;AAEE,aAAA,YAAA;AAAA,MACF,CAAA;AAAA;IAGF,mBAAA;AACE,UAAA,CAAA,KAAA,QAAA,KAAA,KAAA,WAAA;AAAA;AAGA,UAAA,KAAA,KAAA,SAAA,KAAA,aAAA;AACE,cAAA,iBAAA,KAAA;AACA,aAAA,cAAA,KAAA,IAAA,KAAA,KAAA,QAAA,KAAA,cAAA,KAAA,UAAA;AAGA,YAAA,mBAAA,KAAA,aAAA;;QAGA;AAAA,MACF;AAAA;IAKF,oBAAA;AACE,WAAA,eAAA,CAAA;AAAA;;IAIF,MAAA,SAAA,QAAA,OAAA,aAAA,OAAA;;;MAIE;;AAKA,UAAA,KAAA,WAAA,KAAA,CAAA,YAAA;;AAEE,aAAA,UAAA;;;MAGF;AAEA,UAAA;AAEE,YAAA,KAAA,SAAA,KAAA,CAAA,YAAA;;AAEE,cAAA,CAAA,KAAA,WAAA;;UAEA;AAAA,QACF;AAGA,YAAA,CAAA,YAAA;;QAEA;AAGA,cAAA,aAAA,KAAA;;;AAGE,eAAA,aAAA,WAAA,cAAA;AACA,eAAA,UAAA,WAAA,KAAA,WAAA;;;AAIA,iBAAA,QAAA,QAAA,UAAA;AAAA,QACF;;;QAKA;AAGA,YAAA,KAAA,cAAA,UAAA;AACE,uBAAA,KAAA,cAAA,QAAA;AAAA,QACF;;;;QAMA,CAAA;AAGA,YAAA;AACA,YAAA,KAAA,WAAA,GAAA;AAEE,gBAAA,WAAA,KAAA;;;;;AAME,iBAAA,cAAA;;UAEF;;sCAGE,MAAA,KAAA,0BAAA,KAAA,IACA,MAAA,KAAA,iBAAA,KAAA;AAAA;;sCAKA,MAAA,KAAA,0BAAA,KAAA,IACA,MAAA,KAAA,cAAA,UAAA,KAAA;AAAA,QACJ;;;;;;;MAaF,UAAA;AAEE,YAAA,CAAA,YAAA;;AAEEA,wBAAA,MAAA,YAAA;AAAA,QACF;AAAA,MACF;AAAA;;IAIF,gBAAA;AACE,YAAA,QAAA,KAAA,WAAA,KAAA,MAAA;AACA,UAAA,CAAA,SAAA,CAAA,MAAA,KAAA;AAAA,eAAA;AAEA,YAAA,MAAA,KAAA;;AAGA,UAAA,MAAA,KAAA,YAAA,QAAA;AACE,eAAA;AAAA,MACF;;;;IAMF,YAAA,MAAA;AACE,UAAA,CAAA;AAAA;AAEA,YAAA,QAAA,KAAA,WAAA,KAAA,MAAA;;AAGA,UAAA,KAAA,SAAA,GAAA;;;;;AAKE,cAAA,OAAA,KAAA;AAAA,MACF;AAEA,YAAA,UAAA,KAAA,eAAA;AACA,YAAA,cAAA,KAAA;;;AAKE,cAAA,cAAA,KAAA,IAAA,MAAA,aAAA,MAAA,KAAA,MAAA;AAAA,MACF;AAAA;;;;AAOA,UAAA,KAAA,SAAA,GAAA;AACE,aAAA,UAAA;;MAEF;AAGA,YAAA,eAAA,KAAA,gBAAA,KAAA;;AAIE,mBAAA,MAAA;AACEA,wBAAAA,MAAA,UAAA;AAAA;YAEE,SAAA;AAAA,YACA,aAAA;AAAA,YACA,YAAA;AAAA,YACA,SAAA,CAAA,QAAA;AACE,kBAAA,IAAA,SAAA;AACE,qBAAA,UAAA;AAAA,cACF;AAAA,YACF;AAAA,UACF,CAAA;AAAA,QACF,GAAA,GAAA;AAAA,MACF;AAAA;;;;;MAQA;AAGA,UAAA,+BAAA,KAAA;AACE,eAAA,MAAA;AAAA,MACF;AAGA,UAAA,+BAAA,MAAA;AACE,gBAAA,MAAA,MAAA;AAAA,UACE,KAAA;;;AAGE,mBAAA;AAAA,UACF,KAAA;AACE,mBAAA;AAAA,UACF;AACE,mBAAA;AAAA,QACJ;AAAA,MACF;AAGA,UAAA,+BAAA,QAAA;;;;AAII,iBAAA;AAAA;AAEA,iBAAA;AAAA;AAEA,iBAAA;AAAA;AAEA,iBAAA;AAAA,QACF;AAAA,MACF;AAEA,cAAA,+BAAA,YAAA;AAAA;;;;AASA,iBAAA,MAAA;AACE,aAAA,SAAA,IAAA,EAAA,MAAA,SAAA;AACEA,wBAAAA,MAAA,UAAA;AAAA,YACE,OAAA;AAAA;;UAGF,CAAA;AAAA,QACF,CAAA;AAAA,MACF,GAAA,GAAA;AAAA;IAGF,UAAA,MAAA;;;;AASM,mBAAA,UAAA,KAAA,SAAA,IAAA;AAGA,cAAA,KAAA,QAAA;;;;UAIA;;;AASA,eAAA,aAAA;AAGA,eAAA,UAAA,MAAA;AACE,iBAAA,aAAA;AAAA,UACF,CAAA;AAAA,QACF;AAAA,MACF;AAAA;IAGF,YAAA,MAAA;AAGE,UAAA,QAAA,KAAA,KAAA;;;AAKM,gBAAA,KAAA,WAAA;AACE,mBAAA,UAAA,YAAA,KAAA;;YAEF;;AAIE,mBAAA,KAAA,YAAA,KAAA;AACA,mBAAA,KAAA,mBAAA,KAAA,aAAA;AAAA,YACF;AAAA,UACF;AAAA,QACF,CAAA;;AAMA,aAAA,aAAA;AAAA,MACF;AAAA;;AAKA,WAAA,UAAA,IAAA;AAAA;IAGF,cAAA,GAAA;AACE,UAAA,CAAA,KAAA,CAAA,EAAA,iBAAA,CAAA,EAAA,cAAA,WAAA,CAAA,EAAA,cAAA,QAAA,KAAA;;MAEA;;;;AAKA,YAAA,MAAA,YAAA,EAAA,cAAA,QAAA;AAEAA,oBAAAA,MAAA,WAAA;AAAA,QACE;AAAA;AAEE,qBAAA,MAAA;;;QAGF;AAAA;;IAMJ,mBAAA;AACE,UAAA;AACE,cAAA,WAAA,KAAA;AACA,YAAA,eAAA,SAAA;AAGA,YAAA,CAAA,cAAA;;QAEA,WAAA,OAAA,iBAAA,UAAA;AACE,cAAA;AACE,gBAAA,UAAA,aAAA;AAEE,sBAAA,QAAA,QAAA,QAAA,GAAA;;;YAKF;AACA,2BAAA,KAAA,MAAA,OAAA;AAAA,UACF,SAAA,GAAA;;UAEA;AAAA;;QAGF;;AAIE,uBAAA,cAAA;AAAA,QACF;AAEA,cAAA,eAAA,KAAA;AAGA,YAAA,KAAA,WAAA,GAAA;;;;QAMA;AAEA,YAAA,iBAAA,KAAA,aAAA;AACE,eAAA,aAAA;AAAA,QACF;AAEA,eAAA;AAAA,MACF,SAAA,GAAA;;AAEE,eAAA,EAAA,aAAA,OAAA,YAAA,MAAA;AAAA,MACF;AAAA;IAGF,sBAAA;;AAEI,YAAA;;;;;AAKE,kBAAA,KAAA;AAAA,QACF;AAAA,MACF,CAAA;AAAA;IAGF,iBAAA,YAAA,OAAA;AACE,UAAA,CAAA,aAAA,KAAA,WAAA;;MAEA;AAEA,WAAA,YAAA;;QAGE,MAAA,KAAA;AAAA,QACA,OAAA;AAAA;AAAA,QACA,MAAA;AAAA;;AAKEA,sBAAA,MAAA,YAAA;AACA,aAAA,YAAA;AAIA,YAAA,IAAA,WAAA,KAAA;;AAGE,cAAA,KAAA,SAAA,GAAA;AACE,iBAAA,OAAA;AAAA;;UAIF;AAGA,cAAA,IAAA,KAAA,UAAA,QAAA;AACE,iBAAA,aAAA,IAAA,KAAA;AAAA,UACF;AAGA,cAAA,KAAA,WAAA,GAAA;;;;;;;;UAYA;AAEA,eAAA,UAAA,KAAA,WAAA,KAAA,KAAA,SAAA;AAGA,cAAA,KAAA,KAAA,SAAA,KAAA,aAAA;AACE,iBAAA,cAAA,KAAA,KAAA;AAAA,UACF;AAEA,eAAA,WAAA;AAEA,iBAAA,IAAA;AAAA;;AAGA,iBAAA,QAAA,OAAA,IAAA,OAAA,QAAA;AAAA,QACF;AAAA;;;MAKF,CAAA;AAAA;IAGJ,0BAAA,YAAA,OAAA;AACE,UAAA,CAAA,aAAA,KAAA,WAAA;;MAEA;AAEA,WAAA,YAAA;;QAGE,MAAA,KAAA;AAAA,QACA,OAAA;AAAA;AAAA;;;AAKF,aAAAE,0BAAA,MAAA,EAAA,KAAA,SAAA;AACEF,sBAAA,MAAA,YAAA;AACA,aAAA,YAAA;AAEA,YAAA,IAAA,WAAA,KAAA;;AAGE,cAAA,KAAA,SAAA,GAAA;AACE,iBAAA,OAAA;AAAA;;UAIF;AAGA,cAAA,IAAA,KAAA,UAAA,QAAA;AACE,iBAAA,aAAA,IAAA,KAAA;AAAA,UACF;AAGA,cAAA,KAAA,WAAA,GAAA;;;;;;;;UAYA;AAEA,eAAA,UAAA,KAAA,WAAA,KAAA,KAAA,SAAA;AAGA,cAAA,KAAA,KAAA,SAAA,KAAA,aAAA;AACE,iBAAA,cAAA,KAAA,KAAA;AAAA,UACF;AAEA,eAAA,WAAA;AAEA,iBAAA,IAAA;AAAA;;AAGA,cAAA,KAAA,SAAA,GAAA;AACE,iBAAA,UAAA;;UAEF;AAEAA,wBAAAA,MAAA,UAAA;AAAA;;;UAIA,CAAA;AAEA,iBAAA,QAAA,OAAA,IAAA,OAAA,WAAA;AAAA,QACF;AAAA,MACF,CAAA,EAAA,MAAA,SAAA;AACEA,sBAAA,MAAA,YAAA;AACA,aAAA,YAAA;;AAGA,YAAA,KAAA,SAAA,GAAA;AACE,eAAA,UAAA;;QAEF;AAEAA,sBAAAA,MAAA,UAAA;AAAA;;;QAIA,CAAA;;MAGF,CAAA;AAAA;IAGF,cAAA,OAAA,UAAA,YAAA,OAAA;AAGE,UAAA,CAAA,aAAA,KAAA,WAAA;;MAEA;AAEA,WAAA,YAAA;;QAGE,MAAA,KAAA;AAAA,QACA,OAAA;AAAA;AAAA,QACA;AAAA;AAGF,UAAA,SAAA,UAAA;;AAEE,eAAA,WAAA,KAAA,gBAAA;AAAA,MACF;AAEA,aAAAE,0BAAA,MAAA,EAAA,KAAA,SAAA;AAEE,aAAA,YAAA;AACAF,sBAAA,MAAA,YAAA;AAIA,YAAA,IAAA,WAAA,KAAA;;AAGE,cAAA,KAAA,SAAA,GAAA;AACE,iBAAA,OAAA;AAAA;;UAIF;AAGA,cAAA,IAAA,KAAA,UAAA,QAAA;AACE,iBAAA,aAAA,IAAA,KAAA;AAAA,UACF;AAGA,cAAA,KAAA,WAAA,GAAA;;;;;;;;UAYA;AAEA,eAAA,UAAA,KAAA,WAAA,KAAA,KAAA,SAAA;AAGA,cAAA,KAAA,KAAA,SAAA,KAAA,aAAA;AACE,iBAAA,cAAA,KAAA,KAAA;AAAA,UACF;AAEA,eAAA,WAAA;AAEA,iBAAA,IAAA;AAAA;;AAGA,cAAA,KAAA,SAAA,GAAA;AACE,iBAAA,UAAA;;UAEF;AAEAA,wBAAAA,MAAA,UAAA;AAAA;;;UAIA,CAAA;AAEA,iBAAA,QAAA,OAAA,IAAA,OAAA,QAAA;AAAA,QACF;AAAA,MACF,CAAA,EAAA,MAAA,SAAA;AAEE,aAAA,YAAA;AACAA,sBAAA,MAAA,YAAA;;AAGA,YAAA,KAAA,SAAA,GAAA;AACE,eAAA,UAAA;;QAEF;;AAIAA,wBAAAA,MAAA,UAAA;AAAA;;;UAIA,CAAA;AAAA,QACA;;MAGF,CAAA;AAAA;;AAIA,UAAA,CAAA,KAAA,eAAA,KAAA,cAAA,IAAA;AACE,aAAA,cAAA;AAAA,MACF;AACA,UAAA,CAAA,KAAA,YAAA;AACE,aAAA,aAAA;AAAA,MACF;AAEA,UAAA,CAAA,KAAA,QAAA,KAAA,KAAA,WAAA,GAAA;AACE;AAAA,MACF;;;;AAOA,UAAA,KAAA;AAAA;;AAGA,iBAAA,MAAA;;MAEA,GAAA,GAAA;AAGA,UAAA,KAAA,aAAA,KAAA,gBAAA,KAAA,eAAA,UAAA;AACE;AAAA,MACF;;AAKA,UAAA,KAAA,WAAA,KAAA,CAAA,YAAA;AACE;AAAA,MACF;;;AAKE;AAAA,MACF;;;MAKA;;;;AAQA,WAAA,UAAA,MAAA;AACE,aAAA,SAAA,IAAA,EAAA,MAAA,WAAA;AAGE,eAAA,OAAA;;AAIAA,wBAAAA,MAAA,UAAA;AAAA,YACE,OAAA;AAAA;;UAGF,CAAA;AAAA,QACF,CAAA;AAAA,MACF,CAAA;AAAA;;IAIF,eAAA,SAAA,WAAA,KAAA;AACEA,oBAAAA,MAAA,UAAA;AAAA;;QAGE;AAAA,MACF,CAAA;AAAA;;;;MAOA;AAGA,YAAA,SAAA,OAAA,OAAA,CAAA,GAAA,IAAA;;AAIA,YAAA,OAAA,KAAA,qBAAA,MAAA;AACA,YAAA,OAAA,KAAA,eAAA,MAAA;;AAIA,aAAA,OAAA,QAAA;AAAA,QACE;AAAA;;QAGA;AAAA;QAEA,YAAA,OAAA,cAAA,OAAA,SAAA;AAAA,QACA,OAAA,OAAA,SAAA,OAAA,cAAA;AAAA;QAEA,iBAAA,OAAA,mBAAA,OAAA,YAAA,OAAA,eAAA;AAAA;;;MAIF,CAAA;;;AAOA,WAAA,mBAAA,MAAA;AAGA,UAAA,OAAA,WAAA;;;MAGA;AAEA,aAAA,YAAA,OAAA,aAAA;AACA,aAAA,cAAA,OAAA,eAAA;AACA,aAAA,WAAA,OAAA,YAAA;;;;QAME;AAAA,MACF;;;;;;;AASE,eAAA,KAAA;AAAA,MACF;AAEA,UAAA,KAAA,SAAA,KAAA,WAAA;;MAEA,WAAA,KAAA,OAAA;;MAEA,WAAA,KAAA,UAAA,MAAA,QAAA,KAAA,MAAA,KAAA,KAAA,OAAA,SAAA;;;;MAKA;AAAA;;IAIF,eAAA,MAAA;;;AAEI,eAAA,KAAA;AAAA,MACF;AAEA,aAAA;AAAA;QAEE,UAAA,UAAA,cAAA,mBAAA,WAAA,KAAA,eAAA;AAAA;;QAGA,KAAA,KAAA,YAAA;AAAA;;;;AAMF,UAAA,OAAA,SAAA,GAAA;AACE,aAAA,mBAAA,MAAA;AAAA;AAEA,aAAA,mBAAA,MAAA;AAAA;AAEA,aAAA,mBAAA,MAAA;AAAA,MACF;AAAA;;;AAKA,UAAA,CAAA,OAAA,QAAA,CAAA,MAAA,QAAA,OAAA,IAAA,GAAA;AACE,eAAA,OAAA;;AAGE,iBAAA,OAAA,OAAA,OAAA,IAAA,SAAA;AACE,mBAAA,OAAA,QAAA,WAAA,EAAA,IAAA,IAAA;AAAA,UACF,CAAA;AAAA,QACF;AAEA,YAAA,OAAA,OAAA,OAAA,IAAA,KAAA;;;AAGI,mBAAA,KAAA,QAAA,OAAA,GAAA;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEA,aAAA,OAAA,OAAA,KAAA,IAAA,SAAA;;AAEI,iBAAA,EAAA,KAAA,KAAA,MAAA,KAAA,MAAA;QACF;AACA,eAAA;AAAA,UACE,KAAA,IAAA,OAAA;AAAA,UACA,MAAA,IAAA,QAAA;AAAA,UACA,MAAA,IAAA,QAAA;AAAA;MAEJ,CAAA;AAEA,aAAA,YAAA,OAAA,KAAA;AAAA;;;;;UAOI,KAAA,OAAA,SAAA,OAAA,aAAA;AAAA;;;;MAKJ;AAAA;;;AAKA,UAAA,CAAA,OAAA,OAAA;;UAEI,MAAA,OAAA,cAAA;AAAA,UACA,OAAA,OAAA,eAAA;AAAA;;MAGJ;AAAA;;AAGA,WAAA,cAAA,EAAA,QAAA,CAAA,EAAA;AAAA;;AAKA,QAAA,gBAAA;AAAA;;;AAGA,UAAA,GAAA,4BAAA,kBAAA,mBAAA;AAAA;;;;;;;;AAYA,UAAA;AACE,aAAA,cAAA;;AAMA,aAAA,SAAA;;AAMA,aAAA,cAAA;;AAIE,eAAA,UAAA;;AAEA;AAAA,QACF;AAGA,YAAA,UAAA,KAAA,CAAA,KAAA,cAAA;AACE,eAAA,cAAA;;QAEF;;;;MAOF,UAAA;;MAEA;AAAA;;IAIF,gBAAA;;AAEE,WAAA,UAAA;;;;;;;;UASI,MAAA,CAAA;AAAA,UACA,MAAA;AAAA;;;;;AAMF,aAAA,aAAA;AAAA;;;;MAKF;AAAA;;;;;AAUE,aAAA,SAAA,IAAA;AAAA,MACF;AAAA;;IAIF,yBAAA,UAAA;AAEE,YAAA,eAAA,KAAA;AAGA,UAAA,KAAA,WAAA,GAAA;;;AAGA,aAAA,cAAA,SAAA,gBAAA;AAAA,MACA;AAGA,UAAA,iBAAA,KAAA,aAAA;AAEE,aAAA,cAAA,KAAA,MAAA;;;;AAQA,aAAA,SAAA,IAAA,EAAA,MAAA,SAAA;AAAA,QACA,CAAA;AAAA,MACF;AAAA;;;;;;AAeE,YAAA;;QAEA,SAAA,GAAA;;QAEA;AAAA,MACF;AAEA,aAAA;AAAA;;;AAMA,WAAA,UAAA,KAAA,OAAA,MAAA,IAAA,OAAA;AAGA,YAAA,WAAA,KAAA;;;AAGA,WAAA,aAAA,SAAA,eAAA;AAEA,aAAA;AAAA;;IAIF,kBAAA;AACE,YAAA,WAAA,KAAA;;;AAGA,WAAA,UAAA,KAAA,OAAA,MAAA,IAAA,OAAA;AACA,WAAA,aAAA,SAAA,eAAA;AAAA;;IAGF,oBAAA;AACE,WAAA,YAAA;;;AAGAA,oBAAA,MAAA,YAAA;AAAA;;;AAKA,YAAA,WAAA;AAAA,QACE;AAAA;;;;AAMF,YAAA,gBAAA,SAAA,KAAA,MAAA,KAAA,WAAA,SAAA,MAAA,CAAA;AAEAA,oBAAAA,MAAA,YAAA;AAAA,QACE,OAAA;AAAA,QACA,MAAA;AAAA,MACF,CAAA;AAAA;;IAIF,iBAAA,UAAA,QAAA;AACEA,oBAAAA,MAAA,UAAA;AAAA;QAEE,MAAA;AAAA;MAEF,CAAA;AAAA;;IAIF,eAAA,UAAA,QAAA;AACEA,oBAAAA,MAAA,UAAA;AAAA;;;MAIA,CAAA;AAAA;;IAIF,mBAAA;AACEA,oBAAAA,MAAA,UAAA;AAAA;;QAGE,aAAA;AAAA,QACA,YAAA;AAAA,QACA,SAAA,CAAA,QAAA;AACE,cAAA,IAAA,SAAA;AACE,iBAAA,UAAA;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAA;AAAA;;;AAWA,WAAA,wBAAA;AAAA;;AAoDA,WAAA,+BAAA;AAAA;IAGF,iCAAA;;;;;;;;;AA+BE,UAAA;;AAEI,uBAAA,KAAA,aAAA;;QAEF;AAEA,YAAA,KAAA,aAAA;AACE,uBAAA,KAAA,WAAA;AACA,eAAA,cAAA;AAAA,QACF;AAEA,YAAA,KAAA,WAAA;AACE,cAAA,OAAA,yBAAA,aAAA;AACE,iCAAA,KAAA,SAAA;AAAA,UACF;AACA,eAAA,YAAA;AAAA,QACF;AAAA;MAEF;AAAA;;;AAKA,UAAA;;AAKE,YAAA,KAAA,cAAA,KAAA,aAAA;AACE,gBAAA,MAAA,KAAA;AACA,iBAAA,KAAA,KAAA,UAAA,EAAA,QAAA,SAAA;AACE,kBAAA,QAAA,KAAA,WAAA,GAAA;AACA,gBAAA,SAAA,MAAA,cAAA,MAAA,MAAA,aAAA,KAAA,YAAA,SAAA,GAAA;;AAGE,oBAAA,aAAA;AAAA,YACF;AAAA,UACF,CAAA;AAAA,QACF;AAAA;MAqBF;AAAA;;IAIF,gBAAA;AAEEG,+BAAA;AAEI,YAAA,IAAA,WAAA,OAAA,IAAA,MAAA;AAEE,eAAA,SAAA,IAAA,KAAA,IAAA,UAAA;AACE,mBAAA;AAAA;cAEE,aAAA,KAAA,eAAA,KAAA;AAAA,cACA,eAAA,KAAA,iBAAA,KAAA;AAAA,cACA,MAAA,KAAA,eAAA,KAAA;AAAA;AAAA,cACA,QAAA,KAAA,iBAAA,KAAA;AAAA;AAAA;;cAGA,eAAA,KAAA,iBAAA;AAAA,cACA,cAAA,KAAA,gBAAA;AAAA,cACA,YAAA,KAAA,gBAAA;AAAA;AAAA,cACA,oBAAA,KAAA,sBAAA;AAAA;UAEJ,CAAA;AAAA;AAEA,eAAA,SAAA;QACF;AAAA;AAGA,aAAA,SAAA;MACF,CAAA;AAAA;;IAIJ,eAAA,KAAA;AAEEH,oBAAA,MAAA,YAAA;AAGA,WAAA,YAAA;;AAIA,UAAA,KAAA,SAAA,GAAA;AACE,aAAA,UAAA;;MAEF;AAGAA,oBAAAA,MAAA,UAAA;AAAA,QACE,OAAA,IAAA,OAAA;AAAA;;MAGF,CAAA;AAAA;;IAIF,kBAAA;AAEE,YAAA,iBAAAA,cAAA,MAAA,eAAA,eAAA,KAAAA,oBAAA,eAAA,eAAA;;AAGA,UAAA,kBAAA,iBAAA;AACE,aAAA,eAAA,WAAA,cAAA;;;AAKA,YAAA,eAAA;;;AAIE,eAAA,uBAAA,KAAA,cAAA,KAAA,aAAA;AAAA,QACF;AACA;AAAA,MACF;;;;IAOF,kBAAA;;;;;AAwBEA,oBAAAA,MAAA,YAAA;AAAA;QAEE,SAAA;AAAA;AAAA,QACA,SAAA,CAAA,QAAA;AACE,eAAA,sBAAA,GAAA;AAAA;;AAGAA,wBAAA,MAAA,MAAA,OAAA,oCAAA,sBAAA,GAAA;;QAEF;AAAA,MACF,CAAA;AAAA;;;AAKA,WAAA,eAAA,IAAA;AACA,WAAA,gBAAA,IAAA;;;;;AAUA,UAAA,IAAA,SAAA;AACE,aAAA,WAAA,IAAA,QAAA,QAAA,IAAA,QAAA,YAAA,IAAA,QAAA,YAAA;AACA,YAAA,KAAA,UAAA;;QAEA;AAAA;;MAIF;AAAA;;;AAKAA,oBAAA,MAAA,MAAA,OAAA,oCAAA,SAAA,GAAA;AAIA,UAAA,IAAA,QAAA;AACE,YAAA,IAAA,OAAA,SAAA,WAAA;AAAA;AAAA,iBAEA,IAAA,OAAA,SAAA,SAAA;AAAA;AAAA,iBAEA,IAAA,OAAA,SAAA,MAAA;AAAA;AAAA,MAGF;;;;IAcF,qBAAA;AACE,WAAA,eAAA;AACA,WAAA,gBAAA;;;;;;;IAUF,kBAAA;AAGEA,0BAAA,kBAAA,eAAA;AACAA,0BAAA,kBAAA,eAAA;AACAA,0BAAA,kBAAA,gBAAA;;;;IAMF,uBAAA,UAAA,WAAA;;QAKI,KAAA;AAAA;UAEE,UAAA,GAAA,QAAA,IAAA,SAAA;AAAA;;UAEA,SAAA;AAAA;QAEF,SAAA,CAAA,QAAA;AAEE,cAAA,IAAA,KAAA,WAAA,KAAA,IAAA,KAAA,QAAA;;AAEE,kBAAA,WAAA,OAAA,QAAA;;AAEA,kBAAA,WAAA,OAAA,QAAA;AAGA,gBAAA,cAAA;AACA,gBAAA,YAAA,MAAA;AACE,kBAAA,aAAA,MAAA;AAEE,8BAAA,YAAA,YAAA;AAAA;AAGA,8BAAA,WAAA,QAAA,YAAA;AAAA,cACF;AAAA;AAGA,4BAAA,OAAA,WAAA;AAAA,YACF;;;;;UAYF;AAAA;QAEF,MAAA,MAAA;;QAEA;AAAA,MACF,CAAA;AAAA;;IAIF,qBAAA;;;;;IAOA,iBAAA,EAAA,MAAA,UAAA;;;AAIM,cAAA,KAAA,YAAA,UAAA,KAAA,OAAA,QAAA;AACE,mBAAA;AAAA,cACE,GAAA;AAAA,cACA,eAAA;AAAA;UAEJ;;QAEF,CAAA;AAAA,MACF;AAGA,WAAA,SAAA,IAAA;AAAA;;;AAIA,WAAA,SAAA,IAAA;AAAA;;AAGA,UAAA,KAAA,KAAA,GAAA,GAAA;AAEE,aAAA,KAAA,GAAA,EAAA,YAAA;AAAA,MACF;AAAA;;EAGJ,UAAA;AAAA,IACE,UAAA;AACE,aAAA,KAAA,UAAA;AAAA;;IAGF,eAAA;AAEE,UAAA,CAAA,KAAA,mBAAA,KAAA,gBAAA,WAAA,KAAA,KAAA,QAAA;AACE,aAAA,kBAAA,KAAA,KAAA,IAAA,UAAA;AACE,cAAA,CAAA,KAAA,YAAA;AACE,mBAAA,KAAA,kBAAA,IAAA;AAAA,UACF;;QAEF,CAAA;AAAA,MACF;;IAIF;AAAA;EAEF,OAAA;AAAA;AAAA;;IAKE;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrxEA,GAAG,WAAW,eAAe;"}