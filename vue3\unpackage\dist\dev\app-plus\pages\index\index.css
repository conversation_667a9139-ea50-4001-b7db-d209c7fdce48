
/* APP端兼容性优化 - 移除CSS变量，使用具体值 */
.tabbar[data-v-e9b92a61]{
  position: fixed;
  z-index: 998;
  width: 100%;
  /* APP端兼容性：条件编译底部安全区域 */

  bottom: 1.5625rem;




  box-sizing: border-box;
  justify-content: center;
  pointer-events: none; /* 优化性能，只有子元素可点击 */
}
.tabbar-box[data-v-e9b92a61]{
  z-index: 998;
  width: calc(100% - 3.75rem);
  height: 3.125rem;
  border-radius: 1.5625rem;
  justify-content: space-around;
  /* APP端兼容性：条件编译backdrop-filter */




  pointer-events: auto; /* 恢复点击事件 */
  transition: all 0.3s ease; /* 添加过渡动画 */
}
.tb-bs[data-v-e9b92a61]{
  box-shadow: 0 0 0.75rem rgba(0, 0, 0, 0.06);
  border: 1px solid #f8f8f8;
}
.tabbar-box .tabbar-item[data-v-e9b92a61]{
  width: 20%;
  height: 3.125rem;
  justify-content: center;
  position: relative;
  transition: transform 0.2s ease; /* 添加点击反馈动画 */
  cursor: pointer;
}

/* 小程序兼容性：使用class替代:active伪类 */
.tabbar-item.active-state[data-v-e9b92a61] {
  transform: scale(0.95); /* 点击时缩放效果 */
}
.tabbar-item .icon[data-v-e9b92a61]{
  width: 1.5rem;
  height: 1.5rem;
  transition: opacity 0.2s ease; /* 图标切换动画 */
}
.tabbar-item .msg[data-v-e9b92a61]{
  position: absolute;
  top: 0.5625rem;
  left: calc(50% + 0.25rem);
  min-width: 1.0625rem;
  height: 1.0625rem;
  line-height: 1.0625rem;
  text-align: center;
  font-size: 0.5625rem;
  font-weight: 700;
  color: #fff;
  background: #fa5150;
  border-radius: 1.0625rem;
  border: 0.0625rem solid #fff;
}
.tabbar-item .add[data-v-e9b92a61]{
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 50%;
  justify-content: center;
  transition: all 0.3s ease-in-out;
}
.tabbar-item .add uni-image[data-v-e9b92a61]{
  width: 0.5rem;
  height: 0.5rem;
}
.tabbar .tabbar-add[data-v-e9b92a61]{
  position: fixed;
  z-index: 997;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  background-image: linear-gradient(to bottom, rgb(173 173 173 / 95%), rgb(25 25 25 / 95%));
  /* APP端兼容性：条件编译backdrop-filter */
}
.content-wrapper[data-v-e9b92a61] {
  width: 100%;
  padding-top: 5.625rem;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 头部标题区域 */
.add-header[data-v-e9b92a61] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 80%;
  padding-top: 2.8125rem;
}
.header-content[data-v-e9b92a61] {
  flex: 1;
}
.header-image[data-v-e9b92a61] {
  width: 9.375rem;
  height: 9.375rem;
}
.header-image uni-image[data-v-e9b92a61] {
  width: 100%;
  height: 100%;
  border-radius: 0.375rem;
}
.add-title[data-v-e9b92a61] {
  font-size: 1.25rem;
  font-weight: bold;
  color: #fff;
  display: flex;
  align-items: center;
}
.add-plus[data-v-e9b92a61] {
  margin-left: 0.25rem;
  font-weight: normal;
}
.add-subtitle[data-v-e9b92a61] {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 0.25rem;
}

/* 新增卡片样式 */
.card-container[data-v-e9b92a61] {
  width: 80%;
  display: flex;
  flex-direction: column;
  gap: 0.9375rem;
  margin-top: 7.5rem;
  pointer-events: auto; /* 确保容器可以传递点击事件 */
}

/* 卡片通用样式 */
.card[data-v-e9b92a61] {
  display: flex;
  align-items: center;
  border-radius: 0.625rem;
  padding: 0.9375rem;
  background-color: rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  pointer-events: auto; /* 确保卡片可以被点击 */
}

/* 小程序兼容性：使用class替代:active伪类 */
.card.active-state[data-v-e9b92a61] {
  transform: scale(0.98);
  background-color: rgba(255, 255, 255, 0.15);
}
.cream-card[data-v-e9b92a61] {
  background-color: rgba(255, 255, 255, 0.15);
}
.mint-card[data-v-e9b92a61] {
  background-color: rgba(255, 255, 255, 0.15);
}
.card-left[data-v-e9b92a61] {
  width: 1.875rem;
  height: 1.875rem;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.625rem;
}
.card-left uni-image[data-v-e9b92a61] {
  width: 1.25rem;
  height: 1.25rem;
}
.card-content[data-v-e9b92a61] {
  flex: 1;
}
.card-title[data-v-e9b92a61] {
  font-size: 0.875rem;
  font-weight: bold;
  color: #fff;
}
.card-subtitle[data-v-e9b92a61] {
  font-size: 0.6875rem;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 0.1875rem;
}
.card-right[data-v-e9b92a61] {
  width: 1.125rem;
  height: 1.125rem;
  transform: rotate(180deg);
}
.card-right uni-image[data-v-e9b92a61] {
  width: 100%;
  height: 100%;
}

/* 两列卡片布局 */
.two-column-container[data-v-e9b92a61] {
  display: flex;
  width: 100%;
  gap: 0.625rem;
}
.two-column-card[data-v-e9b92a61] {
  flex: 1;
  border-radius: 0.5rem;
  padding: 0.9375rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.3s ease;
  cursor: pointer;
  pointer-events: auto; /* 确保两列卡片可以被点击 */
}

/* 小程序兼容性：使用class替代:active伪类 */
.two-column-card.active-state[data-v-e9b92a61] {
  transform: scale(0.98);
}
.video-card[data-v-e9b92a61] {
  background-color: rgba(76, 130, 219, 0.3);
}
.audio-card[data-v-e9b92a61] {
  background-color: rgba(245, 166, 35, 0.3);
}
.card-content-left[data-v-e9b92a61] {
  flex: 1;
}
.two-column-card-title[data-v-e9b92a61] {
  font-size: 0.8125rem;
  font-weight: bold;
  color: #FFFFFF;
}
.two-column-card-desc[data-v-e9b92a61] {
  font-size: 0.625rem;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 0.1875rem;
}
.card-content-right[data-v-e9b92a61] {
  width: 1.5625rem;
  height: 1.5625rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
.card-content-right uni-image[data-v-e9b92a61] {
  width: 1.25rem;
  height: 1.25rem;
}
.close-btn uni-image[data-v-e9b92a61] {
  width: 0.75rem;
  height: 0.75rem;
}
.df[data-v-e9b92a61]{
  display: flex;
  align-items: center;
}
.bfh[data-v-e9b92a61]{
  background: rgba(0, 0, 0, 0.8);
}
.bfw[data-v-e9b92a61]{
  background: #fff;
}

/* 动画优化 - APP端兼容性 */
.fade-in[data-v-e9b92a61]{
  animation: fadeIn-e9b92a61 0.3s forwards;
}
.fade-out[data-v-e9b92a61]{
  animation: fadeOut-e9b92a61 0.3s forwards;
}
@keyframes fadeIn-e9b92a61{
from{
    opacity: 0;
    transform: translateY(0.3125rem);
}
to{
    opacity: 1;
    transform: translateY(0);
}
}
@keyframes fadeOut-e9b92a61{
from{
    opacity: 1;
    transform: translateY(0);
}
to{
    opacity: 0;
    transform: translateY(0.3125rem);
}
}


.container { 
  background: #ffffff; 
  min-height: 100vh;
  width: 100%;
  overflow-x: hidden; /* Prevent horizontal scrolling */
}
.content-box {
  width: 100%;
  height: 100%;
  overflow-x: hidden; /* Prevent horizontal overflow */
  position: relative;
  z-index: 1;
  will-change: transform;
  padding-left: 0.3125rem;
  padding-right: 0.3125rem;
  box-sizing: border-box; /* Include padding in width calculation */
}
.content-scroll {
  height: 100%;
  -webkit-overflow-scrolling: touch;
}
.nav-box {
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 99;
  background: #fff;
  box-shadow: 0 0.0625rem 0.25rem rgba(0,0,0,0.03);
  transform: translateZ(0);
  will-change: transform;
}
.bar-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 2.75rem;
  padding: 0 1rem;
}
.bar-logo {
  height: 1.5rem;
  width: 3.75rem;
}
.bar-title { flex: 1; text-align: center; font-size: 1.0625rem; font-weight: bold; color: #222; letter-spacing: 0.0625rem;
}
.bar-icons {
  display: flex;
  align-items: center;
  gap: 1rem;
}
.bar-icon {
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 50%;
  background: #fff;
  box-shadow: 0 0.0625rem 0.25rem rgba(0,0,0,0.06);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s;
}
.bar-icon:active {
  background: #f0f0f0;
}
.shadow {
  box-shadow: 0 0.125rem 0.75rem rgba(45,156,250,0.10);
}
.top-section {
  display: flex;
  justify-content: space-between;
  margin: 0.3125rem 0 0.625rem;
  padding: 0 0.15625rem;
}
.influencer-card { 
  background: #2d9cfa; 
  border-radius: 0.625rem; 
  padding: 0.46875rem; 
  width: 42%; 
  color: #fff; 
  position: relative;
}
.avatar { width: 3.125rem; height: 3.125rem; border-radius: 0.625rem;
}
.voice-time { position: absolute; left: 0.46875rem; top: 0.46875rem; background: #fff; color: #2d9cfa; border-radius: 0.625rem; padding: 0.0625rem 0.3125rem; font-size: 0.6875rem;
}
.name { font-size: 1rem; font-weight: bold; margin-top: 0.3125rem;
}
.desc { font-size: 0.75rem; margin-top: 0.125rem;
}
.right-func { 
  width: 52%;
  display: flex; 
  flex-direction: column; 
  justify-content: space-between;
}
.func-box {
  margin-bottom: 0.3125rem;
  padding: 0.375rem;
  background: #fff;
  border-radius: 0.625rem;
  box-shadow: 0 0.125rem 0.75rem rgba(45,156,250,0.10);
}
.func-title { font-size: 0.75rem; font-weight: bold; margin-bottom: 0.1875rem;
}
.func-avatars { display: flex;
}
.func-avatar { width: 1rem; height: 1rem; border-radius: 50%; margin-right: 0.1875rem;
}
.func-status { font-size: 0.6875rem;
}
.closed { color: #b0b0b0;
}
.online { color: #ffb800;
}
.popular-section { margin: 0.625rem 0.3125rem 0.9375rem;
}
.popular-title { display: flex; justify-content: space-between; align-items: center; font-size: 1rem; font-weight: bold; margin-bottom: 0.5rem;
}
.all-btn { color: #2d9cfa; font-size: 0.75rem;
}

/* 圈子相关样式 */
.scroll-box{
  width:100%;
  white-space:nowrap;
  overflow:hidden;
  transition:height .45s ease-in-out
}
.circle-box{
  width: 100%;
  display: flex;
  padding: 0.9375rem 0.3125rem;
  box-sizing: border-box; /* Include padding in width calculation */
}
.circle-box .circle-item{
  flex-shrink:0
}
.circle-item .circle-item-top{
  margin:0 0.625rem;
  width:3.625rem;
  height:3.625rem;
  border-radius:50%;
  background:#f8f8f8;
  border:0.0625rem solid #f5f5f5;
  position:relative
}
.circle-item-top uni-image{
  width:100%;
  height:100%;
  border-radius:50%
}
.circle-item-top .icon{
  margin:1.0625rem;
  width:1.5rem;
  height:1.5rem
}
.circle-item-top .circle-item-tag{
  position:absolute;
  right:0;
  bottom:0;
  width:0.75rem;
  height:0.75rem;
  border-radius:50%;
  border:0.1875rem solid #fff
}
.circle-item .circle-name{
  margin: 0.625rem 0 0.3125rem;
  width: 5rem;
  color: #000;
  font-weight: 500;
  font-size: 0.75rem;
  line-height: 0.75rem;
  text-align: center;
  box-sizing: border-box; /* Include padding in width calculation */
}
.circle-item .circle-tips{
  width: 5rem;
  color: #999;
  font-size: 0.5625rem;
  line-height: 0.5625rem;
  font-weight: 300;
  text-align: center;
  box-sizing: border-box; /* Include padding in width calculation */
}
.playwith-section { margin: 0.625rem 0.3125rem 0.9375rem;
}
.playwith-title { font-size: 1rem; font-weight: bold; margin-bottom: 0.5rem;
}
.playwith-tabs {
  display: flex;
  background: #fff;
  border-radius: 0.5rem;
  overflow: hidden;
  margin-bottom: 0.5rem;
  box-shadow: 0 0.0625rem 0.25rem rgba(0,0,0,0.03);
}
.playwith-tab {
  flex: 1;
  text-align: center;
  font-size: 0.9375rem;
  color: #999;
  padding: 0.625rem 0;
  font-weight: 500;
  position: relative;
  transition: all 0.3s ease;
}
.playwith-tab.active {
  color: #000;
  font-weight: 700;
  transform: scale(1.05);
}
.playwith-tab .active-line {
  position: absolute;
  bottom: -0.15625rem;
  left: 50%;
  transform: translateX(-50%);
  width: 0.9375rem;
  height: 0.1875rem;
  background-color: #000;
  border-radius: 0.09375rem;
  transition: all 0.3s ease;
}
.playwith-tab-content {
  /* 可加切换动画 */
}

/* 个人卡片样式 */
.profile-card {
  background: #fff;
  border-radius: 0.625rem;
  box-shadow: 0 0.125rem 0.75rem rgba(45,156,250,0.10);
  padding: 1rem 0.75rem 0.75rem 0.75rem;
  margin-bottom: 0.75rem;
}
.profile-header {
  display: flex;
  align-items: flex-start;
  position: relative;
}
.profile-avatar {
  width: 2.75rem;
  height: 2.75rem;
  border-radius: 50%;
  margin-right: 0.625rem;
}
.profile-info {
  flex: 1;
}
.profile-title {
  display: flex;
  align-items: center;
  font-size: 1rem;
  font-weight: bold;
  color: #222;
}
.profile-name {
  margin-right: 0.375rem;
}
.profile-gender {
  font-size: 0.75rem;
  color: #ff7eb3;
}
.profile-gender.female {
  color: #ff7eb3;
}
.profile-status-row {
  margin-top: 0.25rem;
  display: flex;
  gap: 0.375rem;
}
.profile-status {
  font-size: 0.6875rem;
  color: #888;
  background: #f5f6fa;
  border-radius: 0.25rem;
  padding: 0.0625rem 0.375rem;
}
.profile-status.online {
  color: #2ecc71;
  background: #eaffea;
}
.profile-status.city {
  color: #2d9cfa;
  background: #eaf6ff;
}
.profile-tags {
  margin-top: 0.25rem;
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
}
.profile-tag {
  font-size: 0.625rem;
  color: #666;
  background: #f5f6fa;
  border-radius: 0.25rem;
  padding: 0.0625rem 0.3125rem;
}
.profile-hi-btn {
  position: absolute;
  right: 0;
  top: 0;
  background: linear-gradient(90deg,#5b8cff,#b36fff);
  color: #fff;
  font-size: 0.875rem;
  font-weight: bold;
  border-radius: 0.5rem;
  padding: 0.25rem 1rem;
  box-shadow: 0 0.0625rem 0.25rem rgba(91,140,255,0.10);
}
.profile-voice {
  margin-top: 0.625rem;
}
.voice-btn {
  display: flex;
  align-items: center;
  background: #f5f6fa;
  border-radius: 0.625rem;
  width: 2.8125rem;
  padding: 0.1875rem 0.375rem;
  color: #222;
  font-size: 0.6875rem;
  font-weight: bold;
}
.voice-icon {
  width: 0.875rem;
  height: 0.875rem;
  margin-right: 0.25rem;
}
.profile-desc {
  margin-top: 0.5625rem;
  font-size: 0.8125rem;
  color: #222;
  font-weight: 500;
}
.profile-photos {
  margin-top: 0.375rem;
  display: flex;
  gap: 0.375rem;
}
.profile-photo {
  width: 3.75rem;
  height: 3.75rem;
  border-radius: 0.375rem;
  object-fit: cover;
}

/* 一起玩卡片样式 */
.playwith-card {
  display: flex;
  padding: 0.75rem;
  background: #fff;
  border-radius: 0.625rem;
  margin-bottom: 0.5rem;
  box-shadow: 0 0.125rem 0.75rem rgba(45,156,250,0.10);
}
.playwith-avatar {
  width: 3.125rem;
  height: 3.125rem;
  border-radius: 0.625rem;
  margin-right: 0.625rem;
}
.playwith-info {
  flex: 1;
}
.playwith-name {
  font-size: 1rem;
  font-weight: bold;
  color: #222;
}
.playwith-score {
  color: #ffb800;
  font-size: 0.8125rem;
  margin-bottom: 0.125rem;
}
.playwith-game {
  font-size: 0.75rem;
  color: #2d9cfa;
  margin-bottom: 0.125rem;
}
.playwith-desc {
  font-size: 0.75rem;
  color: #888;
}
.star {
  color: #ffb800;
}

/* 优化性能相关样式 */
.hardware-accelerated {
  transform: translateZ(0);
  will-change: transform;
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
}

/* 卡片渐入动画 */
@keyframes fade-in {
from { opacity: 0; transform: translateY(0.625rem);
}
to { opacity: 1; transform: translateY(0);
}
}
.ohto {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 添加底部tabbar相关样式 */
.df {
  display: flex;
  align-items: center;
}

/* 新增功能卡片样式 */
.feature-grid {
  padding: 0.46875rem; /* 减小内边距 */
}

/* 新的网格布局样式 */
.grid-layout {
  display: flex;
  flex-direction: column;
  gap: 0.46875rem; /* 减小间距 */
}

/* 顶部网格：左一右二 */
.top-grid {
  display: flex;
  gap: 0.46875rem; /* 减小间距 */
  height: 6.875rem; /* 减小高度 */
}
.left-item {
  flex: 1;
  border-radius: 0.625rem;
  padding: 0.625rem; /* 减小内边距 */
  position: relative;
  overflow: hidden;
}
.right-stack {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.46875rem; /* 减小间距 */
}
.right-item {
  flex: 1;
  border-radius: 0.625rem;
  padding: 0.46875rem; /* 减小内边距 */
  position: relative;
  overflow: hidden;
}

/* 底部一行三个 */
.bottom-row {
  display: flex;
  gap: 0.46875rem; /* 减小间距 */
}
.bottom-item {
  flex: 1;
  border-radius: 0.625rem;
  padding: 0.46875rem; /* 减小内边距 */
  position: relative;
  overflow: hidden;
  min-height: 2.8125rem; /* 减小最小高度 */
}
.feature-bubble {
  position: absolute;
  right: 0.46875rem;
  bottom: 0.46875rem;
  width: 2.5rem; /* 减小气泡大小 */
  height: 2.5rem; /* 减小气泡大小 */
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.15);
  z-index: 0;
}
.feature-content {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  z-index: 2;
  position: relative;
}
.feature-title {
  font-size: 0.875rem; /* 减小字体大小 */
  font-weight: bold;
  color: #fff;
  margin-bottom: 0.1875rem; /* 减小下边距 */
}
.feature-subtitle {
  font-size: 0.625rem; /* 减小字体大小 */
  color: rgba(255, 255, 255, 0.8);
}
.feature-btn {
  background: #ffffff;
  color: #0099ff;
  font-size: 0.75rem; /* 减小字体大小 */
  font-weight: bold;
  border-radius: 1.5625rem;
  padding: 0.1875rem 0.75rem; /* 减小内边距 */
  box-shadow: 0 0.0625rem 0.25rem rgba(0, 0, 0, 0.1);
  margin-top: 0.46875rem; /* 减小上边距 */
}
.feature-icon {
  position: absolute;
  right: 0.625rem;
  bottom: 0.625rem;
  z-index: 1;
}
.feature-icon uni-image {
  width: 1.5625rem; /* 减小图标大小 */
  height: 1.5625rem; /* 减小图标大小 */
}
.heart-icon uni-image {
  width: 1.25rem; /* 减小图标大小 */
  height: 1.25rem; /* 减小图标大小 */
}

/* Card colors */
.blue {
  background: #0099ff;
}
.pink {
  background: #ff66cc;
}
.green {
  background: #33cc99;
}
.purple {
  background: #9966ff;
}
.hot-pink {
  background: #ff6699;
}
.cyan {
  background: #00cccc;
}
