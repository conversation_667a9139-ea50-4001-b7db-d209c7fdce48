
.uni-load-more[data-v-9245e42c] {
  display: flex;
  flex-direction: row;
  height: 1.875rem;
  align-items: center;
  justify-content: center;
}
.uni-load-more__text[data-v-9245e42c] {
  font-size: 0.75rem;
  margin-left: 0.5rem;
}
.uni-load-more__img[data-v-9245e42c] {
  width: 1.5rem;
  height: 1.5rem;
}
.uni-load-more__img--nvue[data-v-9245e42c] {
  color: #999;
}
.uni-load-more__img--android[data-v-9245e42c],
.uni-load-more__img--ios[data-v-9245e42c] {
  width: 1.5rem;
  height: 1.5rem;
  transform: rotate(0);
}
.uni-load-more__img--android[data-v-9245e42c] {
  animation: loading-ios 1s 0s linear infinite;
}
.uni-load-more__img--ios-H5[data-v-9245e42c] {
  position: relative;
  animation: loading-ios-H5-9245e42c 1s 0s step-end infinite;
}
.uni-load-more__img--ios-H5 uni-image[data-v-9245e42c] {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
}
@keyframes loading-ios-H5-9245e42c {
0% {
    transform: rotate(0);
}
8% {
    transform: rotate(30deg);
}
16% {
    transform: rotate(60deg);
}
24% {
    transform: rotate(90deg);
}
32% {
    transform: rotate(120deg);
}
40% {
    transform: rotate(150deg);
}
48% {
    transform: rotate(180deg);
}
56% {
    transform: rotate(210deg);
}
64% {
    transform: rotate(240deg);
}
73% {
    transform: rotate(270deg);
}
82% {
    transform: rotate(300deg);
}
91% {
    transform: rotate(330deg);
}
to {
    transform: rotate(360deg);
}
}
.uni-load-more__img--android-MP[data-v-9245e42c] {
  position: relative;
  width: 1.5rem;
  height: 1.5rem;
  transform: rotate(0);
  animation: loading-ios 1s 0s ease infinite;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon[data-v-9245e42c] {
  position: absolute;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: solid 0.125rem transparent;
  border-top: solid 0.125rem #999;
  transform-origin: center;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon[data-v-9245e42c]:nth-child(1) {
  animation: loading-android-MP-1-9245e42c 1s 0s linear infinite;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon[data-v-9245e42c]:nth-child(2) {
  animation: loading-android-MP-2-9245e42c 1s 0s linear infinite;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon[data-v-9245e42c]:nth-child(3) {
  animation: loading-android-MP-3-9245e42c 1s 0s linear infinite;
}
@keyframes loading-android-9245e42c {
0% {
    transform: rotate(0);
}
to {
    transform: rotate(360deg);
}
}
@keyframes loading-android-MP-1-9245e42c {
0% {
    transform: rotate(0);
}
50% {
    transform: rotate(90deg);
}
to {
    transform: rotate(360deg);
}
}
@keyframes loading-android-MP-2-9245e42c {
0% {
    transform: rotate(0);
}
50% {
    transform: rotate(180deg);
}
to {
    transform: rotate(360deg);
}
}
@keyframes loading-android-MP-3-9245e42c {
0% {
    transform: rotate(0);
}
50% {
    transform: rotate(270deg);
}
to {
    transform: rotate(360deg);
}
}


.uni-popup[data-v-4dd3c44b] {
  position: fixed;
  z-index: 99;
}
.uni-popup.top[data-v-4dd3c44b],
.uni-popup.left[data-v-4dd3c44b],
.uni-popup.right[data-v-4dd3c44b] {
  top: 0;
}
.uni-popup .uni-popup__wrapper[data-v-4dd3c44b] {
  display: block;
  position: relative;
  /* iphonex 等安全区设置，底部安全区适配 */
}
.uni-popup .uni-popup__wrapper.left[data-v-4dd3c44b],
.uni-popup .uni-popup__wrapper.right[data-v-4dd3c44b] {
  padding-top: 0;
  flex: 1;
}
.fixforpc-z-index[data-v-4dd3c44b] {

  z-index: 999;
}
.fixforpc-top[data-v-4dd3c44b] {
  top: 0;
}


.lazy-image[data-v-337fa078]{position:relative;width:100%;height:100%;overflow:hidden;background:#f8f8f8;}
.lazy-image uni-image[data-v-337fa078]{width:100%;height:100%;}
.lazy-image .err[data-v-337fa078]{position:absolute;top:0;left:0;width:100%;height:100%;background:rgba(248,248,248,0.7) url('../../static/img/load.png') no-repeat center/50%;}


.container{width:100%;}
.nav-bar{position:fixed;top:0;left:0;width:100%;z-index:99;box-sizing:border-box;}
.bar-box .bar-back{padding:0 0.9375rem;width:1.0625rem;height:100%;}
.bar-box .bar-title{max-width:60%;font-size:1rem;font-weight:700;}
.nav-box{width:100%;height:2.5rem;}
.nav-box .nav-item{padding:0 0.9375rem;height:100%;flex-direction:column;justify-content:center;position:relative;}
.nav-box .nav-item uni-text{font-weight:700;transition:all .3s ease-in-out;}
.nav-box .nav-line{position:absolute;bottom:0.375rem;width:0.5625rem;height:0.1875rem;border-radius:0.1875rem;background:#000;transition:opacity .3s ease-in-out;}
.content-box{width:100%;}
.content-box .activity-item{width:calc(100% - 1.875rem);margin:0.9375rem;}
.activity-item .activity-img{width:8.59375rem;height:6.875rem;border-radius:0.25rem;background:#f8f8f8;position:relative;overflow:hidden;}
.activity-img .activity-state{position:absolute;top:0.46875rem;left:0.46875rem;width:2.125rem;height:1.1875rem;color:#fff;font-size:0.5rem;font-weight:700;background:rgba(0,0,0,.4);border:1px solid rgba(255,255,255,.16);border-radius:0.25rem;justify-content:center;}
.activity-item .activity-data{padding-left:0.625rem;width:calc(100% - 9.21875rem);height:6.875rem;display:flex;flex-direction:column;justify-content:space-between;position:relative;}
.activity-data .title{font-size:0.875rem;line-height:0.875rem;font-weight:700;padding-bottom:0.375rem;}
.activity-data .txt{margin-bottom:0.125rem;}
.activity-data .txt uni-image{margin-right:0.25rem;width:0.625rem;height:0.625rem;}
.activity-data .txt uni-view{width:calc(100% - 0.8125rem);color:#999;font-size:0.625rem;font-weight:500;}
.activity-data .cu-img-group{margin:0.25rem 0 0.5rem 0.5rem;direction:ltr;unicode-bidi:bidi-override;display:inline-block;}
.cu-img-group .cu-img{width:1rem;height:1rem;display:inline-flex;position:relative;margin-left:-0.5rem;border:0.0625rem solid #fff;background:#eee;vertical-align:middle;border-radius:0.25rem;border-radius:50%;}
.cu-img-group .cu-img uni-image{width:100%;height:100%;border-radius:0.25rem;border-radius:50%;}
.cu-img-group .cu-tit{display:inline-flex;margin-left:0.25rem;color:#999;font-size:0.625rem;font-weight:500;}
.activity-data .cu-txt-group{margin:0.25rem 0 0.5rem;font-size:0.625rem;font-weight:500;}
.activity-data .activity-btn{width:100%;height:1.875rem;justify-content:space-between;}
.activity-btn .btn-item{padding:0;margin:0;height:1.875rem;font-size:0.625rem;font-weight:700;color:#000;background:#f8f8f8;border-radius:0.25rem;justify-content:center;}
.activity-btn .btn-item .icon{margin-left:0.3125rem;width:0.625rem;height:0.625rem;}
.activity-btn .btn-item .img{width:0.75rem;height:0.75rem;}
.w100{width:100%;}
.w50{width:calc(50% - 1.25rem);}
.w60{width:calc(100% - 4.375rem);}
.popup-box{width:calc(100% - 1.875rem);padding:0.9375rem;background:#fff;border-radius:0.9375rem 0.9375rem 0 0;padding-bottom:max(env(safe-area-inset-bottom),0.9375rem);position:relative;}
.popup-box .popup-top{width:calc(100% - 0.625rem);padding:0.3125rem;justify-content:space-between;}
.popup-top .popup-title .t1{font-size:1.1875rem;font-weight:700;}
.popup-top .popup-title .t2{color:#999;font-size:0.625rem;font-weight:300;}
.popup-top .popup-close{width:1.5rem;height:1.5rem;border-radius:50%;background:#f8f8f8;justify-content:center;transform:rotate(45deg);}
.popup-box .popup-input,.popup-box .popup-textarea{width:calc(100% - 1.25rem);padding:0.625rem;border-radius:0.25rem;background:#f8f8f8;font-size:0.75rem;font-weight:700;}
.popup-box .popup-textarea{margin-top:0.9375rem;min-height:3.75rem;}
.popup-box .popup-input-tips{position:absolute;right:0.625rem;color:#999;font-size:0.75rem;font-weight:700;}
.popup-box .popup-scroll{width:100%;max-height:50vh;overflow-y:scroll;}
.popup-box .popup-item{margin-top:0.9375rem;width:calc(100% - 4px);border:2px dashed #f5f5f5;border-radius:0.5rem;}
.popup-item .code-item{margin:2px;width:calc(100% - 4px);height:2.8125rem;justify-content:center;background:#f8f8f8;border-radius:0.375rem;}
.popup-item .item-active{color:#ccc;}
.popup-item .code-item .t1{font-size:1rem;font-weight:bolder;letter-spacing:0.125rem;}
.popup-item .code-item .t2{font-size:0.8125rem;font-weight:300;}
.popup-box .popup-tips{width:100%;margin-top:1.25rem;color:#999;font-size:0.625rem;text-align:center;}
.popup-box .popup-btn{margin-top:1.25rem;width:100%;justify-content:space-between;}
.popup-box .popup-btn uni-view{width:calc(50% - 0.3125rem);height:2.8125rem;line-height:2.8125rem;text-align:center;font-size:0.75rem;font-weight:700;border-radius:0.9375rem;}
.popup-box .popup-btn .bg1{color:#999;background:#f8f8f8;}
.popup-box .popup-btn .bg2{color:#fff;background:#000;}
.tips-box{padding:0.625rem 0.9375rem;border-radius:0.375rem;justify-content:center;}
.tips-box .tips-item{color:#fff;font-size:0.875rem;font-weight:700;}
.empty-box{flex-direction:column;align-items:center;justify-content:center;padding:3.125rem 0;}
.empty-box uni-image{width:6.25rem;height:6.25rem;margin-bottom:0.9375rem;}
.empty-box .e1{font-size:0.875rem;font-weight:bold;margin-bottom:0.3125rem;}
.empty-box .e2{font-size:0.75rem;color:#999;}
.heio{width:100%;overflow:hidden;transition:height 0.3s;}
.ohto{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;}
.bfw{background:#fff;}
.df{display:flex;align-items:center;}
