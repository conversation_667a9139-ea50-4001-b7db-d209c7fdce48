"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const api_social = require("../../api/social.js");
const stores_user = require("../../stores/user.js");
if (!Array) {
  const _easycom_uni_popup2 = common_vendor.resolveComponent("uni-popup");
  _easycom_uni_popup2();
}
const _easycom_uni_popup = () => "../../uni_modules/uni-popup/components/uni-popup/uni-popup.js";
if (!Math) {
  (common_vendor.unref(lazyImage) + uniLoadMore + emptyPage + common_vendor.unref(waterfall) + common_vendor.unref(cardGg) + _easycom_uni_popup)();
}
const lazyImage = () => "../../components/lazyImage/lazyImage.js";
const uniLoadMore = () => "../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js";
const waterfall = () => "../../components/waterfall/waterfall.js";
const cardGg = () => "../../components/card-gg/card-gg.js";
const emptyPage = () => "../../components/emptyPage.js";
const _sfc_main = {
  __name: "details",
  setup(__props) {
    const instance = common_vendor.getCurrentInstance();
    const userStore = stores_user.useUserStore();
    const statusBarHeight = common_vendor.ref(20);
    const titleBarHeight = common_vendor.ref(44);
    const navbarTrans = common_vendor.ref(0);
    const userInfo = common_vendor.reactive({
      id: 0,
      uid: 0,
      avatar: "",
      name: "昵称加载中",
      nickname: "",
      intro: "",
      about_me: "",
      gender: 0,
      sex: 0,
      age: "",
      constellation: 0,
      constellation_label: "",
      province: "",
      follow_count: 0,
      fans_count: 0,
      like_count: 0,
      like_count_str: 0,
      is_follow: 0,
      is_mutual_follow: 0,
      circle: [],
      user_id_number: "",
      privacy: {
        like: 1,
        follow: 1
      },
      home_background: "[]",
      visitors: [],
      // 访客记录
      // VIP相关信息
      vip: false,
      vip_id: 0,
      vip_icon: "",
      vip_name: "",
      vip_status: 2,
      svip_open: false,
      is_ever_level: 0,
      is_money_level: 0,
      overdue_time: 0
    });
    const barList = common_vendor.ref(["笔记", "赞过"]);
    const barIdx = common_vendor.ref(0);
    const isThrottling = common_vendor.ref(false);
    const list = common_vendor.ref([]);
    const page = common_vendor.ref(1);
    const isEmpty = common_vendor.ref(false);
    const loadStatus = common_vendor.ref("loading");
    const tipsTitle = common_vendor.ref("");
    const isWaterfall = common_vendor.ref(false);
    const backgroundImages = common_vendor.ref([]);
    const currentBgIndex = common_vendor.ref(0);
    const carouselTimer = common_vendor.ref(null);
    const userId = common_vendor.ref(0);
    const isFollowing = common_vendor.ref(false);
    const limit = common_vendor.ref(10);
    const isLoading = common_vendor.ref(false);
    const isProcessing = common_vendor.ref(false);
    common_vendor.ref(false);
    common_vendor.ref(false);
    const hasError = common_vendor.ref(false);
    const errorMessage = common_vendor.ref("");
    const likePopup = common_vendor.ref(null);
    const tipsPopup = common_vendor.ref(null);
    const isLogin = common_vendor.computed(() => {
      return userStore.isLoggedIn;
    });
    const loginUserId = common_vendor.computed(() => {
      return userStore.uid || 0;
    });
    const followBtnClass = common_vendor.computed(() => {
      if (parseInt(userInfo.is_mutual_follow) === 1) {
        return "mutual";
      } else if (isFollowing.value || parseInt(userInfo.is_follow) === 1) {
        return "active";
      } else {
        return "";
      }
    });
    const followBtnText = common_vendor.computed(() => {
      if (parseInt(userInfo.is_mutual_follow) === 1) {
        return "互相关注";
      } else if (isFollowing.value || parseInt(userInfo.is_follow) === 1) {
        return "已关注";
      } else {
        return "＋关注";
      }
    });
    common_vendor.onLoad(async (options) => {
      try {
        if (typeof common_vendor.index.showShareMenu === "function") {
          common_vendor.index.showShareMenu();
        }
      } catch (e) {
        common_vendor.index.__f__("warn", "at pages/user/details.vue:314", "showShareMenu not supported on this platform:", e);
      }
      await instance.appContext.app.config.globalProperties.$onLaunched;
      common_vendor.index.__f__("log", "at pages/user/details.vue:320", "页面onLoad参数:", options);
      try {
        const userIdParam = (options == null ? void 0 : options.id) || (options == null ? void 0 : options.user_id) || (options == null ? void 0 : options.uid) || 0;
        if (userIdParam) {
          userId.value = parseInt(userIdParam);
          if (isNaN(userId.value) || userId.value <= 0) {
            common_vendor.index.__f__("error", "at pages/user/details.vue:331", "用户ID无效:", userIdParam);
            opTipsPopup("用户ID无效", true);
            return;
          }
          const currentLoginUserId = loginUserId.value || common_vendor.index.getStorageSync("uid") || 0;
          const isSelfProfile = userId.value === parseInt(currentLoginUserId);
          common_vendor.index.__f__("log", "at pages/user/details.vue:340", "加载用户ID:", userId.value, "是否自己:", isSelfProfile);
          if (isSelfProfile) {
            barList.value = ["笔记", "赞过"];
          } else {
            barList.value = ["笔记", "赞过"];
          }
          getUserInfo();
          loadDynamicList();
        } else {
          common_vendor.index.__f__("error", "at pages/user/details.vue:353", "缺少用户ID参数");
          opTipsPopup("用户状态异常或已注销！", true);
        }
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/user/details.vue:357", "onLoad异常:", e);
        opTipsPopup("加载用户信息失败", true);
      }
    });
    common_vendor.onUnload(() => {
      clearCarouselTimer();
    });
    const getUserInfo = () => {
      common_vendor.index.__f__("log", "at pages/user/details.vue:371", "开始获取用户信息, userId:", userId.value);
      if (!userId.value || userId.value <= 0) {
        common_vendor.index.__f__("error", "at pages/user/details.vue:375", "获取用户信息失败: 用户ID无效", userId.value);
        opTipsPopup("用户ID无效", true);
        return;
      }
      common_vendor.index.showLoading({
        title: "加载中...",
        mask: true
      });
      api_social.getUserHomepage({
        user_id: userId.value
      }).then((res) => {
        common_vendor.index.__f__("log", "at pages/user/details.vue:390", "获取用户信息成功:", res);
        if (res.data) {
          Object.assign(userInfo, res.data);
          if (!userInfo.id && userInfo.uid) {
            userInfo.id = userInfo.uid;
          }
          if (!userInfo.name && userInfo.nickname) {
            userInfo.name = userInfo.nickname;
          }
          if (!userInfo.intro && userInfo.about_me) {
            userInfo.intro = userInfo.about_me;
          }
          userInfo.is_follow = parseInt(userInfo.is_follow || 0);
          userInfo.is_mutual_follow = parseInt(userInfo.is_mutual_follow || 0);
          isFollowing.value = userInfo.is_follow === 1;
          if (!userInfo.privacy) {
            userInfo.privacy = {
              like: 1,
              follow: 1
            };
          }
          if (res.data.visitors && Array.isArray(res.data.visitors)) {
            userInfo.visitors = res.data.visitors;
            common_vendor.index.__f__("log", "at pages/user/details.vue:428", "获取到访客记录:", res.data.visitors.length, "条");
          } else {
            userInfo.visitors = [];
          }
          if (res.data.vip !== void 0) {
            userInfo.vip = res.data.vip;
            userInfo.vip_id = res.data.vip_id || 0;
            userInfo.vip_icon = res.data.vip_icon || "";
            userInfo.vip_name = res.data.vip_name || "";
            userInfo.vip_status = res.data.vip_status || 2;
            userInfo.svip_open = res.data.svip_open || false;
            userInfo.is_ever_level = res.data.is_ever_level || 0;
            userInfo.is_money_level = res.data.is_money_level || 0;
            userInfo.overdue_time = res.data.overdue_time || 0;
            common_vendor.index.__f__("log", "at pages/user/details.vue:444", "获取到VIP信息:", {
              vip: userInfo.vip,
              vip_name: userInfo.vip_name,
              vip_status: userInfo.vip_status,
              svip_open: userInfo.svip_open
            });
          }
          common_vendor.index.__f__("log", "at pages/user/details.vue:452", "处理后的用户信息:", {
            id: userInfo.id,
            name: userInfo.name,
            is_follow: userInfo.is_follow,
            is_mutual_follow: userInfo.is_mutual_follow,
            isFollowing: isFollowing.value,
            privacy: userInfo.privacy,
            visitorsCount: userInfo.visitors.length,
            vip: userInfo.vip,
            vip_status: userInfo.vip_status
          });
          updateBackgroundImages();
          common_vendor.index.setNavigationBarTitle({
            title: userInfo.name || "用户详情"
          });
        } else {
          common_vendor.index.__f__("error", "at pages/user/details.vue:472", "获取用户信息返回数据为空");
          opTipsPopup("获取用户信息失败", true);
        }
      }).catch((err) => {
        common_vendor.index.__f__("error", "at pages/user/details.vue:476", "获取用户信息失败", err);
        opTipsPopup("获取用户信息失败: " + (err.msg || "网络错误"), true);
      }).finally(() => {
        common_vendor.index.hideLoading();
      });
    };
    const loadDynamicList = () => {
      if (isLoading.value || loadStatus.value === "noMore")
        return;
      isLoading.value = true;
      loadStatus.value = "loading";
      const isLikedTab = barIdx.value === 1;
      const isOwnProfile = userId.value === loginUserId.value;
      let apiCall;
      if (isLikedTab) {
        common_vendor.index.__f__("log", "at pages/user/details.vue:499", "加载点赞动态列表 - 用户ID:", userId.value);
        apiCall = api_social.getLikeDynamicList(userId.value, {
          page: page.value,
          limit: limit.value
        });
      } else {
        common_vendor.index.__f__("log", "at pages/user/details.vue:506", "加载用户动态列表, userId:", userId.value, "barIdx:", barIdx.value);
        apiCall = api_social.getOtherUserDynamicList(userId.value, {
          page: page.value,
          limit: limit.value
        });
      }
      apiCall.then((res) => {
        common_vendor.index.__f__("log", "at pages/user/details.vue:514", "动态列表接口返回:", res);
        const listData = res.data.list || [];
        if (page.value === 1) {
          list.value = listData;
          isEmpty.value = listData.length === 0;
        } else {
          list.value = [...list.value, ...listData];
        }
        loadStatus.value = listData.length < limit.value ? "noMore" : "more";
        page.value++;
        if (page.value === 2 && listData.length > 0) {
          const hasMediaContent = listData.some(
            (item) => item.type === 2 || item.type === 3 || item.images && item.images.length > 0
          );
          isWaterfall.value = hasMediaContent;
        }
      }).catch((err) => {
        common_vendor.index.__f__("error", "at pages/user/details.vue:537", "获取动态列表失败", err);
        loadStatus.value = "more";
        if (isLikedTab && !isOwnProfile) {
          if (err.code === 403 || err.status === 403) {
            opTipsPopup("该用户已将点赞设为私密");
          } else {
            common_vendor.index.showToast({
              title: "加载失败，请重试",
              icon: "none"
            });
          }
        } else {
          common_vendor.index.showToast({
            title: "加载失败，请重试",
            icon: "none"
          });
        }
      }).finally(() => {
        isLoading.value = false;
        common_vendor.index.stopPullDownRefresh();
      });
    };
    const handleFollow = () => {
      if (!isLogin.value) {
        common_vendor.index.navigateTo({
          url: "/pages/login/index"
        });
        return;
      }
      if (isProcessing.value)
        return;
      isProcessing.value = true;
      const targetUserId = parseInt(userId.value);
      if (!targetUserId || targetUserId <= 0) {
        common_vendor.index.showToast({
          title: "获取用户ID失败",
          icon: "none"
        });
        common_vendor.index.__f__("error", "at pages/user/details.vue:586", "关注操作失败: 无效的用户ID", targetUserId);
        isProcessing.value = false;
        return;
      }
      const isMutual = parseInt(userInfo.is_mutual_follow) === 1;
      const isFollowed = parseInt(userInfo.is_follow) === 1 || isMutual;
      common_vendor.index.__f__("log", "at pages/user/details.vue:595", "当前关注状态:", { isFollowed, isMutual, targetUserId });
      userInfo.is_follow = isFollowed ? 0 : 1;
      if (isFollowed) {
        userInfo.is_mutual_follow = 0;
      }
      isFollowing.value = !isFollowed;
      common_vendor.index.showToast({
        title: isFollowed ? "取消关注中..." : "关注中...",
        icon: "none",
        duration: 500
      });
      const params = {
        follow_uid: targetUserId,
        is_follow: isFollowed ? 0 : 1
      };
      common_vendor.index.__f__("log", "at pages/user/details.vue:620", "发送关注请求参数:", JSON.stringify(params));
      api_social.followUser(params).then((res) => {
        common_vendor.index.__f__("log", "at pages/user/details.vue:623", "关注接口返回:", res);
        if (res.status === 200) {
          if (res.data && res.data.is_mutual !== void 0) {
            const isMutual2 = parseInt(res.data.is_mutual) === 1;
            userInfo.is_mutual_follow = isMutual2 ? 1 : 0;
            common_vendor.index.__f__("log", "at pages/user/details.vue:630", "更新互相关注状态:", userInfo.is_mutual_follow);
          }
          if (res.data && res.data.fans_count !== void 0) {
            userInfo.fans_count = res.data.fans_count;
          } else {
            const currentFansCount = parseInt(userInfo.fans_count) || 0;
            userInfo.fans_count = isFollowed ? Math.max(0, currentFansCount - 1) : currentFansCount + 1;
          }
          common_vendor.index.showToast({
            title: isFollowed ? "已取消关注" : "关注成功",
            icon: "none"
          });
        } else {
          userInfo.is_follow = isFollowed ? 1 : 0;
          if (isMutual) {
            userInfo.is_mutual_follow = isMutual ? 1 : 0;
          }
          isFollowing.value = isFollowed;
          common_vendor.index.showToast({
            title: res.msg || "操作失败，请重试",
            icon: "none"
          });
        }
      }).catch((err) => {
        userInfo.is_follow = isFollowed ? 1 : 0;
        if (isMutual) {
          userInfo.is_mutual_follow = isMutual ? 1 : 0;
        }
        isFollowing.value = isFollowed;
        common_vendor.index.showToast({
          title: "网络错误，请重试",
          icon: "none"
        });
        common_vendor.index.__f__("error", "at pages/user/details.vue:671", "关注操作异常:", err);
      }).finally(() => {
        isProcessing.value = false;
      });
    };
    const likeClick = (e) => {
      const { id, isLike } = e;
      common_vendor.index.__f__("log", "at pages/user/details.vue:683", "点赞状态变更", id, isLike);
    };
    const followBack = (e) => {
      const { idx, uid, is_follow, is_mutual } = e;
      common_vendor.index.__f__("log", "at pages/user/details.vue:691", "关注状态回调", { idx, uid, is_follow, is_mutual });
      if (list.value[idx]) {
        list.value[idx].is_follow = is_follow;
        list.value[idx].is_mutual_follow = is_mutual;
        if (list.value[idx].user_info) {
          list.value[idx].user_info.is_follow = is_follow;
          list.value[idx].user_info.is_mutual_follow = is_mutual;
        }
      }
    };
    common_vendor.onPullDownRefresh(() => {
      common_vendor.index.__f__("log", "at pages/user/details.vue:707", "下拉刷新");
      page.value = 1;
      list.value = [];
      isEmpty.value = false;
      loadStatus.value = "loading";
      Promise.all([
        getUserInfo(),
        loadDynamicList()
      ]).finally(() => {
        common_vendor.index.stopPullDownRefresh();
      });
    });
    common_vendor.onReachBottom(() => {
      common_vendor.index.__f__("log", "at pages/user/details.vue:726", "触底加载更多, 当前页:", page.value, "加载状态:", loadStatus.value);
      if (loadStatus.value === "more" && !isLoading.value) {
        loadDynamicList();
      }
    });
    const barClick = (e) => {
      if (isThrottling.value)
        return;
      const newBarIdx = parseInt(e.currentTarget.dataset.idx);
      if (newBarIdx === barIdx.value)
        return;
      isThrottling.value = true;
      barIdx.value = newBarIdx;
      common_vendor.index.__f__("log", "at pages/user/details.vue:745", "切换标签:", barIdx.value, "用户ID:", userId.value, "登录用户ID:", loginUserId.value);
      const isOtherUserLikedTab = barIdx.value === 1 && userId.value !== loginUserId.value;
      if (isOtherUserLikedTab && userInfo.privacy && userInfo.privacy.like === 0) {
        common_vendor.index.__f__("log", "at pages/user/details.vue:750", "该用户的点赞内容不可见");
        isEmpty.value = true;
        isThrottling.value = false;
        return;
      }
      common_vendor.index.showLoading({
        title: "加载中...",
        mask: true
      });
      page.value = 1;
      list.value = [];
      isEmpty.value = false;
      loadStatus.value = "loading";
      isWaterfall.value = false;
      loadDynamicList();
      setTimeout(() => {
        isThrottling.value = false;
        common_vendor.index.hideLoading();
      }, 300);
    };
    const followClick = () => {
      handleFollow();
    };
    const toFollow = (e) => {
      let type = e.currentTarget.dataset.type;
      if (userInfo.privacy.follow == 0) {
        let msg = "由于该用户隐私设置，关注列表不可见";
        if (type == 1) {
          msg = "由于该用户隐私设置，粉丝列表不可见";
        }
        return opTipsPopup(msg);
      }
      common_vendor.index.navigateTo({
        url: "/pages/center/follow?type=" + type + "&id=" + userInfo.id + "&name=" + userInfo.name
      });
    };
    const likePopupClick = (show) => {
      if (!show) {
        likePopup.value.close();
      }
      if (show) {
        likePopup.value.open();
      }
    };
    const navigateToFun = (e) => {
      common_vendor.index.navigateTo({
        url: "/pages/" + e.currentTarget.dataset.url
      });
    };
    const navBack = () => {
      if (getCurrentPages().length > 1) {
        common_vendor.index.navigateBack();
      } else {
        common_vendor.index.switchTab({
          url: "//pages/index/index"
        });
      }
    };
    const opTipsPopup = (msg, back) => {
      tipsTitle.value = msg;
      tipsPopup.value.open();
      setTimeout(function() {
        tipsPopup.value.close();
        if (back) {
          navBack();
        }
      }, 2e3);
    };
    const navigationBarColor = (color) => {
      common_vendor.index.setNavigationBarColor({
        frontColor: color,
        backgroundColor: "#ffffff",
        animation: {
          duration: 400,
          timingFunc: "easeIn"
        }
      });
    };
    const switchBackground = (index) => {
      currentBgIndex.value = index;
      startCarousel();
    };
    const updateBackgroundImages = () => {
      try {
        common_vendor.index.__f__("log", "at pages/user/details.vue:854", "处理用户背景图片数据:", userInfo.home_background);
        backgroundImages.value = [];
        if (userInfo.home_background) {
          let bgImages = [];
          if (typeof userInfo.home_background === "string") {
            try {
              if (userInfo.home_background.trim() === "" || userInfo.home_background.trim() === "[]") {
                bgImages = [];
              } else {
                bgImages = JSON.parse(userInfo.home_background);
                common_vendor.index.__f__("log", "at pages/user/details.vue:871", "解析背景图片JSON成功:", bgImages);
              }
            } catch (error) {
              common_vendor.index.__f__("error", "at pages/user/details.vue:874", "解析背景图片JSON失败:", error);
              if (userInfo.home_background.trim().startsWith("http")) {
                bgImages = [{ url: userInfo.home_background }];
              }
            }
          } else if (Array.isArray(userInfo.home_background)) {
            bgImages = userInfo.home_background;
          }
          backgroundImages.value = bgImages.filter((item) => item && item.url && item.url.trim() !== "");
        }
        if (backgroundImages.value.length === 0) {
          const avatarUrl = userInfo.avatar || "/static/img/default-bg.png";
          backgroundImages.value = [{ url: avatarUrl }];
        }
        common_vendor.index.__f__("log", "at pages/user/details.vue:896", "处理后的背景图片数组:", backgroundImages.value);
        currentBgIndex.value = 0;
        if (backgroundImages.value.length > 1) {
          startCarousel();
        } else {
          clearCarouselTimer();
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/user/details.vue:908", "处理背景图片数据异常:", error);
        backgroundImages.value = [{ url: userInfo.avatar || "/static/img/default-bg.png" }];
        currentBgIndex.value = 0;
        clearCarouselTimer();
      }
    };
    const startCarousel = () => {
      clearCarouselTimer();
      if (backgroundImages.value.length > 1) {
        carouselTimer.value = setInterval(() => {
          nextBackground();
        }, 4e3);
      }
    };
    const clearCarouselTimer = () => {
      if (carouselTimer.value) {
        clearInterval(carouselTimer.value);
        carouselTimer.value = null;
      }
    };
    const nextBackground = () => {
      if (backgroundImages.value.length > 0) {
        currentBgIndex.value = (currentBgIndex.value + 1) % backgroundImages.value.length;
      }
    };
    const retryLoad = () => {
      hasError.value = false;
      errorMessage.value = "";
      page.value = 1;
      list.value = [];
      isEmpty.value = false;
      loadStatus.value = "loading";
      getUserInfo();
      loadDynamicList();
    };
    common_vendor.onPageScroll((e) => {
      let frontColor = "#ffffff";
      let ratio = (e.scrollTop > 180 ? 180 : e.scrollTop) / 180;
      if (ratio >= 1) {
        frontColor = "#000000";
      }
      navbarTrans.value = ratio;
      navigationBarColor(frontColor);
    });
    common_vendor.onShareAppMessage(() => {
      return {
        title: userInfo.name + " 的个人名片",
        imageUrl: userInfo.avatar,
        path: "/pages/user/details?id=" + userInfo.id
      };
    });
    common_vendor.onShareTimeline(() => {
      return {
        title: userInfo.name + " 的个人名片",
        imageUrl: userInfo.avatar,
        query: "id=" + userInfo.id
      };
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.n(navbarTrans.value != 1 ? "xwb" : ""),
        b: navbarTrans.value == 1 ? "/static/img/z.png" : "/static/img/z1.png",
        c: titleBarHeight.value + "px",
        d: common_vendor.o(navBack),
        e: navbarTrans.value == 1
      }, navbarTrans.value == 1 ? {
        f: common_vendor.t(userInfo.name)
      } : {}, {
        g: statusBarHeight.value + "px",
        h: "rgba(255, 255, 255," + navbarTrans.value + ")",
        i: backgroundImages.value.length > 0
      }, backgroundImages.value.length > 0 ? common_vendor.e({
        j: common_vendor.f(backgroundImages.value, (img, index, i0) => {
          return {
            a: "e3f6f2c0-0-" + i0,
            b: common_vendor.p({
              src: img.url,
              mode: "aspectFill"
            }),
            c: index,
            d: index === currentBgIndex.value ? 1 : ""
          };
        }),
        k: backgroundImages.value.length > 1
      }, backgroundImages.value.length > 1 ? {
        l: common_vendor.f(backgroundImages.value, (item, index, i0) => {
          return {
            a: index,
            b: index === currentBgIndex.value ? 1 : "",
            c: common_vendor.o(($event) => switchBackground(index), index)
          };
        })
      } : {}) : {
        m: common_vendor.p({
          src: userInfo.avatar || "/static/img/avatar.png",
          mode: "aspectFill"
        })
      }, {
        n: common_vendor.p({
          src: userInfo.avatar
        }),
        o: common_vendor.t(followBtnText.value),
        p: common_vendor.n(followBtnClass.value),
        q: common_vendor.o(followClick),
        r: common_vendor.t(userInfo.name),
        s: userInfo.intro
      }, userInfo.intro ? {
        t: common_vendor.t(userInfo.intro)
      } : {}, {
        v: userInfo.gender != void 0 || userInfo.sex != void 0
      }, userInfo.gender != void 0 || userInfo.sex != void 0 ? {
        w: userInfo.gender == 1 || userInfo.sex == 1 ? "/static/img/nan.png" : "/static/img/nv.png"
      } : {}, {
        x: userInfo.constellation_label
      }, userInfo.constellation_label ? {
        y: common_vendor.t(userInfo.constellation_label)
      } : userInfo.age && userInfo.age != "暂不展示" ? {
        A: common_vendor.t(userInfo.age)
      } : {}, {
        z: userInfo.age && userInfo.age != "暂不展示",
        B: common_vendor.t(userInfo.province || "未知"),
        C: common_vendor.t(userInfo.follow_count),
        D: common_vendor.o(toFollow),
        E: common_vendor.t(userInfo.fans_count),
        F: common_vendor.o(toFollow),
        G: common_vendor.t(userInfo.like_count_str),
        H: common_vendor.o(($event) => likePopupClick(true)),
        I: statusBarHeight.value + titleBarHeight.value + "px",
        J: userInfo.circle.length
      }, userInfo.circle.length ? {
        K: common_vendor.t(userInfo.name),
        L: common_vendor.f(userInfo.circle, (item, index, i0) => {
          return {
            a: item.avatar,
            b: common_vendor.t(item.name),
            c: index,
            d: "note/circle?id=" + item.id,
            e: common_vendor.o(navigateToFun, index)
          };
        })
      } : {}, {
        M: common_vendor.f(barList.value, (item, index, i0) => {
          return {
            a: common_vendor.t(item),
            b: index == barIdx.value ? "#000" : "#999",
            c: index == barIdx.value ? "28rpx" : "26rpx",
            d: index == barIdx.value ? 1 : 0,
            e: index,
            f: common_vendor.o(barClick, index),
            g: index
          };
        }),
        N: statusBarHeight.value + titleBarHeight.value - 1 + "px",
        O: loadStatus.value == "loading" && !isThrottling.value
      }, loadStatus.value == "loading" && !isThrottling.value ? {
        P: common_vendor.p({
          status: loadStatus.value
        })
      } : {}, {
        Q: hasError.value
      }, hasError.value ? {
        R: common_assets._imports_0$9,
        S: common_vendor.t(errorMessage.value || "网络连接异常，请稍后重试"),
        T: common_vendor.o(retryLoad)
      } : isEmpty.value && !hasError.value ? {
        V: common_vendor.p({
          title: barIdx.value == 0 ? "暂无笔记内容" : "暂无喜欢的内容",
          description: userInfo.name + (barIdx.value == 0 ? " 还没有发布过" : " 还没有点赞"),
          image: "/static/img/empty.png"
        })
      } : barIdx.value == 1 && userInfo.privacy.like == 0 ? {
        X: common_vendor.p({
          title: "点赞内容不可见",
          description: "该用户已将点赞设为私密",
          image: "/static/img/private.png"
        })
      } : list.value.length > 0 ? common_vendor.e({
        Z: isWaterfall.value
      }, isWaterfall.value ? {
        aa: common_vendor.p({
          note: list.value,
          page: page.value
        })
      } : {
        ab: common_vendor.f(list.value, (item, index, i0) => {
          return {
            a: common_vendor.o(likeClick, index),
            b: common_vendor.o(followBack, index),
            c: "e3f6f2c0-7-" + i0,
            d: common_vendor.p({
              item,
              idx: index
            }),
            e: index
          };
        })
      }, {
        ac: loadStatus.value !== "loading"
      }, loadStatus.value !== "loading" ? {
        ad: common_vendor.p({
          status: loadStatus.value
        })
      } : {}, {
        ae: common_vendor.n(isWaterfall.value ? "dynamic-box" : "")
      }) : {}, {
        U: isEmpty.value && !hasError.value,
        W: barIdx.value == 1 && userInfo.privacy.like == 0,
        Y: list.value.length > 0,
        af: common_assets._imports_1$10,
        ag: common_vendor.t(userInfo.name),
        ah: common_vendor.t(userInfo.like_count),
        ai: common_vendor.o(($event) => likePopupClick(false)),
        aj: common_vendor.sr(likePopup, "e3f6f2c0-9", {
          "k": "likePopup"
        }),
        ak: common_vendor.t(tipsTitle.value),
        al: common_vendor.sr(tipsPopup, "e3f6f2c0-10", {
          "k": "tipsPopup"
        }),
        am: common_vendor.p({
          type: "top",
          ["mask-background-color"]: "rgba(0, 0, 0, 0)"
        })
      });
    };
  }
};
_sfc_main.__runtimeHooks = 7;
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/user/details.js.map
