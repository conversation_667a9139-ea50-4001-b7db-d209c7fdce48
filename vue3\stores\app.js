// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2023 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------

import { defineStore } from 'pinia'

export const useAppStore = defineStore('app', {
  state: () => ({
    // 系统信息
    systemInfo: {
      statusBarHeight: uni.getSystemInfoSync().statusBarHeight || 20,
      titleBarHeight: 44,
      screenWidth: uni.getSystemInfoSync().screenWidth,
      screenHeight: uni.getSystemInfoSync().screenHeight,
      platform: uni.getSystemInfoSync().platform
    },
    
    // 应用配置
    config: {
      version: '1.0.0',
      apiBaseUrl: '',
      cdnBaseUrl: '',
      debug: false
    },
    
    // 主题设置
    theme: {
      mode: 'light', // light | dark
      primaryColor: '#007AFF',
      backgroundColor: '#F8F8F8'
    },
    
    // 网络状态
    networkStatus: {
      isConnected: true,
      networkType: 'wifi'
    },
    
    // 加载状态
    loading: {
      global: false,
      page: false,
      component: {}
    },
    
    // 缓存管理
    cache: {
      timeout: 300000, // 5分钟
      maxSize: 50, // 最大缓存条数
      data: new Map()
    },
    
    // 热搜词
    hotWords: [],
    
    // 首页数据缓存
    indexData: {
      banner: [],
      categories: [],
      products: [],
      timestamp: 0
    },
    
    // 侧边栏菜单
    sidebarMenu: [],
    
    // 键盘高度（用于适配）
    keyboardHeight: 0
  }),

  getters: {
    // 是否为暗黑模式
    isDarkMode: (state) => state.theme.mode === 'dark',
    
    // 是否为移动端
    isMobile: (state) => ['ios', 'android'].includes(state.systemInfo.platform),
    
    // 安全区域高度
    safeAreaHeight: (state) => {
      return state.systemInfo.statusBarHeight + state.systemInfo.titleBarHeight
    },
    
    // 是否有网络连接
    isOnline: (state) => state.networkStatus.isConnected,
    
    // 全局是否加载中
    isGlobalLoading: (state) => state.loading.global,
    
    // 缓存数据大小
    cacheSize: (state) => state.cache.data.size,
    
    // 是否有热搜词
    hasHotWords: (state) => state.hotWords.length > 0,
    
    // 首页数据是否有效
    isIndexDataValid: (state) => {
      const now = Date.now()
      return state.indexData.timestamp && 
             (now - state.indexData.timestamp < state.cache.timeout)
    }
  },

  actions: {
    // 更新系统信息
    updateSystemInfo() {
      const systemInfo = uni.getSystemInfoSync()
      this.systemInfo = {
        statusBarHeight: systemInfo.statusBarHeight || 20,
        titleBarHeight: 44,
        screenWidth: systemInfo.screenWidth,
        screenHeight: systemInfo.screenHeight,
        platform: systemInfo.platform
      }
    },

    // 设置主题
    setTheme(theme) {
      this.theme = { ...this.theme, ...theme }
      
      // 同步到本地存储
      uni.setStorageSync('app_theme', this.theme)
    },

    // 切换主题模式
    toggleThemeMode() {
      this.theme.mode = this.theme.mode === 'light' ? 'dark' : 'light'
      this.setTheme(this.theme)
    },

    // 更新网络状态
    updateNetworkStatus(status) {
      this.networkStatus = { ...this.networkStatus, ...status }
    },

    // 设置全局加载状态
    setGlobalLoading(loading) {
      this.loading.global = loading
    },

    // 设置页面加载状态
    setPageLoading(loading) {
      this.loading.page = loading
    },

    // 设置组件加载状态
    setComponentLoading(componentId, loading) {
      this.loading.component[componentId] = loading
    },

    // 获取组件加载状态
    getComponentLoading(componentId) {
      return this.loading.component[componentId] || false
    },

    // 缓存数据
    setCache(key, data, timeout = null) {
      const expireTime = timeout || this.cache.timeout
      const cacheItem = {
        data,
        timestamp: Date.now(),
        expireTime
      }
      
      // 检查缓存大小，超出限制则清理最旧的数据
      if (this.cache.data.size >= this.cache.maxSize) {
        const firstKey = this.cache.data.keys().next().value
        this.cache.data.delete(firstKey)
      }
      
      this.cache.data.set(key, cacheItem)
    },

    // 获取缓存数据
    getCache(key) {
      const cacheItem = this.cache.data.get(key)
      if (!cacheItem) return null
      
      const now = Date.now()
      if (now - cacheItem.timestamp > cacheItem.expireTime) {
        this.cache.data.delete(key)
        return null
      }
      
      return cacheItem.data
    },

    // 清除缓存
    clearCache(key = null) {
      if (key) {
        this.cache.data.delete(key)
      } else {
        this.cache.data.clear()
      }
    },

    // 清理过期缓存
    cleanExpiredCache() {
      const now = Date.now()
      for (const [key, item] of this.cache.data.entries()) {
        if (now - item.timestamp > item.expireTime) {
          this.cache.data.delete(key)
        }
      }
    },

    // 设置热搜词
    setHotWords(words) {
      this.hotWords = words
    },

    // 设置首页数据
    setIndexData(data) {
      this.indexData = {
        ...data,
        timestamp: Date.now()
      }
    },

    // 设置侧边栏菜单
    setSidebarMenu(menu) {
      this.sidebarMenu = menu
    },

    // 设置键盘高度
    setKeyboardHeight(height) {
      this.keyboardHeight = height
    },

    // 从本地存储初始化
    initFromStorage() {
      try {
        // 恢复主题设置
        const theme = uni.getStorageSync('app_theme')
        if (theme) {
          this.theme = { ...this.theme, ...theme }
        }
        
        // 恢复其他设置...
      } catch (error) {
        console.warn('从本地存储初始化应用状态失败:', error)
      }
    },

    // 重置应用状态
    reset() {
      this.loading = {
        global: false,
        page: false,
        component: {}
      }
      this.clearCache()
      this.hotWords = []
      this.indexData = {
        banner: [],
        categories: [],
        products: [],
        timestamp: 0
      }
      this.sidebarMenu = []
      this.keyboardHeight = 0
    }
  }
})
