"use strict";
const common_vendor = require("../../common/vendor.js");
const api_user = require("../../api/user.js");
const api_api = require("../../api/api.js");
const libs_login = require("../../libs/login.js");
const libs_routine = require("../../libs/routine.js");
const utils_cache = require("../../utils/cache.js");
const mixins_color = require("../../mixins/color.js");
const common_assets = require("../../common/assets.js");
const navbar = () => "../../components/navbar/navbar.js";
const app = getApp();
const _sfc_main = {
  components: {
    navbar
  },
  mixins: [mixins_color.colors],
  data() {
    return {
      statusBarHeight: 0,
      titleBarHeight: 0,
      appBq: [],
      userInfo: {},
      // 用户信息
      fileSizeString: "",
      // 缓存大小
      version: "",
      // 版本号
      loginType: "h5",
      // 登录类型
      userIndex: 0,
      // 当前账号索引
      switchUserInfo: []
      // 账号切换列表
    };
  },
  computed: common_vendor.mapGetters(["isLogin"]),
  watch: {
    isLogin: {
      handler: function(newV, oldV) {
        if (newV) {
          this.getUserInfo();
        }
      },
      deep: true
    }
  },
  onLoad() {
    var _a, _b;
    let that = this;
    that.statusBarHeight = ((_a = app.globalData) == null ? void 0 : _a.statusBarHeight) || common_vendor.index.getSystemInfoSync().statusBarHeight || 20;
    that.titleBarHeight = ((_b = app.globalData) == null ? void 0 : _b.titleBarHeight) || 44;
    if (this.isLogin) {
      that.getConfigData();
      that.getUserInfo();
    } else {
      libs_login.toLogin();
    }
  },
  onUnload() {
  },
  onShow() {
  },
  methods: {
    // 获取用户信息 (参考用户页面)
    getUserInfo: function() {
      let that = this;
      if (!this.isLogin) {
        common_vendor.index.__f__("log", "at pages/setting/index.vue:314", "用户未登录");
        that.showToast("请先登录");
        return;
      }
      const token = this.$store.state.app.token;
      common_vendor.index.__f__("log", "at pages/setting/index.vue:321", "当前token:", token);
      if (!token) {
        common_vendor.index.__f__("log", "at pages/setting/index.vue:324", "没有token");
        that.showToast("登录已过期，请重新登录");
        return;
      }
      api_user.getUserInfo().then((res) => {
        common_vendor.index.__f__("log", "at pages/setting/index.vue:330", "getUserInfo success:", res);
        that.$set(that, "userInfo", res.data);
        let switchUserInfo = res.data.switchUserInfo || [];
        for (let i = 0; i < switchUserInfo.length; i++) {
          if (switchUserInfo[i].uid == that.userInfo.uid)
            that.userIndex = i;
        }
        that.$set(that, "switchUserInfo", switchUserInfo);
      }).catch((err) => {
        common_vendor.index.__f__("error", "at pages/setting/index.vue:349", "获取用户信息失败:", err);
        that.showToast("获取用户信息失败: " + (err.msg || err.message || "网络错误"));
      });
    },
    // 获取配置数据
    getConfigData() {
      let that = this;
      api_api.siteConfig().then((res) => {
        if (res.code == 200) {
          if (res.data.app_xcx) {
            app.globalData.appXx = res.data.app_xcx;
          }
          if (res.data.app_bq) {
            app.globalData.appBq = res.data.app_bq;
            that.appBq = res.data.app_bq;
            that.appBq[4] = that.appBq[4] ? that.appBq[4] : "https://example.com";
          }
          if (res.data.upload_type) {
            app.globalData.uploadType = res.data.upload_type;
          }
        } else {
          common_vendor.index.__f__("warn", "at pages/setting/index.vue:374", "获取配置数据失败，使用默认配置");
        }
      }).catch((err) => {
        common_vendor.index.__f__("warn", "at pages/setting/index.vue:377", "获取配置数据失败:", err);
      });
    },
    // 账号切换功能
    switchAccounts: function(index) {
      let userInfo = this.switchUserInfo[index], that = this;
      that.userIndex = index;
      if (that.switchUserInfo.length <= 1)
        return true;
      if (userInfo === void 0)
        return that.showToast("切换的账号不存在");
      if (userInfo.user_type === "h5") {
        common_vendor.index.showLoading({
          title: "正在切换中"
        });
        api_api.switchH5Login().then((res) => {
          common_vendor.index.hideLoading();
          that.$store.commit("LOGIN", {
            "token": res.data.token,
            "time": utils_cache.Cache.strTotime(res.data.expires_time) - utils_cache.Cache.time()
          });
          that.getUserInfo();
        }).catch((err) => {
          common_vendor.index.hideLoading();
          return that.showToast(err);
        });
      } else {
        that.$store.commit("LOGOUT");
        common_vendor.index.showLoading({
          title: "正在切换中"
        });
        libs_login.toLogin();
      }
    },
    // 导航到指定页面
    navigateToFun(e) {
      let url = "/pages/" + e.currentTarget.dataset.url;
      common_vendor.index.navigateTo({
        url
      });
    },
    // 退出登录
    outLogin: function() {
      let that = this;
      common_vendor.index.showModal({
        title: "提示",
        content: "确认退出登录？",
        success: function(res) {
          if (res.confirm) {
            api_user.getLogout().then((res2) => {
              that.$store.commit("LOGOUT");
              common_vendor.index.reLaunch({
                url: "//pages/index/index"
              });
            }).catch((err) => {
              that.$store.commit("LOGOUT");
              common_vendor.index.reLaunch({
                url: "//pages/index/index"
              });
            });
          }
        }
      });
    },
    // 微信小程序获取手机号
    getphonenumber(e) {
      if (e.detail.errMsg == "getPhoneNumber:ok") {
        libs_routine.Routine.getCode().then((code) => {
          let data = {
            code,
            iv: e.detail.iv,
            encryptedData: e.detail.encryptedData
          };
          api_user.mpBindingPhone(data).then((res) => {
            this.getUserInfo();
            this.showToast({
              title: res.msg,
              icon: "success"
            });
          }).catch((err) => {
            return this.showToast(err);
          });
        }).catch((error) => {
          common_vendor.index.hideLoading();
        });
      }
    },
    isWeixinEnv() {
      return false;
    },
    // 通用提示函数
    showToast(options) {
      if (typeof options === "string") {
        common_vendor.index.showToast({
          title: options,
          icon: "none",
          duration: 2e3
        });
      } else {
        const { title, icon = "none", success } = options;
        common_vendor.index.showToast({
          title,
          icon,
          duration: 2e3,
          success
        });
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_0$15,
    b: common_assets._imports_1$4,
    c: common_vendor.o((...args) => $options.navigateToFun && $options.navigateToFun(...args)),
    d: common_assets._imports_2$12,
    e: !$data.userInfo.phone
  }, !$data.userInfo.phone ? {
    f: common_vendor.o((...args) => $options.getphonenumber && $options.getphonenumber(...args))
  } : {
    g: common_vendor.t($data.userInfo.phone)
  }, {
    h: $data.userInfo.phone
  }, $data.userInfo.phone ? {
    i: common_assets._imports_3$13,
    j: common_assets._imports_1$4,
    k: common_vendor.o((...args) => $options.navigateToFun && $options.navigateToFun(...args))
  } : {}, {
    l: $data.switchUserInfo.length > 1
  }, $data.switchUserInfo.length > 1 ? {
    m: common_vendor.f($data.switchUserInfo, (item, index, i0) => {
      return common_vendor.e({
        a: item.avatar,
        b: common_vendor.t(item.nickname),
        c: common_vendor.t(item.phone),
        d: index != $data.userIndex
      }, index != $data.userIndex ? {} : {}, {
        e: common_vendor.n(index == $data.userIndex ? "on" : ""),
        f: index,
        g: common_vendor.o(($event) => $options.switchAccounts(index), index)
      });
    })
  } : {}, {
    n: common_assets._imports_4$5,
    o: common_assets._imports_1$4,
    p: common_vendor.o((...args) => $options.navigateToFun && $options.navigateToFun(...args)),
    q: common_assets._imports_5$8,
    r: common_assets._imports_1$4,
    s: common_vendor.o((...args) => $options.navigateToFun && $options.navigateToFun(...args)),
    t: $data.userInfo.invioce_func
  }, $data.userInfo.invioce_func ? {
    v: common_assets._imports_6$2,
    w: common_assets._imports_1$4,
    x: common_vendor.o((...args) => $options.navigateToFun && $options.navigateToFun(...args))
  } : {}, {
    y: common_assets._imports_7,
    z: common_assets._imports_1$4,
    A: common_vendor.o((...args) => $options.navigateToFun && $options.navigateToFun(...args)),
    B: common_assets._imports_0$15,
    C: common_assets._imports_1$4,
    D: common_assets._imports_8$5,
    E: common_assets._imports_1$4,
    F: common_vendor.o((...args) => $options.navigateToFun && $options.navigateToFun(...args)),
    G: common_assets._imports_9$2,
    H: common_assets._imports_1$4,
    I: common_vendor.o((...args) => $options.navigateToFun && $options.navigateToFun(...args)),
    J: common_assets._imports_10$3,
    K: common_assets._imports_1$4,
    L: common_vendor.o((...args) => $options.navigateToFun && $options.navigateToFun(...args)),
    M: common_assets._imports_3$13,
    N: common_vendor.o((...args) => $options.outLogin && $options.outLogin(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/setting/index.js.map
