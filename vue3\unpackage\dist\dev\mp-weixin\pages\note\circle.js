"use strict";
const common_vendor = require("../../common/vendor.js");
const api_social = require("../../api/social.js");
const common_assets = require("../../common/assets.js");
const lazyImage = () => "../../components/lazyImage/lazyImage.js";
const uniLoadMore = () => "../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js";
const waterfall = () => "../../components/waterfall/waterfall.js";
const cardGg = () => "../../components/card-gg/card-gg.js";
const shareComponent = () => "../../components/share/index.js";
const app = getApp();
const _sfc_main = {
  components: {
    lazyImage,
    uniLoadMore,
    waterfall,
    cardGg,
    shareComponent
  },
  data() {
    return {
      statusBarHeight: this.$store.state.statusBarHeight || 20,
      titleBarHeight: this.$store.state.titleBarHeight || 44,
      navbarTrans: 0,
      barList: ["推荐", "最新", "规则"],
      barIdx: 0,
      circleId: 0,
      circleInfo: {
        id: 0,
        circle_name: "",
        circle_avatar: "",
        circle_background: "",
        circle_description: "",
        circle_notice: "",
        member_count: 0,
        dynamic_count: 0,
        view_count: 0,
        is_joined: false,
        user_role: 0,
        recent_members: []
      },
      list: [],
      page: 1,
      limit: 10,
      totalCount: 0,
      isThrottling: true,
      isEmpty: false,
      loadStatus: "more",
      tipsTitle: "",
      isWaterfall: false,
      showLoading: true,
      // 初始状态设为加载中
      loadingTimer: null,
      debounceTimer: null,
      showShare: false,
      userId: 0,
      adminMembers: [],
      // 管理团队成员列表
      // 缓存相关
      cacheKey: "",
      lastUpdateTime: 0
    };
  },
  computed: {
    // 圈子分享信息，转换为分享组件需要的格式
    circleShareInfo() {
      return {
        id: this.circleInfo.id || this.circleId,
        uid: this.circleInfo.creator_id || this.circleInfo.uid,
        // 圈主ID
        title: this.circleInfo.circle_name || this.circleInfo.name,
        content: this.circleInfo.circle_description || this.circleInfo.intro,
        image: this.circleInfo.circle_avatar,
        type: "circle",
        // 标识这是圈子类型
        share_url: `/pages/note/circle?id=${this.circleId}`
      };
    }
  },
  onLoad(options) {
    common_vendor.index.showShareMenu();
    const userInfo = common_vendor.index.getStorageSync("userInfo");
    const vuexUid = this.$store.state.app.uid;
    this.userId = vuexUid || userInfo && userInfo.uid || 0;
    if (options.id) {
      this.circleId = parseInt(options.id);
      this.circleInfo.id = this.circleId;
      this.cacheKey = `circle_${this.circleId}`;
      this.checkCache();
      this.getCircleDetail();
    } else {
      this.opTipsPopup("圈子ID不存在", true);
    }
  },
  methods: {
    // 检查缓存
    checkCache() {
      try {
        const cached = common_vendor.index.getStorageSync(this.cacheKey);
        const now = Date.now();
        if (cached && cached.timestamp && now - cached.timestamp < 5 * 60 * 1e3) {
          this.circleInfo = { ...this.circleInfo, ...cached.data };
          this.lastUpdateTime = cached.timestamp;
        }
      } catch (e) {
        common_vendor.index.__f__("warn", "at pages/note/circle.vue:349", "读取缓存失败:", e);
      }
    },
    // 更新缓存
    updateCache(data) {
      try {
        const cacheData = {
          data,
          timestamp: Date.now()
        };
        common_vendor.index.setStorageSync(this.cacheKey, cacheData);
        this.lastUpdateTime = cacheData.timestamp;
      } catch (e) {
        common_vendor.index.__f__("warn", "at pages/note/circle.vue:363", "更新缓存失败:", e);
      }
    },
    // 预览圈子背景图片
    previewCircleImage() {
      if (this.circleInfo.circle_background || this.circleInfo.circle_avatar) {
        const imageUrl = this.circleInfo.circle_background || this.circleInfo.circle_avatar;
        common_vendor.index.previewImage({
          current: imageUrl,
          urls: [imageUrl]
        });
      }
    },
    // 获取圈子详情
    async getCircleDetail() {
      try {
        this.showLoading = true;
        const res = await api_social.getCircleDetail(this.circleId);
        if (res.status === 200 && res.data) {
          const newCircleInfo = {
            ...this.circleInfo,
            ...res.data,
            id: res.data.id || this.circleId
          };
          this.circleInfo = newCircleInfo;
          this.updateCache(newCircleInfo);
          await Promise.all([
            this.getCircleDynamic(),
            this.getAdminMembers()
          ]);
        } else {
          this.opTipsPopup(res.msg || "获取圈子详情失败", true);
        }
      } catch (err) {
        common_vendor.index.__f__("error", "at pages/note/circle.vue:406", "获取圈子详情失败:", err);
        this.handleError(err, "网络错误，请稍后重试");
        setTimeout(() => {
          common_vendor.index.navigateBack();
        }, 2e3);
      } finally {
        this.showLoading = false;
      }
    },
    // 获取圈子动态
    async getCircleDynamic() {
      this.isThrottling = false;
      this.loadStatus = "loading";
      if (this.loadingTimer) {
        clearTimeout(this.loadingTimer);
      }
      this.loadingTimer = setTimeout(() => {
        if (this.loadStatus === "loading") {
          this.showLoading = true;
        }
      }, 300);
      const params = {
        page: this.page,
        limit: this.limit,
        circle_id: this.circleId,
        type: this.barIdx === 0 ? "recommend" : "latest",
        // 0=推荐, 1=最新
        uid: this.userId
        // 传递用户ID用于获取点赞和关注状态
      };
      try {
        const res = await api_social.getCircleDynamicList(params);
        if (res.status === 200 && res.data) {
          const responseData = res.data;
          if (responseData.list && responseData.list.length > 0) {
            const formattedList = responseData.list.map((item) => ({
              ...item,
              circle_id: this.circleId,
              circle_name: this.circleInfo.circle_name,
              circle_avatar: this.circleInfo.circle_avatar
            }));
            if (this.page == 1) {
              this.list = formattedList;
            } else {
              this.list = this.list.concat(formattedList);
            }
            if (responseData.count !== void 0) {
              this.totalCount = responseData.count;
            }
            this.isEmpty = false;
          } else if (this.page == 1) {
            this.isEmpty = true;
            this.list = [];
          }
          if (this.list.length >= this.totalCount && this.list.length > 0) {
            this.loadStatus = "noMore";
          }
        } else {
          if (this.page == 1) {
            this.isEmpty = true;
            this.list = [];
          }
        }
      } catch (err) {
        if (this.page == 1) {
          this.isEmpty = true;
          this.list = [];
        }
        common_vendor.index.__f__("error", "at pages/note/circle.vue:493", "获取圈子动态失败:", err);
        if (this.page === 1) {
          this.handleError(err, "获取圈子动态失败，请稍后重试");
        }
      } finally {
        this.isThrottling = true;
        this.loadStatus = "more";
        if (this.loadingTimer) {
          clearTimeout(this.loadingTimer);
          this.loadingTimer = null;
        }
        this.showLoading = false;
      }
    },
    // 获取管理团队成员
    async getAdminMembers() {
      try {
        const res = await api_social.getCircleMemberList({
          circle_id: this.circleId,
          page: 1,
          limit: 50
        });
        if (res.status === 200 && res.data && res.data.list) {
          const adminList = res.data.list.filter((member) => member.role_type === 2 || member.role_type === 3);
          this.adminMembers = adminList.sort((a, b) => {
            if (a.role_type === 3 && b.role_type === 2)
              return -1;
            if (a.role_type === 2 && b.role_type === 3)
              return 1;
            return 0;
          });
        }
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/note/circle.vue:535", "获取管理团队成员失败:", e);
        this.handleError(e, "获取管理团队信息失败");
      }
    },
    // 加入/退出圈子
    async handleJoinCircle() {
      const userInfo = common_vendor.index.getStorageSync("userInfo");
      const vuexUid = this.$store.state.app.uid;
      const currentUserId = vuexUid || userInfo && userInfo.uid || 0;
      if (!currentUserId) {
        this.opTipsPopup("请先登录");
        return;
      }
      try {
        common_vendor.index.showLoading({
          title: this.circleInfo.is_joined ? "退出中..." : "加入中...",
          mask: true
        });
        const apiMethod = this.circleInfo.is_joined ? api_social.exitCircle : api_social.joinCircle;
        const params = {
          circle_id: this.circleId
        };
        const res = await apiMethod(params);
        if (res.status === 200) {
          this.circleInfo.is_joined = !this.circleInfo.is_joined;
          if (this.circleInfo.is_joined) {
            this.circleInfo.member_count += 1;
            this.opTipsPopup("已成功加入圈子");
          } else {
            this.circleInfo.member_count = Math.max(0, this.circleInfo.member_count - 1);
            this.opTipsPopup("已退出圈子");
          }
          app.globalData.isCenterPage = true;
        } else {
          this.opTipsPopup(res.msg || "操作失败，请重试");
        }
      } catch (err) {
        common_vendor.index.__f__("error", "at pages/note/circle.vue:584", "圈子操作失败:", err);
        this.handleError(err, "网络错误，请稍后重试");
      } finally {
        common_vendor.index.hideLoading();
      }
    },
    // 切换分类标签
    barClick(e) {
      const clickIdx = parseInt(e.currentTarget.dataset.idx);
      if (clickIdx === this.barIdx)
        return;
      if (this.debounceTimer) {
        clearTimeout(this.debounceTimer);
      }
      this.barIdx = clickIdx;
      if (clickIdx < this.barList.length - 1) {
        this.debounceTimer = setTimeout(() => {
          this.isThrottling = false;
          this.page = 1;
          this.loadStatus = "loading";
          this.list = [];
          this.getCircleDynamic();
        }, 100);
      }
    },
    goToAllMembers() {
      common_vendor.index.navigateTo({ url: "/pages/note/circlemember?id=" + this.circleId });
    },
    // 跳转到用户主页
    goToUserProfile(uid) {
      common_vendor.index.navigateTo({
        url: `/pages/user/details?id=${uid}`
      });
    },
    // 格式化加入时间
    formatJoinTime(timeStr) {
      if (!timeStr)
        return "";
      const date = new Date(timeStr);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      return `${year}-${month}-${day}`;
    },
    // 分享控制
    shareClick(isOpen) {
      this.showShare = isOpen;
    },
    // 关闭分享弹窗
    closeShare() {
      this.showShare = false;
    },
    // 处理分享事件
    handleShare(type) {
      common_vendor.index.__f__("log", "at pages/note/circle.vue:646", "分享圈子:", type, this.circleInfo);
      common_vendor.index.showToast({
        title: "分享成功",
        icon: "success"
      });
      this.showShare = false;
    },
    // 处理不感兴趣
    handleDislike(circleId) {
      common_vendor.index.__f__("log", "at pages/note/circle.vue:656", "标记圈子不感兴趣:", circleId);
      this.showShare = false;
    },
    // 处理举报
    handleReport(reportData) {
      common_vendor.index.__f__("log", "at pages/note/circle.vue:662", "举报圈子:", reportData);
      common_vendor.index.showToast({
        title: "举报成功",
        icon: "success"
      });
      this.showShare = false;
    },
    // 处理编辑（如果是圈主）
    handleEdit(circleId) {
      common_vendor.index.__f__("log", "at pages/note/circle.vue:672", "编辑圈子:", circleId);
      common_vendor.index.navigateTo({
        url: `/pages/circle/edit?id=${circleId}`
      });
      this.showShare = false;
    },
    // 处理删除（如果是圈主）
    handleDelete(circleId) {
      common_vendor.index.__f__("log", "at pages/note/circle.vue:682", "删除圈子:", circleId);
      common_vendor.index.showModal({
        title: "确认删除",
        content: "确定要删除这个圈子吗？删除后不可恢复。",
        confirmColor: "#FA5150",
        success: (res) => {
          if (res.confirm) {
            this.deleteCircle(circleId);
          }
        }
      });
      this.showShare = false;
    },
    // 删除圈子
    deleteCircle() {
      common_vendor.index.showLoading({
        title: "删除中...",
        mask: true
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "删除成功",
          icon: "success"
        });
        setTimeout(() => {
          this.navBack();
        }, 1500);
      }, 1e3);
    },
    // 点赞回调
    likeClick(e) {
      if (!e || !this.list || !this.list[e.idx])
        return;
      this.list[e.idx].is_like = e.is_like;
      this.list[e.idx].like_count = e.like_count;
      this.list[e.idx].like_count_str = String(e.like_count);
    },
    // 返回上一页
    navBack() {
      if (getCurrentPages().length > 1) {
        common_vendor.index.navigateBack();
      } else {
        common_vendor.index.switchTab({
          url: "//pages/index/index"
        });
      }
    },
    // 统一错误处理方法
    handleError(error, defaultMessage = "操作失败") {
      var _a, _b, _c;
      common_vendor.index.__f__("error", "at pages/note/circle.vue:755", "错误处理:", error);
      let message = defaultMessage;
      if (typeof error === "string") {
        message = error;
      } else if (error && typeof error === "object") {
        if (error.code === "NETWORK_ERROR" || ((_a = error.message) == null ? void 0 : _a.includes("Network"))) {
          message = "网络连接异常，请检查网络设置";
        } else if (error.code === "TIMEOUT" || ((_b = error.message) == null ? void 0 : _b.includes("timeout"))) {
          message = "请求超时，请稍后重试";
        } else {
          message = error.msg || error.message || ((_c = error.data) == null ? void 0 : _c.msg) || defaultMessage;
        }
      }
      this.opTipsPopup(message);
      return message;
    },
    // 提示弹窗
    opTipsPopup(msg, isBack = false) {
      let that = this;
      that.tipsTitle = msg;
      that.$refs.tipsPopup.open();
      setTimeout(function() {
        that.$refs.tipsPopup.close();
        if (isBack) {
          that.navBack();
        }
      }, 2e3);
    },
    // 刷新列表数据
    fetchList() {
      this.page = 1;
      this.getCircleDynamic();
    },
    onCardUpdate({ vote_info, idx }) {
      if (this.list[idx]) {
        this.$set(this.list[idx], "vote_info", vote_info);
      }
    }
  },
  onReachBottom() {
    if (this.loadStatus === "loading") {
      return;
    }
    if (this.isThrottling && this.list.length && this.list.length < this.totalCount) {
      this.page = this.page + 1;
      this.loadStatus = "loading";
      this.getCircleDynamic();
    } else if (this.list.length >= this.totalCount && this.list.length > 0) {
      this.loadStatus = "noMore";
    }
  },
  onPageScroll(e) {
    const scrollTop = e.scrollTop > 150 ? 150 : e.scrollTop;
    this.navbarTrans = scrollTop / 150;
  },
  onUnload() {
    if (this.loadingTimer) {
      clearTimeout(this.loadingTimer);
      this.loadingTimer = null;
    }
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
      this.debounceTimer = null;
    }
  },
  // 分享到微信好友
  onShareAppMessage() {
    return {
      title: this.circleInfo.circle_name || "圈子分享",
      path: "/pages/note/circle?id=" + this.circleId,
      imageUrl: this.circleInfo.circle_avatar || "/static/img/avatar.png"
    };
  },
  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: this.circleInfo.circle_name || "圈子分享",
      query: "id=" + this.circleId,
      imageUrl: this.circleInfo.circle_avatar || "/static/img/avatar.png"
    };
  }
};
if (!Array) {
  const _component_lazy_image = common_vendor.resolveComponent("lazy-image");
  const _component_waterfall = common_vendor.resolveComponent("waterfall");
  const _component_card_gg = common_vendor.resolveComponent("card-gg");
  const _component_share_component = common_vendor.resolveComponent("share-component");
  const _easycom_uni_popup2 = common_vendor.resolveComponent("uni-popup");
  (_component_lazy_image + _component_waterfall + _component_card_gg + _component_share_component + _easycom_uni_popup2)();
}
const _easycom_uni_popup = () => "../../uni_modules/uni-popup/components/uni-popup/uni-popup.js";
if (!Math) {
  _easycom_uni_popup();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_0$7,
    b: $data.titleBarHeight + "px",
    c: common_vendor.o((...args) => $options.navBack && $options.navBack(...args)),
    d: $data.navbarTrans == 1
  }, $data.navbarTrans == 1 ? {
    e: common_vendor.t($data.circleInfo.circle_name || $data.circleInfo.name)
  } : {}, {
    f: $data.statusBarHeight + "px",
    g: "rgba(255, 255, 255," + $data.navbarTrans + ")",
    h: $data.circleInfo.circle_background || $data.circleInfo.circle_avatar
  }, $data.circleInfo.circle_background || $data.circleInfo.circle_avatar ? {
    i: common_vendor.o($options.previewCircleImage),
    j: common_vendor.p({
      src: $data.circleInfo.circle_background || $data.circleInfo.circle_avatar,
      mode: "aspectFill"
    })
  } : {}, {
    k: !$data.showLoading
  }, !$data.showLoading ? {
    l: $data.circleInfo.circle_avatar || "/static/img/avatar.png"
  } : {}, {
    m: !$data.showLoading
  }, !$data.showLoading ? {
    n: common_vendor.t($data.circleInfo.circle_name || $data.circleInfo.name || "圈子加载中...")
  } : {}, {
    o: !$data.showLoading
  }, !$data.showLoading ? {
    p: common_vendor.t($data.circleInfo.dynamic_count || 0)
  } : {}, {
    q: !$data.showLoading
  }, !$data.showLoading ? {} : {}, {
    r: !$data.showLoading
  }, !$data.showLoading ? {
    s: common_vendor.t($data.circleInfo.member_count || 0)
  } : {}, {
    t: !$data.showLoading && $data.circleInfo.view_count
  }, !$data.showLoading && $data.circleInfo.view_count ? {} : {}, {
    v: !$data.showLoading && $data.circleInfo.view_count
  }, !$data.showLoading && $data.circleInfo.view_count ? {
    w: common_vendor.t($data.circleInfo.view_count)
  } : {}, {
    x: $data.showLoading
  }, $data.showLoading ? {} : {}, {
    y: !$data.showLoading && ($data.circleInfo.circle_description || $data.circleInfo.intro)
  }, !$data.showLoading && ($data.circleInfo.circle_description || $data.circleInfo.intro) ? {
    z: common_vendor.t($data.circleInfo.circle_description || $data.circleInfo.intro)
  } : {}, {
    A: !$data.showLoading && $data.circleInfo.circle_notice
  }, !$data.showLoading && $data.circleInfo.circle_notice ? {
    B: common_vendor.t($data.circleInfo.circle_notice)
  } : {}, {
    C: $data.showLoading
  }, $data.showLoading ? {} : common_vendor.e({
    D: !$data.circleInfo.is_joined && $data.circleInfo.recent_members && $data.circleInfo.recent_members.length > 0
  }, !$data.circleInfo.is_joined && $data.circleInfo.recent_members && $data.circleInfo.recent_members.length > 0 ? {
    E: common_vendor.f($data.circleInfo.recent_members.slice(0, 3), (member, index, i0) => {
      return {
        a: member.avatar || "/static/img/avatar.png",
        b: index
      };
    })
  } : {}, {
    F: common_vendor.t($data.circleInfo.is_joined ? "已加入" : "加入圈子"),
    G: common_vendor.o((...args) => $options.handleJoinCircle && $options.handleJoinCircle(...args)),
    H: common_vendor.n($data.circleInfo.is_joined ? "joined-state" : ""),
    I: common_assets._imports_2$7,
    J: $data.circleInfo.is_joined,
    K: common_vendor.o(($event) => $options.shareClick(true)),
    L: common_vendor.n($data.circleInfo.is_joined ? "share-expanded" : "")
  }), {
    M: $data.statusBarHeight + $data.titleBarHeight + 40 + "rpx",
    N: common_vendor.f($data.barList, (item, index, i0) => {
      return {
        a: common_vendor.t(item),
        b: index == $data.barIdx ? "#000" : "#999",
        c: index == $data.barIdx ? "28rpx" : "26rpx",
        d: index == $data.barIdx ? 1 : 0,
        e: index,
        f: common_vendor.o((...args) => $options.barClick && $options.barClick(...args), index),
        g: index
      };
    }),
    O: $data.barIdx === $data.barList.length - 1
  }, $data.barIdx === $data.barList.length - 1 ? {
    P: common_vendor.o((...args) => $options.goToAllMembers && $options.goToAllMembers(...args)),
    Q: common_vendor.f($data.adminMembers, (member, idx, i0) => {
      return common_vendor.e({
        a: member.user_avatar || "/static/img/avatar.png",
        b: common_vendor.t(member.user_nickname),
        c: member.role_type === 3
      }, member.role_type === 3 ? {} : member.role_type === 2 ? {} : {}, {
        d: member.role_type === 2,
        e: common_vendor.t(member.gender === "男" ? "♂" : "♀"),
        f: common_vendor.n(member.gender === "男" ? "male" : "female"),
        g: common_vendor.t($options.formatJoinTime(member.join_time)),
        h: "admin-" + idx,
        i: common_vendor.o(($event) => $options.goToUserProfile(member.uid), "admin-" + idx)
      });
    })
  } : common_vendor.e({
    R: $data.showLoading
  }, $data.showLoading ? {} : {}, {
    S: $data.isEmpty
  }, $data.isEmpty ? {
    T: common_assets._imports_3$1,
    U: common_vendor.t($data.circleInfo.circle_name || $data.circleInfo.name || "该圈子"),
    V: common_vendor.t($data.circleInfo.is_joined ? "快来发布第一篇圈内笔记吧" : "加入圈子即可发布圈内笔记")
  } : common_vendor.e({
    W: $data.isWaterfall
  }, $data.isWaterfall ? {
    X: common_vendor.p({
      note: $data.list,
      page: $data.page
    })
  } : {
    Y: common_vendor.f($data.list, (item, index, i0) => {
      return {
        a: index,
        b: common_vendor.o($options.likeClick, index),
        c: common_vendor.o($options.onCardUpdate, index),
        d: "5725315b-2-" + i0,
        e: common_vendor.p({
          item,
          idx: index
        })
      };
    })
  }, {
    Z: common_vendor.n($data.isWaterfall ? "dynamic-box" : "")
  }), {
    aa: $data.list.length > 0 && $data.loadStatus === "noMore"
  }, $data.list.length > 0 && $data.loadStatus === "noMore" ? {} : {}), {
    ab: common_vendor.o($options.closeShare),
    ac: common_vendor.o($options.handleShare),
    ad: common_vendor.o($options.handleDislike),
    ae: common_vendor.o($options.handleReport),
    af: common_vendor.o($options.handleEdit),
    ag: common_vendor.o($options.handleDelete),
    ah: common_vendor.p({
      show: $data.showShare,
      noteInfo: $options.circleShareInfo,
      userId: $data.userId
    }),
    ai: common_vendor.t($data.tipsTitle),
    aj: common_vendor.sr("tipsPopup", "5725315b-4"),
    ak: common_vendor.p({
      type: "top",
      ["mask-background-color"]: "rgba(0, 0, 0, 0)"
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
_sfc_main.__runtimeHooks = 7;
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/note/circle.js.map
