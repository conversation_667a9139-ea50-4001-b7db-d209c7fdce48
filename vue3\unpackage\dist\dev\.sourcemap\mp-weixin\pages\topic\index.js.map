{"version": 3, "file": "index.js", "sources": ["pages/topic/index.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvdG9waWMvaW5kZXgudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"container\">\r\n    <!-- 导航栏 -->\r\n    <view class=\"nav-box df\" :style=\"{'padding-top': statusBarHeight + 'px'}\">\r\n      <view class=\"nav-back df\" :style=\"{'height': titleBarHeight + 'px'}\" @tap=\"navBack\">\r\n        <image src=\"/static/img/back.png\" style=\"width:34rpx;height:34rpx\"></image>\r\n      </view>\r\n      <view class=\"nav-title\">话题列表</view>\r\n    </view>\r\n\r\n    <!-- 话题列表 -->\r\n    <view class=\"topic-list\" :style=\"{'padding-top': statusBarHeight + titleBarHeight + 20 + 'px'}\">\r\n      <view\r\n        v-for=\"(topic, index) in topicList\"\r\n        :key=\"topic.id || index\"\r\n        class=\"topic-item\"\r\n        @tap=\"goToTopicDetail(topic)\">\r\n        <view class=\"topic-content\">\r\n          <view class=\"topic-header\">\r\n            <text class=\"topic-hash\">#</text>\r\n            <text class=\"topic-title\">{{ topic.title }}</text>\r\n          </view>\r\n          <view class=\"topic-stats\">\r\n            <text class=\"stat-text\">{{ topic.post_count || 0 }}篇动态</text>\r\n            <text class=\"stat-divider\">·</text>\r\n            <text class=\"stat-text\">{{ topic.view_count || 0 }}次浏览</text>\r\n          </view>\r\n          <view v-if=\"topic.description\" class=\"topic-desc\">{{ topic.description }}</view>\r\n        </view>\r\n        <view class=\"topic-arrow\">\r\n          <image src=\"/static/img/arrow_right.png\" mode=\"aspectFit\"></image>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 空状态 -->\r\n      <view v-if=\"topicList.length === 0 && !loading\" class=\"empty-box\">\r\n        <image src=\"/static/img/empty.png\"/>\r\n        <text class=\"empty-text\">暂无话题</text>\r\n      </view>\r\n\r\n      <!-- 加载状态 -->\r\n      <view v-if=\"loading\" class=\"loading-box\">\r\n        <text>加载中...</text>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { getTopicList } from '@/api/social'\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      statusBarHeight: this.$store.state.statusBarHeight || 20,\r\n      titleBarHeight: this.$store.state.titleBarHeight || 44,\r\n      topicList: [],\r\n      loading: false\r\n    }\r\n  },\r\n  onLoad() {\r\n    this.getTopics();\r\n  },\r\n  methods: {\r\n    // 获取话题列表\r\n    getTopics() {\r\n      this.loading = true;\r\n      getTopicList({\r\n        page: 1,\r\n        limit: 20,\r\n        keyword: ''\r\n      }).then(res => {\r\n        this.loading = false;\r\n        if (res.status === 200 && res.data && res.data.list) {\r\n          this.topicList = res.data.list;\r\n        }\r\n      }).catch(err => {\r\n        this.loading = false;\r\n        console.error('获取话题列表失败:', err);\r\n        uni.showToast({\r\n          title: '获取话题列表失败',\r\n          icon: 'none'\r\n        });\r\n      });\r\n    },\r\n\r\n    // 跳转到话题详情\r\n    goToTopicDetail(topic) {\r\n      if (!topic || !topic.id) return;\r\n\r\n      uni.navigateTo({\r\n        url: `/pages/topic/details?id=${topic.id}`,\r\n        fail: (err) => {\r\n          console.error('跳转话题详情失败:', err);\r\n          uni.showToast({\r\n            title: '页面跳转失败',\r\n            icon: 'none'\r\n          });\r\n        }\r\n      });\r\n    },\r\n\r\n    // 返回上一页\r\n    navBack() {\r\n      if (getCurrentPages().length > 1) {\r\n        uni.navigateBack();\r\n      } else {\r\n        uni.switchTab({\r\n          url: \"//pages/index/index\"\r\n        });\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style>\r\n.container {\r\n  min-height: 100vh;\r\n  background: #f8f8f8;\r\n}\r\n\r\n.nav-box {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  z-index: 99;\r\n  background: #fff;\r\n  border-bottom: 1rpx solid #f0f0f0;\r\n}\r\n\r\n.nav-back {\r\n  padding: 0 30rpx;\r\n  width: 34rpx;\r\n  height: 100%;\r\n}\r\n\r\n.nav-title {\r\n  font-size: 32rpx;\r\n  font-weight: 700;\r\n  color: #333;\r\n}\r\n\r\n.topic-list {\r\n  padding: 20rpx;\r\n}\r\n\r\n.topic-item {\r\n  background: #fff;\r\n  border-radius: 16rpx;\r\n  padding: 30rpx;\r\n  margin-bottom: 20rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);\r\n}\r\n\r\n.topic-content {\r\n  flex: 1;\r\n}\r\n\r\n.topic-header {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 10rpx;\r\n}\r\n\r\n.topic-hash {\r\n  font-size: 32rpx;\r\n  font-weight: bold;\r\n  color: #007AFF;\r\n  margin-right: 8rpx;\r\n}\r\n\r\n.topic-title {\r\n  font-size: 28rpx;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.topic-stats {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 10rpx;\r\n}\r\n\r\n.stat-text {\r\n  font-size: 24rpx;\r\n  color: #999;\r\n}\r\n\r\n.stat-divider {\r\n  margin: 0 8rpx;\r\n  font-size: 24rpx;\r\n  color: #999;\r\n}\r\n\r\n.topic-desc {\r\n  font-size: 24rpx;\r\n  color: #666;\r\n  line-height: 1.4;\r\n}\r\n\r\n.topic-arrow {\r\n  width: 32rpx;\r\n  height: 32rpx;\r\n}\r\n\r\n.topic-arrow image {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.empty-box {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 100rpx 0;\r\n}\r\n\r\n.empty-box image {\r\n  width: 200rpx;\r\n  height: 200rpx;\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.empty-text {\r\n  font-size: 28rpx;\r\n  color: #999;\r\n}\r\n\r\n.loading-box {\r\n  display: flex;\r\n  justify-content: center;\r\n  padding: 50rpx 0;\r\n}\r\n\r\n.loading-box text {\r\n  font-size: 28rpx;\r\n  color: #999;\r\n}\r\n\r\n.df {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n</style>", "import MiniProgramPage from 'Z:/WWW/shejiao/vue3/pages/topic/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["getTopicList", "uni"], "mappings": ";;;;AAmDA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,iBAAiB,KAAK,OAAO,MAAM,mBAAmB;AAAA,MACtD,gBAAgB,KAAK,OAAO,MAAM,kBAAkB;AAAA,MACpD,WAAW,CAAE;AAAA,MACb,SAAS;AAAA,IACX;AAAA,EACD;AAAA,EACD,SAAS;AACP,SAAK,UAAS;AAAA,EACf;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,YAAY;AACV,WAAK,UAAU;AACfA,8BAAa;AAAA,QACX,MAAM;AAAA,QACN,OAAO;AAAA,QACP,SAAS;AAAA,OACV,EAAE,KAAK,SAAO;AACb,aAAK,UAAU;AACf,YAAI,IAAI,WAAW,OAAO,IAAI,QAAQ,IAAI,KAAK,MAAM;AACnD,eAAK,YAAY,IAAI,KAAK;AAAA,QAC5B;AAAA,MACF,CAAC,EAAE,MAAM,SAAO;AACd,aAAK,UAAU;AACfC,sBAAc,MAAA,MAAA,SAAA,+BAAA,aAAa,GAAG;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACH,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,gBAAgB,OAAO;AACrB,UAAI,CAAC,SAAS,CAAC,MAAM;AAAI;AAEzBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,2BAA2B,MAAM,EAAE;AAAA,QACxC,MAAM,CAAC,QAAQ;AACbA,wBAAA,MAAA,MAAA,SAAA,+BAAc,aAAa,GAAG;AAC9BA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,UAAU;AACR,UAAI,gBAAe,EAAG,SAAS,GAAG;AAChCA,sBAAG,MAAC,aAAY;AAAA,aACX;AACLA,sBAAAA,MAAI,UAAU;AAAA,UACZ,KAAK;AAAA,QACP,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AChHA,GAAG,WAAW,eAAe;"}