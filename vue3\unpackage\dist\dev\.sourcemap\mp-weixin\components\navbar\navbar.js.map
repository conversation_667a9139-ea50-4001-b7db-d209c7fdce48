{"version": 3, "file": "navbar.js", "sources": ["components/navbar/navbar.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/WjovV1dXL3NoZWppYW8vdnVlMy9jb21wb25lbnRzL25hdmJhci9uYXZiYXIudnVl"], "sourcesContent": ["<template>\r\n  <view \r\n    :class=\"['nav-bar', bg == 0 ? 'bfw' : '', bg == 1 ? 'bf8' : '']\" \r\n    :style=\"{'padding-top': statusBarHeight + 'px'}\"\r\n  >\r\n    <view class=\"navbar-item\" :style=\"{'height': titleBarHeight + 'px'}\">\r\n      <view class=\"back-box\" :style=\"{'height': titleBarHeight + 'px'}\" @tap=\"navBack\">\r\n        <image src=\"/static/img/back.png\"></image>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\n// 移除 getApp() 调用，使用简单的默认值\r\nlet sysHeight = 20; // 默认状态栏高度\r\n\r\nexport default {\r\n  name: 'navbar',\r\n  props: {\r\n    bg: {\r\n      type: Number,\r\n      default: 0\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      statusBarHeight: 20,\r\n      titleBarHeight: 44,\r\n      navH: 0,\r\n      sysHeight: sysHeight + 'px'\r\n    }\r\n  },\r\n  created() {\r\n    this.initNavBarHeight();\r\n  },\r\n  methods: {\r\n    initNavBarHeight() {\r\n      let that = this;\r\n      \r\n      // 获取状态栏高度\r\n      const systemInfo = uni.getSystemInfoSync();\r\n      this.statusBarHeight = systemInfo.statusBarHeight || 0;\r\n      \r\n      // 使用固定的导航栏高度，避免依赖 getApp()\r\n      // #ifdef MP\r\n      that.navH = 0; // 小程序使用默认值\r\n      // #endif\r\n      // #ifdef H5\r\n      that.navH = 96;\r\n      // #endif\r\n      // #ifdef APP-PLUS\r\n      that.navH = 30;\r\n      // #endif\r\n      \r\n      // 计算标题栏高度\r\n      if (that.navH > 0) {\r\n        // 有全局导航高度时，用rpx转px\r\n        const rpxRatio = 750 / systemInfo.windowWidth;\r\n        const totalPx = that.navH / rpxRatio;\r\n        this.titleBarHeight = totalPx - this.statusBarHeight;\r\n      } else {\r\n        // 默认高度\r\n        this.titleBarHeight = 44;\r\n      }\r\n      \r\n      // 确保最小高度\r\n      if (this.titleBarHeight < 30) {\r\n        this.titleBarHeight = 44;\r\n      }\r\n    },\r\n    \r\n    navBack() {\r\n      if (getCurrentPages().length > 1) {\r\n        uni.navigateBack();\r\n      } else {\r\n        uni.switchTab({\r\n          url: '//pages/index/index'\r\n        });\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style>\r\n.nav-bar {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  width: 100%;\r\n  z-index: 99;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.nav-bar .navbar-item {\r\n  width: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.navbar-item .back-box {\r\n  padding: 0 30rpx;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.navbar-item .back-box image {\r\n  width: 34rpx;\r\n  height: 34rpx;\r\n}\r\n\r\n.bfw {\r\n  backdrop-filter: blur(10px);\r\n  -webkit-backdrop-filter: blur(10px);\r\n  background: rgba(255,255,255,.8);\r\n}\r\n\r\n.bf8 {\r\n  background: #f8f8f8;\r\n}\r\n</style> ", "import Component from 'Z:/WWW/shejiao/vue3/components/navbar/navbar.vue'\nwx.createComponent(Component)"], "names": ["uni"], "mappings": ";;;AAeA,IAAI,YAAY;AAEhB,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,OAAO;AAAA,IACL,IAAI;AAAA,MACF,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,EACD;AAAA,EACD,OAAO;AACL,WAAO;AAAA,MACL,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,MAChB,MAAM;AAAA,MACN,WAAW,YAAY;AAAA,IACzB;AAAA,EACD;AAAA,EACD,UAAU;AACR,SAAK,iBAAgB;AAAA,EACtB;AAAA,EACD,SAAS;AAAA,IACP,mBAAmB;AACjB,UAAI,OAAO;AAGX,YAAM,aAAaA,oBAAI;AACvB,WAAK,kBAAkB,WAAW,mBAAmB;AAIrD,WAAK,OAAO;AAUZ,UAAI,KAAK,OAAO,GAAG;AAEjB,cAAM,WAAW,MAAM,WAAW;AAClC,cAAM,UAAU,KAAK,OAAO;AAC5B,aAAK,iBAAiB,UAAU,KAAK;AAAA,aAChC;AAEL,aAAK,iBAAiB;AAAA,MACxB;AAGA,UAAI,KAAK,iBAAiB,IAAI;AAC5B,aAAK,iBAAiB;AAAA,MACxB;AAAA,IACD;AAAA,IAED,UAAU;AACR,UAAI,gBAAe,EAAG,SAAS,GAAG;AAChCA,sBAAG,MAAC,aAAY;AAAA,aACX;AACLA,sBAAAA,MAAI,UAAU;AAAA,UACZ,KAAK;AAAA,QACP,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AACF;;;;;;;;;;;;;ACjFA,GAAG,gBAAgB,SAAS;"}