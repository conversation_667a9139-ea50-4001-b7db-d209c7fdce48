<template>
  <view>
    <!-- <navbar ref="navbar"></navbar> -->
    <view class="content">
      <mp-html :content="content" :tag-style="tagStyle"></mp-html>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import { getUserAgreement } from '@/api/user.js'
import { onLoad } from '@dcloudio/uni-app'

// 获取app实例
const app = getApp()

// 响应式数据
const content = ref("")
const navH = ref(0)
const tagStyle = ref({
  img: 'width:100%;display:block;',
  table: 'width:100%',
  video: 'width:100%'
})

// 初始化导航栏高度
const initNavHeight = () => {
  // #ifdef MP
  navH.value = app.globalData.navHeight || 88
  // #endif
  // #ifdef H5
  navH.value = 96
  // #endif
  // #ifdef APP-PLUS
  navH.value = 30
  // #endif
}

// 加载用户协议内容
const loadUserAgreement = async (type) => {
  try {
    const res = await getUserAgreement(type)
    content.value = res.data.content

    // 设置页面标题
    uni.setNavigationBarTitle({
      title: res.data.title
    })
  } catch (err) {
    // 显示错误提示
    uni.showToast({
      title: err.message || err || '加载失败',
      icon: 'none',
      duration: 2000
    })
  }
}

// 页面加载生命周期
onLoad((options) => {
  // 初始化导航栏高度
  initNavHeight()

  // 加载用户协议内容
  if (options && options.type) {
    loadUserAgreement(options.type)
  }
})
</script>

<style>
page {
  background: #fff;
}

.content {
  width: calc(100% - 60rpx);
  margin: 0 30rpx;
  font-size: 24rpx;
  line-height: 2;
  padding: 20rpx 0;
  box-sizing: border-box;
}
</style>