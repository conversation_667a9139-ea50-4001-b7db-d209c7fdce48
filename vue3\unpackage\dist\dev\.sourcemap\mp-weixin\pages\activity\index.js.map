{"version": 3, "file": "index.js", "sources": ["pages/activity/index.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvYWN0aXZpdHkvaW5kZXgudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"container\">\r\n    <!-- 顶部导航栏 -->\r\n    <view class=\"nav-bar bfw\" :style=\"{'padding-top': statusBarHeight + 'px'}\">\r\n      <view class=\"bar-box df\" :style=\"{'height': titleBarHeight + 'px', 'width': '100%'}\">\r\n        <view class=\"bar-back df\" @tap=\"navBack\">\r\n          <image src=\"/static/img/back.png\" style=\"width:34rpx;height:34rpx\"></image>\r\n        </view>\r\n        <view class=\"bar-title ohto\">活动</view>\r\n      </view>\r\n      <view class=\"nav-box df\">\r\n        <view v-for=\"(item, index) in barList\" :key=\"index\" class=\"nav-item df\" @tap=\"barClick\" :data-idx=\"index\">\r\n          <text :style=\"{'color': index == barIdx ? '#000' : '#999', 'font-size': index == barIdx ? '28rpx' : '26rpx'}\">{{item}}</text>\r\n          <view :style=\"{'opacity': index == barIdx ? 1 : 0}\" class=\"nav-line\"></view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    <view class=\"content-box\" :style=\"{'margin-top': 'calc(' + (statusBarHeight + titleBarHeight) + 'px + 90rpx)'}\">\r\n      <view class=\"heio\" :style=\"{'height': isThrottling || loadStatus == 'loading' ? '0px' : '60rpx'}\">\r\n        <uni-load-more status=\"loading\"></uni-load-more>\r\n      </view>\r\n      <emptyPage\r\n        v-if=\"isEmpty\"\r\n        :title=\"barIdx == 0 ? '暂无推荐活动' : '暂无参加活动'\"\r\n        description=\"空空如也，等待探索\"\r\n        image=\"/static/img/inset/null.png\"\r\n      />\r\n      <block v-else>\r\n        <view v-for=\"(item, index) in list\" :key=\"index\" class=\"activity-item df\">\r\n          <view class=\"activity-img\" @tap=\"toActivityDetails\" :data-id=\"item.id\">\r\n            <lazy-image :src=\"item.img\"></lazy-image>\r\n            <view class=\"activity-state df\">{{item.status_str ? item.status_str : \"加载中\"}}</view>\r\n          </view>\r\n          <view class=\"activity-data\">\r\n            <view class=\"title ohto\" @tap=\"toActivityDetails\" :data-id=\"item.id\">{{item.name ? item.name : \"活动名称加载中\"}}</view>\r\n            <view class=\"txt df\" @tap=\"toActivityDetails\" :data-id=\"item.id\"><image src=\"/static/img/sj.png\"></image><view class=\"ohto\">{{item.activity_time ? item.activity_time : \"活动时间加载中\"}}</view></view>\r\n            <view class=\"txt df\" @tap=\"toActivityDetails\" :data-id=\"item.id\"><image src=\"/static/img/wz.png\"></image><view class=\"ohto\">{{item.adds_name ? item.adds_name : \"活动地址加载中\"}}</view></view>\r\n            <view v-if=\"item.user_count\" class=\"cu-img-group\" @tap=\"toActivityDetails\" :data-id=\"item.id\">\r\n              <view v-for=\"(img, imgIndex) in item.avatar_list\" :key=\"imgIndex\" class=\"cu-img\"><image :src=\"img\" mode=\"aspectFill\"></image></view>\r\n              <view class=\"cu-tit\">{{item.user_count}}人已参加</view>\r\n            </view>\r\n            <view v-else class=\"cu-txt-group\" @tap=\"toActivityDetails\" :data-id=\"item.id\">{{item.browse}}人想参加 </view>\r\n            <view class=\"activity-btn df\">\r\n              <button v-if=\"barIdx == 0\" class=\"btn-item df w100\" @tap=\"toActivityDetails\" :data-id=\"item.id\"><text>{{item.is_join ? '查看详情' : '立即参加'}}</text><image class=\"effect icon\" src=\"/static/img/z.png\"></image></button>\r\n              <block v-else>\r\n                <button class=\"btn-item df\" style=\"width:60rpx\" open-type=\"contact\" :send-message-title=\"item.name\" :send-message-path=\"'/pages/activity/details?id='+item.id\" :send-message-img=\"item.img\" :show-message-card=\"true\">\r\n                  <image class=\"img\" src=\"/static/img/dh.png\"></image>\r\n                </button>\r\n                <block v-if=\"item.product_status == 1 || item.product_status == 3\">\r\n                  <button class=\"btn-item df w50\" @tap=\"refundClick(true, index)\">\r\n                    <text v-if=\"item.product_status == 1\">申请售后</text>\r\n                    <text v-else style=\"color:#FA5150\">售后中</text>\r\n                  </button>\r\n                  <button class=\"btn-item df w50\" @tap=\"ticketClick(true, index)\">\r\n                    <text>出示门票</text>\r\n                  </button>\r\n                </block>\r\n                <block v-else-if=\"item.product_status == 2\">\r\n                  <button class=\"btn-item df\" style=\"width:60rpx\" @tap=\"delActivity\" :data-idx=\"index\">\r\n                    <image class=\"img\" src=\"/static/img/delete.png\"></image>\r\n                  </button>\r\n                  <button class=\"btn-item df w60\" @tap=\"ticketClick(true, index)\">\r\n                    <text>出示门票</text>\r\n                  </button>\r\n                </block>\r\n                <block v-else>\r\n                  <button class=\"btn-item df\" style=\"width:60rpx\" @tap=\"delActivity\" :data-idx=\"index\">\r\n                    <image class=\"img\" src=\"/static/img/delete.png\"></image>\r\n                  </button>\r\n                  <button class=\"btn-item df w60\">\r\n                    <text style=\"color:#999\">报名已取消</text>\r\n                  </button>\r\n                </block>\r\n              </block>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </block>\r\n      <uni-load-more :status=\"loadStatus\"></uni-load-more>\r\n    </view>\r\n    \r\n    <!-- 售后弹窗 -->\r\n    <uni-popup ref=\"refundPopup\" type=\"bottom\" :safe-area=\"false\" class=\"r\">\r\n      <view class=\"popup-box\">\r\n        <view class=\"popup-top df\">\r\n          <view class=\"popup-title\">\r\n            <view class=\"t1\">\"{{list[idx] && list[idx].activity_product_name}}\"申请售后</view>\r\n            <view class=\"t2\">提交后您可以联系客服为您及时处理</view>\r\n          </view>\r\n          <view class=\"popup-close df\" @tap=\"refundClick(false)\">\r\n            <image src=\"/static/img/tabbar/3.png\" style=\"width:20rpx;height:20rpx\"></image>\r\n          </view>\r\n        </view>\r\n        <view class=\"df\" style=\"position:relative;margin-top:30rpx\">\r\n          <input class=\"popup-input\" type=\"number\" :maxlength=\"list[idx] && list[idx].quantity\" :placeholder=\"'售后票数（最多'+(list[idx] && list[idx].quantity)+'张）'\" v-model=\"refundQuantity\"/>\r\n          <view class=\"popup-input-tips\">张</view>\r\n        </view>\r\n        <textarea class=\"popup-textarea\" auto-height placeholder=\"售后原因（最多200字）\" maxlength=\"200\" v-model=\"refundReason\"></textarea>\r\n        <view v-if=\"list[idx] && list[idx].product_status == 3\" class=\"popup-tips\">注：售后中重新提交将会覆盖之前的售后申请。</view>\r\n        <view class=\"popup-btn df\">\r\n          <view class=\"bg1\" @tap=\"refundClick(false)\">取消</view>\r\n          <view class=\"bg2\" @tap=\"activityRefund\">提交</view>\r\n        </view>\r\n      </view>\r\n    </uni-popup>\r\n    \r\n    <!-- 门票弹窗 -->\r\n    <uni-popup ref=\"ticketPopup\" type=\"bottom\" :safe-area=\"false\" class=\"r\">\r\n      <view class=\"popup-box\">\r\n        <view class=\"popup-top df\">\r\n          <view class=\"popup-title\">\r\n            <view class=\"t1\">{{list[idx] && list[idx].activity_product_name}}，共{{list[idx] && list[idx].quantity}}张</view>\r\n            <view class=\"t2\">长按下方门票号码即可复制</view>\r\n          </view>\r\n          <view class=\"popup-close df\" @tap=\"ticketClick(false)\">\r\n            <image src=\"/static/img/tabbar/3.png\" style=\"width:20rpx;height:20rpx\"></image>\r\n          </view>\r\n        </view>\r\n        <view class=\"popup-scroll\">\r\n          <view v-for=\"(code, codeIndex) in (list[idx] && list[idx].code)\" :key=\"codeIndex\" class=\"popup-item\">\r\n            <view :class=\"['code-item', 'df', list[idx] && list[idx].product_status != 1 && 'item-active']\">\r\n              <text user-select=\"true\" class=\"t1\">{{code}}</text>\r\n              <text v-if=\"list[idx] && list[idx].product_status != 1\" class=\"t2\">\r\n                {{list[idx] && list[idx].product_status == 2 ? '（已使用）' : list[idx] && list[idx].product_status == 3 ? '（售后中）' : '（已取消）'}}\r\n              </text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        <view class=\"popup-tips\">注：门票出示并核销后将无法退款，请谨慎操作</view>\r\n        <view class=\"popup-btn df\" @tap=\"ticketClick(false)\">\r\n          <view class=\"bg1\">取消</view>\r\n          <view class=\"bg2\">完成</view>\r\n        </view>\r\n      </view>\r\n    </uni-popup>\r\n    \r\n    <!-- 提示弹窗 -->\r\n    <uni-popup ref=\"tipsPopup\" type=\"top\" :mask-background-color=\"'rgba(0, 0, 0, 0)'\">\r\n      <view class=\"tips-box df\">\r\n        <view class=\"tips-item\">{{tipsTitle}}</view>\r\n      </view>\r\n    </uni-popup>\r\n  </view>\r\n</template>\r\n<script>\r\nimport lazyImage from '@/components/lazyImage/lazyImage.vue'\r\nimport uniLoadMore from '@/uni_modules/uni-load-more/components/uni-load-more/uni-load-more.vue'\r\nimport emptyPage from '@/components/emptyPage.vue'\r\n\r\nconst app = getApp();\r\n\r\nexport default {\r\n  components: {\r\n    lazyImage,\r\n    uniLoadMore,\r\n    emptyPage\r\n  },\r\n  data() {\r\n    return {\r\n      statusBarHeight: this.$store.state.statusBarHeight || 20,\r\n      titleBarHeight: this.$store.state.titleBarHeight || 44,\r\n      barList: [\"全部\", \"我的\"],\r\n      barIdx: 0,\r\n      list: [],\r\n      idx: 0,\r\n      page: 1,\r\n      isThrottling: true,\r\n      isEmpty: false,\r\n      loadStatus: \"loading\",\r\n      refundQuantity: \"\",\r\n      refundReason: \"\",\r\n      tipsTitle: \"\"\r\n    }\r\n  },\r\n  onLoad(option) {\r\n    if (option.type) {\r\n      this.barIdx = option.type\r\n    }\r\n    this.activityList()\r\n  },\r\n  methods: {\r\n    activityList() {\r\n      let that = this\r\n      // 模拟API请求\r\n      setTimeout(() => {\r\n        that.isThrottling = true\r\n        that.loadStatus = \"more\"\r\n        \r\n        // 模拟数据\r\n        let data = {\r\n          data: []\r\n        }\r\n        \r\n        if (that.barIdx == 0) {\r\n          // 全部活动\r\n          data.data = [\r\n            {\r\n              id: 1,\r\n              name: \"夏季摄影大赛\",\r\n              img: \"/static/img/avatar.png\",\r\n              activity_time: \"2023-07-20 14:00-17:00\",\r\n              adds_name: \"市中心广场\",\r\n              status_str: \"报名中\",\r\n              is_join: false,\r\n              browse: 123,\r\n              user_count: 10,\r\n              avatar_list: [\"/static/img/avatar.png\", \"/static/img/avatar.png\"]\r\n            },\r\n            {\r\n              id: 2,\r\n              name: \"手工DIY工作坊\",\r\n              img: \"/static/img/avatar.png\",\r\n              activity_time: \"2023-07-25 10:00-12:00\",\r\n              adds_name: \"文创园区5号楼\",\r\n              status_str: \"报名中\",\r\n              is_join: true,\r\n              browse: 89,\r\n              user_count: 15,\r\n              avatar_list: [\"/static/img/avatar.png\", \"/static/img/avatar.png\"]\r\n            }\r\n          ]\r\n        } else {\r\n          // 我的活动\r\n          data.data = [\r\n            {\r\n              id: 2,\r\n              name: \"手工DIY工作坊\",\r\n              img: \"/static/img/avatar.png\",\r\n              activity_time: \"2023-07-25 10:00-12:00\",\r\n              adds_name: \"文创园区5号楼\",\r\n              status_str: \"已报名\",\r\n              is_join: true,\r\n              product_status: 1,\r\n              order_id: \"ORDER12345\",\r\n              activity_product_name: \"DIY工作坊标准票\",\r\n              quantity: 2,\r\n              code: [\"TICKET123456\", \"TICKET123457\"],\r\n              avatar_list: [\"/static/img/avatar.png\", \"/static/img/avatar.png\"]\r\n            }\r\n          ]\r\n        }\r\n        \r\n        if (data.data.length > 0) {\r\n          if (that.page == 1) {\r\n            that.list = data.data\r\n          } else {\r\n            that.list = that.list.concat(data.data)\r\n          }\r\n          that.isEmpty = false\r\n        } else if (that.page == 1) {\r\n          that.list = []\r\n          that.isEmpty = true\r\n        }\r\n        \r\n        // 第二页没有数据\r\n        if (that.page > 1) {\r\n          that.loadStatus = \"noMore\"\r\n        }\r\n      }, 500)\r\n    },\r\n    \r\n    delActivity(e) {\r\n      let that = this\r\n      let idx = e.currentTarget.dataset.idx\r\n      \r\n      uni.showModal({\r\n        content: \"确定要永久删除该活动吗？\",\r\n        confirmColor: \"#FA5150\",\r\n        success: function(res) {\r\n          if (res.confirm) {\r\n            // 模拟删除成功\r\n            app.globalData.isCenterPage = true\r\n            that.opTipsPopup(\"删除成功\")\r\n            that.list.splice(idx, 1)\r\n            if (that.list.length <= 0) {\r\n              that.isEmpty = true\r\n              that.loadStatus = \"more\"\r\n            }\r\n          }\r\n        }\r\n      })\r\n    },\r\n    \r\n    activityRefund() {\r\n      let that = this\r\n      if (!that.refundQuantity || that.refundQuantity < 1) {\r\n        return that.opTipsPopup(\"请填写需要售后的票数\")\r\n      }\r\n      if (!that.refundReason) {\r\n        return that.opTipsPopup(\"请填写售后原因\")\r\n      }\r\n      \r\n      // 模拟售后请求\r\n      setTimeout(() => {\r\n        that.opTipsPopup(\"售后申请已提交，等待审核\")\r\n        that.list[that.idx].product_status = 3\r\n        that.$refs.refundPopup.close()\r\n      }, 500)\r\n    },\r\n    \r\n    barClick(e) {\r\n      if (this.isThrottling) {\r\n        this.isThrottling = false\r\n        let idx = e.currentTarget.dataset.idx\r\n        this.barIdx = idx\r\n        this.page = 1\r\n        this.activityList()\r\n      }\r\n    },\r\n    \r\n    toActivityDetails(e) {\r\n      let id = e.currentTarget.dataset.id\r\n      uni.navigateTo({\r\n        url: \"/pages/activity/details?id=\" + id\r\n      })\r\n    },\r\n    \r\n    refundClick(show, idx = 0) {\r\n      if (!show) {\r\n        this.$refs.refundPopup.close()\r\n      } else {\r\n        this.idx = idx\r\n        this.$refs.refundPopup.open()\r\n      }\r\n    },\r\n    \r\n    ticketClick(show, idx = 0) {\r\n      if (!show) {\r\n        this.$refs.ticketPopup.close()\r\n      } else {\r\n        this.idx = idx\r\n        this.$refs.ticketPopup.open()\r\n      }\r\n    },\r\n    \r\n    opTipsPopup(msg) {\r\n      let that = this\r\n      that.tipsTitle = msg\r\n      that.$refs.tipsPopup.open()\r\n      setTimeout(function() {\r\n        that.$refs.tipsPopup.close()\r\n      }, 2000)\r\n    },\r\n    \r\n    navBack() {\r\n      if (getCurrentPages().length > 1) {\r\n        uni.navigateBack()\r\n      } else {\r\n        uni.switchTab({\r\n          url: \"//pages/index/index\"\r\n        })\r\n      }\r\n    }\r\n  },\r\n  \r\n  onReachBottom() {\r\n    if (this.list.length && this.loadStatus !== 'noMore') {\r\n      this.page = this.page + 1\r\n      this.loadStatus = \"loading\"\r\n      this.activityList()\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style>\r\n.container{width:100%;}\r\n.nav-bar{position:fixed;top:0;left:0;width:100%;z-index:99;box-sizing:border-box;}\r\n.bar-box .bar-back{padding:0 30rpx;width:34rpx;height:100%;}\r\n.bar-box .bar-title{max-width:60%;font-size:32rpx;font-weight:700;}\r\n.nav-box{width:100%;height:80rpx;}\r\n.nav-box .nav-item{padding:0 30rpx;height:100%;flex-direction:column;justify-content:center;position:relative;}\r\n.nav-box .nav-item text{font-weight:700;transition:all .3s ease-in-out;}\r\n.nav-box .nav-line{position:absolute;bottom:12rpx;width:18rpx;height:6rpx;border-radius:6rpx;background:#000;transition:opacity .3s ease-in-out;}\r\n.content-box{width:100%;}\r\n.content-box .activity-item{width:calc(100% - 60rpx);margin:30rpx;}\r\n.activity-item .activity-img{width:275rpx;height:220rpx;border-radius:8rpx;background:#f8f8f8;position:relative;overflow:hidden;}\r\n.activity-img .activity-state{position:absolute;top:15rpx;left:15rpx;width:68rpx;height:38rpx;color:#fff;font-size:16rpx;font-weight:700;background:rgba(0,0,0,.4);border:1px solid rgba(255,255,255,.16);border-radius:8rpx;justify-content:center;}\r\n.activity-item .activity-data{padding-left:20rpx;width:calc(100% - 295rpx);height:220rpx;display:flex;flex-direction:column;justify-content:space-between;position:relative;}\r\n.activity-data .title{font-size:28rpx;line-height:28rpx;font-weight:700;padding-bottom:12rpx;}\r\n.activity-data .txt{margin-bottom:4rpx;}\r\n.activity-data .txt image{margin-right:8rpx;width:20rpx;height:20rpx;}\r\n.activity-data .txt view{width:calc(100% - 26rpx);color:#999;font-size:20rpx;font-weight:500;}\r\n.activity-data .cu-img-group{margin:8rpx 0 16rpx 16rpx;direction:ltr;unicode-bidi:bidi-override;display:inline-block;}\r\n.cu-img-group .cu-img{width:32rpx;height:32rpx;display:inline-flex;position:relative;margin-left:-16rpx;border:2rpx solid #fff;background:#eee;vertical-align:middle;border-radius:8rpx;border-radius:50%;}\r\n.cu-img-group .cu-img image{width:100%;height:100%;border-radius:8rpx;border-radius:50%;}\r\n.cu-img-group .cu-tit{display:inline-flex;margin-left:8rpx;color:#999;font-size:20rpx;font-weight:500;}\r\n.activity-data .cu-txt-group{margin:8rpx 0 16rpx;font-size:20rpx;font-weight:500;}\r\n.activity-data .activity-btn{width:100%;height:60rpx;justify-content:space-between;}\r\n.activity-btn .btn-item{padding:0;margin:0;height:60rpx;font-size:20rpx;font-weight:700;color:#000;background:#f8f8f8;border-radius:8rpx;justify-content:center;}\r\n.activity-btn .btn-item .icon{margin-left:10rpx;width:20rpx;height:20rpx;}\r\n.activity-btn .btn-item .img{width:24rpx;height:24rpx;}\r\n.w100{width:100%;}\r\n.w50{width:calc(50% - 40rpx);}\r\n.w60{width:calc(100% - 140rpx);}\r\n.popup-box{width:calc(100% - 60rpx);padding:30rpx;background:#fff;border-radius:30rpx 30rpx 0 0;padding-bottom:max(env(safe-area-inset-bottom),30rpx);position:relative;}\r\n.popup-box .popup-top{width:calc(100% - 20rpx);padding:10rpx;justify-content:space-between;}\r\n.popup-top .popup-title .t1{font-size:38rpx;font-weight:700;}\r\n.popup-top .popup-title .t2{color:#999;font-size:20rpx;font-weight:300;}\r\n.popup-top .popup-close{width:48rpx;height:48rpx;border-radius:50%;background:#f8f8f8;justify-content:center;transform:rotate(45deg);}\r\n.popup-box .popup-input,.popup-box .popup-textarea{width:calc(100% - 40rpx);padding:20rpx;border-radius:8rpx;background:#f8f8f8;font-size:24rpx;font-weight:700;}\r\n.popup-box .popup-textarea{margin-top:30rpx;min-height:120rpx;}\r\n.popup-box .popup-input-tips{position:absolute;right:20rpx;color:#999;font-size:24rpx;font-weight:700;}\r\n.popup-box .popup-scroll{width:100%;max-height:50vh;overflow-y:scroll;}\r\n.popup-box .popup-item{margin-top:30rpx;width:calc(100% - 4px);border:2px dashed #f5f5f5;border-radius:16rpx;}\r\n.popup-item .code-item{margin:2px;width:calc(100% - 4px);height:90rpx;justify-content:center;background:#f8f8f8;border-radius:12rpx;}\r\n.popup-item .item-active{color:#ccc;}\r\n.popup-item .code-item .t1{font-size:32rpx;font-weight:bolder;letter-spacing:4rpx;}\r\n.popup-item .code-item .t2{font-size:26rpx;font-weight:300;}\r\n.popup-box .popup-tips{width:100%;margin-top:40rpx;color:#999;font-size:20rpx;text-align:center;}\r\n.popup-box .popup-btn{margin-top:40rpx;width:100%;justify-content:space-between;}\r\n.popup-box .popup-btn view{width:calc(50% - 10rpx);height:90rpx;line-height:90rpx;text-align:center;font-size:24rpx;font-weight:700;border-radius:30rpx;}\r\n.popup-box .popup-btn .bg1{color:#999;background:#f8f8f8;}\r\n.popup-box .popup-btn .bg2{color:#fff;background:#000;}\r\n.tips-box{padding:20rpx 30rpx;border-radius:12rpx;justify-content:center;}\r\n.tips-box .tips-item{color:#fff;font-size:28rpx;font-weight:700;}\r\n\r\n.heio{width:100%;overflow:hidden;transition:height 0.3s;}\r\n.ohto{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;}\r\n.bfw{background:#fff;}\r\n.df{display:flex;align-items:center;}\r\n</style> ", "import MiniProgramPage from 'Z:/WWW/shejiao/vue3/pages/activity/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;;AAiJA,MAAK,YAAa,MAAW;AAC7B,MAAO,cAAa,MAAW;AAC/B,kBAAkB,MAAW;AAE7B,MAAM,MAAM,OAAM;AAElB,MAAK,YAAU;AAAA,EACb,YAAY;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,EACD;AAAA,EACD,OAAO;AACL,WAAO;AAAA,MACL,iBAAiB,KAAK,OAAO,MAAM,mBAAmB;AAAA,MACtD,gBAAgB,KAAK,OAAO,MAAM,kBAAkB;AAAA,MACpD,SAAS,CAAC,MAAM,IAAI;AAAA,MACpB,QAAQ;AAAA,MACR,MAAM,CAAE;AAAA,MACR,KAAK;AAAA,MACL,MAAM;AAAA,MACN,cAAc;AAAA,MACd,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,gBAAgB;AAAA,MAChB,cAAc;AAAA,MACd,WAAW;AAAA,IACb;AAAA,EACD;AAAA,EACD,OAAO,QAAQ;AACb,QAAI,OAAO,MAAM;AACf,WAAK,SAAS,OAAO;AAAA,IACvB;AACA,SAAK,aAAa;AAAA,EACnB;AAAA,EACD,SAAS;AAAA,IACP,eAAe;AACb,UAAI,OAAO;AAEX,iBAAW,MAAM;AACf,aAAK,eAAe;AACpB,aAAK,aAAa;AAGlB,YAAI,OAAO;AAAA,UACT,MAAM,CAAC;AAAA,QACT;AAEA,YAAI,KAAK,UAAU,GAAG;AAEpB,eAAK,OAAO;AAAA,YACV;AAAA,cACE,IAAI;AAAA,cACJ,MAAM;AAAA,cACN,KAAK;AAAA,cACL,eAAe;AAAA,cACf,WAAW;AAAA,cACX,YAAY;AAAA,cACZ,SAAS;AAAA,cACT,QAAQ;AAAA,cACR,YAAY;AAAA,cACZ,aAAa,CAAC,0BAA0B,wBAAwB;AAAA,YACjE;AAAA,YACD;AAAA,cACE,IAAI;AAAA,cACJ,MAAM;AAAA,cACN,KAAK;AAAA,cACL,eAAe;AAAA,cACf,WAAW;AAAA,cACX,YAAY;AAAA,cACZ,SAAS;AAAA,cACT,QAAQ;AAAA,cACR,YAAY;AAAA,cACZ,aAAa,CAAC,0BAA0B,wBAAwB;AAAA,YAClE;AAAA,UACF;AAAA,eACK;AAEL,eAAK,OAAO;AAAA,YACV;AAAA,cACE,IAAI;AAAA,cACJ,MAAM;AAAA,cACN,KAAK;AAAA,cACL,eAAe;AAAA,cACf,WAAW;AAAA,cACX,YAAY;AAAA,cACZ,SAAS;AAAA,cACT,gBAAgB;AAAA,cAChB,UAAU;AAAA,cACV,uBAAuB;AAAA,cACvB,UAAU;AAAA,cACV,MAAM,CAAC,gBAAgB,cAAc;AAAA,cACrC,aAAa,CAAC,0BAA0B,wBAAwB;AAAA,YAClE;AAAA,UACF;AAAA,QACF;AAEA,YAAI,KAAK,KAAK,SAAS,GAAG;AACxB,cAAI,KAAK,QAAQ,GAAG;AAClB,iBAAK,OAAO,KAAK;AAAA,iBACZ;AACL,iBAAK,OAAO,KAAK,KAAK,OAAO,KAAK,IAAI;AAAA,UACxC;AACA,eAAK,UAAU;AAAA,QACjB,WAAW,KAAK,QAAQ,GAAG;AACzB,eAAK,OAAO,CAAC;AACb,eAAK,UAAU;AAAA,QACjB;AAGA,YAAI,KAAK,OAAO,GAAG;AACjB,eAAK,aAAa;AAAA,QACpB;AAAA,MACD,GAAE,GAAG;AAAA,IACP;AAAA,IAED,YAAY,GAAG;AACb,UAAI,OAAO;AACX,UAAI,MAAM,EAAE,cAAc,QAAQ;AAElCA,oBAAAA,MAAI,UAAU;AAAA,QACZ,SAAS;AAAA,QACT,cAAc;AAAA,QACd,SAAS,SAAS,KAAK;AACrB,cAAI,IAAI,SAAS;AAEf,gBAAI,WAAW,eAAe;AAC9B,iBAAK,YAAY,MAAM;AACvB,iBAAK,KAAK,OAAO,KAAK,CAAC;AACvB,gBAAI,KAAK,KAAK,UAAU,GAAG;AACzB,mBAAK,UAAU;AACf,mBAAK,aAAa;AAAA,YACpB;AAAA,UACF;AAAA,QACF;AAAA,OACD;AAAA,IACF;AAAA,IAED,iBAAiB;AACf,UAAI,OAAO;AACX,UAAI,CAAC,KAAK,kBAAkB,KAAK,iBAAiB,GAAG;AACnD,eAAO,KAAK,YAAY,YAAY;AAAA,MACtC;AACA,UAAI,CAAC,KAAK,cAAc;AACtB,eAAO,KAAK,YAAY,SAAS;AAAA,MACnC;AAGA,iBAAW,MAAM;AACf,aAAK,YAAY,cAAc;AAC/B,aAAK,KAAK,KAAK,GAAG,EAAE,iBAAiB;AACrC,aAAK,MAAM,YAAY,MAAM;AAAA,MAC9B,GAAE,GAAG;AAAA,IACP;AAAA,IAED,SAAS,GAAG;AACV,UAAI,KAAK,cAAc;AACrB,aAAK,eAAe;AACpB,YAAI,MAAM,EAAE,cAAc,QAAQ;AAClC,aAAK,SAAS;AACd,aAAK,OAAO;AACZ,aAAK,aAAa;AAAA,MACpB;AAAA,IACD;AAAA,IAED,kBAAkB,GAAG;AACnB,UAAI,KAAK,EAAE,cAAc,QAAQ;AACjCA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,gCAAgC;AAAA,OACtC;AAAA,IACF;AAAA,IAED,YAAY,MAAM,MAAM,GAAG;AACzB,UAAI,CAAC,MAAM;AACT,aAAK,MAAM,YAAY,MAAM;AAAA,aACxB;AACL,aAAK,MAAM;AACX,aAAK,MAAM,YAAY,KAAK;AAAA,MAC9B;AAAA,IACD;AAAA,IAED,YAAY,MAAM,MAAM,GAAG;AACzB,UAAI,CAAC,MAAM;AACT,aAAK,MAAM,YAAY,MAAM;AAAA,aACxB;AACL,aAAK,MAAM;AACX,aAAK,MAAM,YAAY,KAAK;AAAA,MAC9B;AAAA,IACD;AAAA,IAED,YAAY,KAAK;AACf,UAAI,OAAO;AACX,WAAK,YAAY;AACjB,WAAK,MAAM,UAAU,KAAK;AAC1B,iBAAW,WAAW;AACpB,aAAK,MAAM,UAAU,MAAM;AAAA,MAC5B,GAAE,GAAI;AAAA,IACR;AAAA,IAED,UAAU;AACR,UAAI,gBAAe,EAAG,SAAS,GAAG;AAChCA,sBAAAA,MAAI,aAAa;AAAA,aACZ;AACLA,sBAAAA,MAAI,UAAU;AAAA,UACZ,KAAK;AAAA,SACN;AAAA,MACH;AAAA,IACF;AAAA,EACD;AAAA,EAED,gBAAgB;AACd,QAAI,KAAK,KAAK,UAAU,KAAK,eAAe,UAAU;AACpD,WAAK,OAAO,KAAK,OAAO;AACxB,WAAK,aAAa;AAClB,WAAK,aAAa;AAAA,IACpB;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzWA,GAAG,WAAW,eAAe;"}