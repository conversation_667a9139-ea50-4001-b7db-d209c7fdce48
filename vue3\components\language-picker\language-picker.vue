<template>
  <view class="language-picker">
    <view class="picker-item" @click="showPicker">
      <view class="label">{{ $t('语言切换') }}</view>
      <view class="value">
        <text class="current-lang">{{ currentLangName }}</text>
        <text class="iconfont icon-xiangyou"></text>
      </view>
    </view>
    
    <!-- 语言选择弹窗 -->
    <uni-popup ref="popup" type="bottom" :safe-area="false">
      <view class="popup-content">
        <view class="popup-header">
          <view class="popup-title">{{ $t('选择语言') }}</view>
          <view class="popup-close" @click="closePicker">
            <text class="iconfont icon-guanbi"></text>
          </view>
        </view>
        
        <view class="language-list">
          <view 
            v-for="(item, index) in langArray" 
            :key="index"
            class="language-item"
            :class="{ active: index === langIndex }"
            @click="selectLanguage(index)"
          >
            <view class="lang-name">{{ item.name }}</view>
            <view class="lang-check" v-if="index === langIndex">
              <text class="iconfont icon-duigou"></text>
            </view>
          </view>
        </view>
      </view>
    </uni-popup>
    
    <!-- 加载遮罩 -->
    <view v-if="langLoading" class="loading-mask">
      <view class="loading-content">
        <uni-load-more status="loading" :content-text="loadingText"></uni-load-more>
      </view>
    </view>
  </view>
</template>

<script>
import { getLangList, getLangJson } from '@/api/language'
import Cache from '@/utils/cache'

export default {
  name: 'LanguagePicker',
  
  data() {
    return {
      // 语言列表
      langArray: [],
      // 当前选中的语言索引
      langIndex: 0,
      // 语言切换加载状态
      langLoading: false,
      // 加载文本
      loadingText: {
        contentdown: '加载中...',
        contentrefresh: '加载中...',
        contentnomore: '加载完成'
      }
    }
  },
  
  computed: {
    // 当前语言名称
    currentLangName() {
      if (this.langArray.length > 0 && this.langArray[this.langIndex]) {
        return this.langArray[this.langIndex].name
      }
      return this.$i18n.locale === 'zh-CN' ? '简体中文' : 'English'
    }
  },
  
  mounted() {
    this.initLanguage()
  },
  
  methods: {
    /**
     * 初始化语言设置
     */
    async initLanguage() {
      // 从缓存加载语言包
      try {
        const cachedLocaleJson = uni.getStorageSync('localeJson')
        const currentLocale = this.$i18n.locale
        
        if (cachedLocaleJson && cachedLocaleJson[currentLocale]) {
          this.$i18n.setLocaleMessage(currentLocale, cachedLocaleJson[currentLocale])
        }
      } catch (error) {
        console.warn('加载缓存语言包失败:', error)
      }
      
      // 获取语言列表
      await this.getLangList()
    },
    
    /**
     * 获取语言列表
     */
    async getLangList() {
      try {
        const res = await getLangList()
        if (res.status === 200 && res.data) {
          this.langArray = res.data
        } else {
          throw new Error('获取语言列表失败')
        }
      } catch (error) {
        console.warn('获取语言列表失败，使用默认列表:', error)
        // 使用默认语言列表
        this.langArray = [
          { name: '简体中文', value: 'zh-CN' },
          { name: 'English', value: 'en-US' }
        ]
      } finally {
        this.setCurrentLangIndex()
      }
    },
    
    /**
     * 设置当前语言索引
     */
    setCurrentLangIndex() {
      const currentLocale = this.$i18n.locale
      this.langArray.forEach((item, index) => {
        if (item.value === currentLocale) {
          this.langIndex = index
        }
      })
    },
    
    /**
     * 显示语言选择器
     */
    showPicker() {
      if (this.langLoading) {
        return
      }
      this.$refs.popup.open()
    },
    
    /**
     * 关闭语言选择器
     */
    closePicker() {
      this.$refs.popup.close()
    },
    
    /**
     * 选择语言
     * @param {number} index 语言索引
     */
    async selectLanguage(index) {
      if (this.langLoading || index === this.langIndex) {
        this.closePicker()
        return
      }
      
      const targetLang = this.langArray[index]
      if (!targetLang) {
        return
      }
      
      this.langLoading = true
      this.closePicker()
      
      try {
        // 获取语言包数据
        const res = await getLangJson(targetLang.value)
        
        if (res.status === 200 && res.data) {
          // 缓存语言选择
          Cache.set('locale', targetLang.value)
          
          // 缓存语言包数据
          uni.setStorageSync('localeJson', res.data)
          
          // 设置语言包
          this.$i18n.setLocaleMessage(targetLang.value, res.data[targetLang.value] || {})
          
          // 更新当前语言
          this.$nextTick(() => {
            this.$i18n.locale = targetLang.value
            this.langIndex = index
          })
          
          // 提示切换成功
          uni.showToast({
            title: this.$t('切换成功'),
            icon: 'success',
            duration: 1500
          })
          
          // 发送语言切换事件
          this.$emit('language-changed', targetLang)
          
          // 延迟刷新页面以应用新语言
          setTimeout(() => {
            this.refreshPageForLanguage()
          }, 1000)
          
        } else {
          throw new Error('获取语言包失败')
        }
        
      } catch (error) {
        console.error('切换语言失败:', error)
        uni.showToast({
          title: this.$t('切换失败，请重试'),
          icon: 'none',
          duration: 2000
        })
      } finally {
        this.langLoading = false
      }
    },
    
    /**
     * 刷新页面以应用新语言
     */
    refreshPageForLanguage() {
      // 获取当前页面路径
      const pages = getCurrentPages()
      const currentPage = pages[pages.length - 1]
      const route = currentPage.route
      
      // 重新加载当前页面
      uni.reLaunch({
        url: `/${route}`
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.language-picker {
  position: relative;
}

.picker-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 0;
  font-size: 28rpx;
  color: #282828;
  
  .label {
    flex: 1;
  }
  
  .value {
    display: flex;
    align-items: center;
    color: #868686;
    
    .current-lang {
      margin-right: 10rpx;
    }
    
    .iconfont {
      font-size: 24rpx;
    }
  }
}

.popup-content {
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
  max-height: 60vh;
}

.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f2f2f2;
  
  .popup-title {
    font-size: 32rpx;
    font-weight: 500;
    color: #282828;
  }
  
  .popup-close {
    width: 60rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .iconfont {
      font-size: 28rpx;
      color: #999;
    }
  }
}

.language-list {
  max-height: 50vh;
  overflow-y: auto;
}

.language-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f8f8f8;
  
  &:last-child {
    border-bottom: none;
  }
  
  &.active {
    background: #f8f8f8;
    
    .lang-name {
      color: var(--view-theme, #e93323);
      font-weight: 500;
    }
  }
  
  .lang-name {
    font-size: 28rpx;
    color: #282828;
  }
  
  .lang-check {
    .iconfont {
      font-size: 28rpx;
      color: var(--view-theme, #e93323);
    }
  }
}

.loading-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  
  .loading-content {
    background: #fff;
    padding: 40rpx;
    border-radius: 20rpx;
    min-width: 200rpx;
    text-align: center;
  }
}
</style>
