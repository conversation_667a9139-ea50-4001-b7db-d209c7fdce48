
.uni-load-more[data-v-9245e42c] {
  display: flex;
  flex-direction: row;
  height: 1.875rem;
  align-items: center;
  justify-content: center;
}
.uni-load-more__text[data-v-9245e42c] {
  font-size: 0.75rem;
  margin-left: 0.5rem;
}
.uni-load-more__img[data-v-9245e42c] {
  width: 1.5rem;
  height: 1.5rem;
}
.uni-load-more__img--nvue[data-v-9245e42c] {
  color: #999;
}
.uni-load-more__img--android[data-v-9245e42c],
.uni-load-more__img--ios[data-v-9245e42c] {
  width: 1.5rem;
  height: 1.5rem;
  transform: rotate(0);
}
.uni-load-more__img--android[data-v-9245e42c] {
  animation: loading-ios 1s 0s linear infinite;
}
.uni-load-more__img--ios-H5[data-v-9245e42c] {
  position: relative;
  animation: loading-ios-H5-9245e42c 1s 0s step-end infinite;
}
.uni-load-more__img--ios-H5 uni-image[data-v-9245e42c] {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
}
@keyframes loading-ios-H5-9245e42c {
0% {
    transform: rotate(0);
}
8% {
    transform: rotate(30deg);
}
16% {
    transform: rotate(60deg);
}
24% {
    transform: rotate(90deg);
}
32% {
    transform: rotate(120deg);
}
40% {
    transform: rotate(150deg);
}
48% {
    transform: rotate(180deg);
}
56% {
    transform: rotate(210deg);
}
64% {
    transform: rotate(240deg);
}
73% {
    transform: rotate(270deg);
}
82% {
    transform: rotate(300deg);
}
91% {
    transform: rotate(330deg);
}
to {
    transform: rotate(360deg);
}
}
.uni-load-more__img--android-MP[data-v-9245e42c] {
  position: relative;
  width: 1.5rem;
  height: 1.5rem;
  transform: rotate(0);
  animation: loading-ios 1s 0s ease infinite;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon[data-v-9245e42c] {
  position: absolute;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: solid 0.125rem transparent;
  border-top: solid 0.125rem #999;
  transform-origin: center;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon[data-v-9245e42c]:nth-child(1) {
  animation: loading-android-MP-1-9245e42c 1s 0s linear infinite;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon[data-v-9245e42c]:nth-child(2) {
  animation: loading-android-MP-2-9245e42c 1s 0s linear infinite;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon[data-v-9245e42c]:nth-child(3) {
  animation: loading-android-MP-3-9245e42c 1s 0s linear infinite;
}
@keyframes loading-android-9245e42c {
0% {
    transform: rotate(0);
}
to {
    transform: rotate(360deg);
}
}
@keyframes loading-android-MP-1-9245e42c {
0% {
    transform: rotate(0);
}
50% {
    transform: rotate(90deg);
}
to {
    transform: rotate(360deg);
}
}
@keyframes loading-android-MP-2-9245e42c {
0% {
    transform: rotate(0);
}
50% {
    transform: rotate(180deg);
}
to {
    transform: rotate(360deg);
}
}
@keyframes loading-android-MP-3-9245e42c {
0% {
    transform: rotate(0);
}
50% {
    transform: rotate(270deg);
}
to {
    transform: rotate(360deg);
}
}


.uni-popup[data-v-4dd3c44b] {
  position: fixed;
  z-index: 99;
}
.uni-popup.top[data-v-4dd3c44b],
.uni-popup.left[data-v-4dd3c44b],
.uni-popup.right[data-v-4dd3c44b] {
  top: 0;
}
.uni-popup .uni-popup__wrapper[data-v-4dd3c44b] {
  display: block;
  position: relative;
  /* iphonex 等安全区设置，底部安全区适配 */
}
.uni-popup .uni-popup__wrapper.left[data-v-4dd3c44b],
.uni-popup .uni-popup__wrapper.right[data-v-4dd3c44b] {
  padding-top: 0;
  flex: 1;
}
.fixforpc-z-index[data-v-4dd3c44b] {

  z-index: 999;
}
.fixforpc-top[data-v-4dd3c44b] {
  top: 0;
}


.lazy-image[data-v-337fa078]{position:relative;width:100%;height:100%;overflow:hidden;background:#f8f8f8;}
.lazy-image uni-image[data-v-337fa078]{width:100%;height:100%;}
.lazy-image .err[data-v-337fa078]{position:absolute;top:0;left:0;width:100%;height:100%;background:rgba(248,248,248,0.7) url('../../static/img/load.png') no-repeat center/50%;}


.money-box[data-v-433ff6d7] {
  display: flex;
  align-items: flex-end;
  font-weight: bolder;
}
.money-box .sale[data-v-433ff6d7] {
  margin: 0 0.0625rem;
}
.money-box .unit[data-v-433ff6d7] {
  margin-bottom: 0.0625rem;
}


.waterfall-box[data-v-8e26fd7f] {
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 0 0.25rem;
  box-sizing: border-box;
}
.waterfall-item[data-v-8e26fd7f] {
  width: calc(50% - 0.125rem);
}
.waterfall-note[data-v-8e26fd7f] {
  margin-bottom: 0.25rem;
  border-radius: 0.25rem;
  overflow: hidden;
  background: #fff;
  box-shadow: 0 0.03125rem 0.125rem rgba(0, 0, 0, 0.03);
}
.waterfall-note .waterfall-note-top[data-v-8e26fd7f] {
  width: 100%;
  position: relative;
  overflow: hidden;
  background: #f8f8f8;
}
.waterfall-note-top.text-only[data-v-8e26fd7f] {
  background: #f8f8f8;
  padding: 0.5rem;
  box-sizing: border-box;
  width: 100%;
  border-radius: 0.25rem 0.25rem 0 0;
  display: flex;
  flex-direction: column;
}
.text-content[data-v-8e26fd7f] {
  margin: 0 !important;
  font-size: 0.8125rem;
  line-height: 1.125rem;
  color: #333;
  word-break: break-word;
  white-space: pre-wrap;
}
.waterfall-note-top .xxiv[data-v-8e26fd7f],
.waterfall-note-top .xxa .xxa-icon[data-v-8e26fd7f] {
  filter: drop-shadow(0 0.0625rem 0.0625rem rgba(0, 0, 0, 0.2));
}
.waterfall-note-top .xxiv[data-v-8e26fd7f] {
  position: absolute;
  top: 0.625rem;
  right: 0.625rem;
  width: 0.875rem;
  height: 0.875rem;
}
.waterfall-note-top .xxa[data-v-8e26fd7f] {
  width: calc(100% - 2rem);
  height: calc(100% - 2rem);
  padding: 1rem;
  position: relative;
  z-index: 1;
  color: #fff;
}
.waterfall-note-top .xxa .xxa-bg[data-v-8e26fd7f] {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -2;
  object-fit: cover;
}
.waterfall-note-top .xxa .xxa-mb[data-v-8e26fd7f] {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  -webkit-backdrop-filter: saturate(150%) blur(25px);
  backdrop-filter: saturate(150%) blur(25px);
  background: rgba(0, 0, 0, 0.5);
}
.waterfall-note-top .xxa .xxa-top[data-v-8e26fd7f] {
  width: 3.75rem;
  height: 3.75rem;
  border-radius: 0.5rem;
  position: relative;
  overflow: hidden;
}
.waterfall-note-top .xxa .xxa-top-img[data-v-8e26fd7f] {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  object-fit: cover;
}
.waterfall-note-top .xxa .xxa-icon[data-v-8e26fd7f] {
  position: absolute;
  top: 1.09375rem;
  right: 1.09375rem;
  bottom: 1.09375rem;
  left: 1.09375rem;
  width: 1.5625rem;
  height: 1.5625rem;
}
.waterfall-note-top .xxa .xxa-t[data-v-8e26fd7f] {
  margin-top: 1rem;
  font-size: 0.8125rem;
  font-weight: 700;
}
.waterfall-note-top .xxa .xxa-tt[data-v-8e26fd7f] {
  margin: 0.25rem 0 1rem;
  opacity: 0.8;
  font-size: 0.625rem;
}
.waterfall-note-top .xxa .xxa-play[data-v-8e26fd7f] {
  width: 100%;
  height: 1.875rem;
  font-size: 0.5625rem;
  font-weight: 700;
  border-radius: 1.875rem;
  justify-content: center;
  background: rgba(255, 255, 255, 0.15);
}
.waterfall-note-top .xxa .xxa-play uni-image[data-v-8e26fd7f] {
  margin-right: 0.25rem;
  width: 0.5rem;
  height: 0.5rem;
}
.waterfall-note-top .xxzd[data-v-8e26fd7f] {
  position: absolute;
  top: 0.5rem;
  left: 0.5rem;
  width: 1.625rem;
  height: 1rem;
  color: #fff;
  font-size: 0.5rem;
  justify-content: center;
  background: rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.16);
  border-radius: 0.25rem;
}
.waterfall-note .waterfall-note-content[data-v-8e26fd7f] {
  width: calc(100% - 1rem);
  margin: 0.375rem 0.5rem;
  font-size: 0.8125rem;
  line-height: 1.125rem;
  word-break: break-word;
  white-space: pre-line;
  color: #333;
}
.waterfall-note .waterfall-note-bottom[data-v-8e26fd7f] {
  width: calc(100% - 1rem);
  margin: 0 0.5rem;
  height: 1.875rem;
  justify-content: space-between;
}
.waterfall-note-bottom .waterfall-note-user[data-v-8e26fd7f] {
  display: flex;
  align-items: center;
}
.waterfall-note-bottom .waterfall-note-user uni-image[data-v-8e26fd7f] {
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
}
.waterfall-note-bottom .waterfall-note-like uni-image[data-v-8e26fd7f] {
  width: 0.875rem;
  height: 0.875rem;
}
.waterfall-note .waterfall-note-top[data-v-8e26fd7f],
.waterfall-note-bottom .waterfall-note-user uni-image[data-v-8e26fd7f],
.waterfall-activity .waterfall-activity-item[data-v-8e26fd7f],
.waterfall-activity .big[data-v-8e26fd7f],
.wlc10[data-v-8e26fd7f] {
  background: #f8f8f8;
}
.waterfall-note-bottom .waterfall-note-user uni-view[data-v-8e26fd7f],
.waterfall-note-bottom .waterfall-note-like uni-text[data-v-8e26fd7f] {
  margin-left: 0.25rem;
  line-height: 1rem;
}
.waterfall-note-bottom .waterfall-note-user uni-view[data-v-8e26fd7f] {
  color: #333;
  max-width: 4.375rem;
  font-size: 0.625rem;
}
.waterfall-note-bottom .waterfall-note-like uni-text[data-v-8e26fd7f] {
  color: #999;
  font-size: 0.625rem;
}
.wlc1[data-v-8e26fd7f] {
  -webkit-line-clamp: 1 !important;
}
.ohto[data-v-8e26fd7f] {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.ohto2[data-v-8e26fd7f] {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}
.df[data-v-8e26fd7f] {
  display: flex;
  align-items: center;
}

/* 活动区域样式调整 */
.waterfall-activity[data-v-8e26fd7f] {
  border-radius: 0.25rem;
  overflow: hidden;
  margin-bottom: 0.25rem;
}
.waterfall-activity-item[data-v-8e26fd7f] {
  border-radius: 0.25rem 0.25rem 0 0;
  overflow: hidden;
}
.waterfall-activity-img[data-v-8e26fd7f] {
  position: relative;
  border-radius: 0.25rem 0.25rem 0 0;
  overflow: hidden;
}
.waterfall-activity .big[data-v-8e26fd7f] {
  height: 2.25rem;
  border-radius: 0 0 0.25rem 0.25rem;
  font-size: 0.75rem;
  font-weight: 500;
  color: #333;
  justify-content: center;
}

/* 图片类卡片的图片样式 */
.lazy-image[data-v-8e26fd7f] {
  border-radius: 0.25rem 0.25rem 0 0;
  overflow: hidden;
}

/* 活动区域更详细样式（从原始组件复制） */
.waterfall-activity .waterfall-activity-item[data-v-8e26fd7f] {
  height: 14.6875rem;
  overflow: hidden;
}
.waterfall-activity-item .waterfall-activity-img[data-v-8e26fd7f] {
  margin-bottom: 0.5rem;
  width: 100%;
  height: 9.0625rem;
  position: relative;
}
.waterfall-activity-img .zt[data-v-8e26fd7f] {
  position: absolute;
  top: 0.5rem;
  left: 0.5rem;
  width: 2.125rem;
  height: 1.1875rem;
  color: #fff;
  font-size: 0.5rem;
  font-weight: 700;
  background: rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.16);
  border-radius: 0.25rem;
  justify-content: center;
}
.waterfall-activity-img .xxbt[data-v-8e26fd7f] {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  padding: 0.75rem 0 0.25rem;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));
}
.waterfall-activity-img .waterfall-activity-name[data-v-8e26fd7f],
.waterfall-activity-item .waterfall-activity-tag[data-v-8e26fd7f] {
  width: calc(100% - 1rem);
  padding: 0 0.5rem 0.25rem;
}
.waterfall-activity-img .waterfall-activity-name[data-v-8e26fd7f] {
  color: #fff;
  font-size: 0.75rem;
  font-weight: 700;
}
.waterfall-activity-item .waterfall-activity-tag uni-image[data-v-8e26fd7f],
.waterfall-activity .waterfall-activity-btn uni-image[data-v-8e26fd7f] {
  width: 0.625rem;
  height: 0.625rem;
}
.waterfall-activity-item .waterfall-activity-tag uni-view[data-v-8e26fd7f] {
  width: calc(100% - 0.875rem);
  color: #999;
  font-size: 0.625rem;
  font-weight: 500;
}
.waterfall-activity-group[data-v-8e26fd7f] {
  margin-left: 0.96875rem;
  direction: ltr;
  unicode-bidi: bidi-override;
  display: inline-block;
}
.waterfall-activity-group .group-img[data-v-8e26fd7f] {
  width: 1rem;
  height: 1rem;
  display: inline-flex;
  position: relative;
  margin-left: -0.5rem;
  border: 0.0625rem solid #f8f8f8;
  background: #fff;
  vertical-align: middle;
  border-radius: 50%;
}
.waterfall-activity-group .group-img uni-image[data-v-8e26fd7f] {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}
.waterfall-activity-group .group-tit[data-v-8e26fd7f] {
  display: inline-flex;
  margin-left: 0.25rem;
  color: #999;
  font-size: 0.625rem;
  font-weight: 500;
}
.waterfall-activity .waterfall-activity-btn[data-v-8e26fd7f] {
  margin-top: 0.25rem;
  font-weight: 700;
  justify-content: center;
}
.waterfall-activity .big[data-v-8e26fd7f] {
  width: 100%;
  font-size: 0.6875rem;
  height: 2.1875rem;
}
.waterfall-activity .small[data-v-8e26fd7f] {
  position: absolute;
  left: 0.5rem;
  bottom: 0.5rem;
  font-size: 0.625rem;
  width: calc(100% - 1rem);
  height: 1.875rem;
  background: #fff;
}


.gg-box[data-v-feabea4d] {
  border-bottom: 1px solid #f8f8f8;
  width: calc(100% - 1.875rem);
  padding: 0.9375rem;
  display: flex;
}
.gg-box .gg-avatar[data-v-feabea4d] {
  width: 2.125rem;
  height: 2.125rem;
  border-radius: 50%;
  border: 1px solid #f8f8f8;
  position: relative;
}
.gg-avatar .top[data-v-feabea4d] {
  position: absolute;
  right: -0.125rem;
  bottom: -0.125rem;
  width: 0.8125rem;
  height: 0.8125rem;
  border-radius: 50%;
  justify-content: center;
  background: #000;
}
.gg-avatar .top uni-image[data-v-feabea4d] {
  width: 100%;
  height: 100%;
}
.gg-box .gg-item[data-v-feabea4d] {
  width: calc(100% - 2.75rem - 2px);
  margin-left: 0.625rem;
}

/* 用户名容器 */
.gg-item .gg-item-user .name-container[data-v-feabea4d] {
  align-items: center;
}
.gg-item .gg-item-user .name[data-v-feabea4d] {
  color: #000;
  font-size: 0.875rem;
  line-height: 1.0625rem;
  font-weight: 700;
}

/* VIP图标样式 - 参考center.vue */
.gg-item .gg-item-user .status-icon[data-v-feabea4d] {
  width: 2.1875rem;
  height: 0.9375rem;
  margin-right: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
.gg-item .gg-item-user .vip-icon[data-v-feabea4d] {
  border-radius: 0.1875rem;
  padding: 0.0625rem;
}
.gg-item .gg-item-user .vip-icon uni-image[data-v-feabea4d] {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
.gg-item .gg-item-user .tag[data-v-feabea4d] {
  margin-left: 0.375rem;
  padding: 0 0.1875rem;
  height: 1.0625rem;
  border-radius: 0.125rem;
  background: #f5f5f5;
}
.gg-item-user .tag uni-image[data-v-feabea4d] {
  margin: 0 0.09375rem;
  width: 0.625rem;
  height: 0.625rem;
}
.gg-item-user .tag uni-text[data-v-feabea4d] {
  margin: 0 0.09375rem;
  font-size: 0.5625rem;
}
.gg-item .gg-item-content[data-v-feabea4d] {
  margin-top: 0.375rem;
  font-size: 0.8125rem;
  font-weight: 400;
  line-height: 1.125rem;
  word-break: break-word;
  white-space: pre-line;
}
.gg-item .gg-item-file[data-v-feabea4d] {
  margin-top: 0.625rem;
  display: flex;
  position: relative;
  z-index: 1;
  flex-wrap: wrap;
}
.gg-item-file .file-h[data-v-feabea4d],
.gg-item-file .file-w[data-v-feabea4d],
.gg-item-file .file-img[data-v-feabea4d] {
  border-radius: 0.25rem;
  overflow: hidden;
}
.gg-item-file .file-h[data-v-feabea4d] {
  width: 10rem;
  height: 13.125rem;
}
.gg-item-file .file-w[data-v-feabea4d] {
  width: 13.125rem;
  height: 10rem;
}
.gg-item-file .file-img[data-v-feabea4d] {
  width: 6.125rem;
  height: 6.125rem;
  margin-right: 0.125rem;
  margin-bottom: 0.125rem;
}
.gg-item-file .file-img[data-v-feabea4d]:nth-child(3n) {
  margin-right: 0 !important;
}
.gg-item-file .file-count[data-v-feabea4d] {
  position: absolute;
  right: 0.625rem;
  bottom: 0.9375rem;
  padding: 0 0.3125rem;
  height: 1.25rem;
  color: #fff;
  font-size: 0.625rem;
  font-weight: 700;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 0.25rem;
}
.gg-item-file .file-count uni-image[data-v-feabea4d],
.gg-item-file .file-video uni-image[data-v-feabea4d] {
  width: 0.625rem;
  height: 0.625rem;
}
.gg-item-file .file-count uni-image[data-v-feabea4d] {
  margin-right: 0.3125rem;
}
.gg-item-file .file-video[data-v-feabea4d] {
  position: absolute;
  top: 0.625rem;
  width: 1.5rem;
  height: 1.5rem;
  background: rgba(0, 0, 0, 0.6);
  justify-content: center;
  border-radius: 50%;
}
.gg-item-file .file-audio[data-v-feabea4d] {
  width: 100%;
  height: 4.375rem;
  border-radius: 0.25rem;
  color: #fff;
  position: relative;
  overflow: hidden;
}
.file-audio .audio-left[data-v-feabea4d] {
  margin-right: 0.9375rem;
  width: 4.375rem;
  height: 4.375rem;
  position: relative;
}
.file-audio .audio-left .icon[data-v-feabea4d] {
  position: absolute;
  top: 1.40625rem;
  right: 1.40625rem;
  bottom: 1.40625rem;
  left: 1.40625rem;
  width: 1.5625rem;
  height: 1.5625rem;
}
.file-audio .audio-bg[data-v-feabea4d],
.file-audio .audio-mb[data-v-feabea4d] {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.file-audio .audio-mb[data-v-feabea4d] {
  z-index: -1;
  -webkit-backdrop-filter: saturate(150%) blur(25px);
  backdrop-filter: saturate(150%) blur(25px);
  background: rgba(0, 0, 0, 0.5);
}
.file-audio .audio-t1[data-v-feabea4d] {
  font-size: 0.8125rem;
  font-weight: 700;
}
.file-audio .audio-t2[data-v-feabea4d] {
  margin-top: 0.3125rem;
  opacity: 0.8;
  font-size: 0.625rem;
}
.file-audio .audio-play[data-v-feabea4d] {
  margin: 0 0.9375rem;
  width: 3.125rem;
  height: 1.875rem;
  line-height: 1.875rem;
  text-align: center;
  font-size: 0.5625rem;
  font-weight: 700;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 1.875rem;
}
.gg-item .gg-item-g[data-v-feabea4d] {
  margin-top: 0.3125rem;
  width: 100%;
  display: flex;
  flex-wrap: wrap;
}
.gg-item-g .g-item[data-v-feabea4d] {
  margin: 0.3125rem 0.3125rem 0 0;
  padding: 0.25rem;
  font-size: 0.625rem;
  font-weight: 500;
  border-radius: 0.25rem;
  background: #f8f8f8;
}
.g-item .g-item-img[data-v-feabea4d] {
  width: 1.25rem;
  height: 1.25rem;
  background: #f8f8f8;
  border-radius: 0.125rem;
  overflow: hidden;
}
.gg-item .gg-item-time[data-v-feabea4d] {
  margin-top: 0.25rem;
  margin-bottom: 0.375rem;
  color: #999;
  font-size: 0.625rem;
}
.gg-item .gg-item-comment[data-v-feabea4d] {
  margin-top: 0.625rem;
  width: calc(100% - 1.25rem);
  padding: 0.625rem;
  color: #999;
  font-size: 0.75rem;
  font-weight: 400;
  word-break: break-word;
  white-space: pre-line;
  border-radius: 0.25rem;
  background: #f8f8f8;
}
.gg-item .gg-item-unm[data-v-feabea4d] {
  display: flex;
  align-items: center;
  width: 100%;
}
.gg-item-unm .unm-item[data-v-feabea4d] {
  margin-top: 0.9375rem;
  display: flex;
  align-items: center;
}
.gg-item-unm .unm-item uni-image[data-v-feabea4d] {
  width: 1.375rem;
  height: 1.375rem;
}
.gg-item-unm .unm-item uni-text[data-v-feabea4d] {
  margin: 0 0.9375rem 0 0.1875rem;
  color: #999;
  font-size: 0.5625rem;
  font-weight: 700;
}
.wlc8[data-v-feabea4d] {
  -webkit-line-clamp: 8 !important;
  line-clamp: 8 !important;
}
.df[data-v-feabea4d] {
  display: flex;
  align-items: center;
}
.ohto[data-v-feabea4d] {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.ohto2[data-v-feabea4d] {
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  line-clamp: 4;
  -webkit-box-orient: vertical;
}

/* 关注按钮样式 */
.gg-item-user .follow-btn[data-v-feabea4d] {
  margin-left: auto;
  padding: 0 0.625rem;
  height: 1.5rem;
  line-height: 1.5rem;
  border-radius: 0.25rem;
  font-size: 0.625rem;
  font-weight: 700;
  color: #000;
  background: #f8f8f8;
  text-align: center;
}
.gg-item-user .follow-btn.active[data-v-feabea4d] {
  color: #999;
  background: #f5f5f5;
}
.gg-item-user .follow-btn.mutual[data-v-feabea4d] {
  color: #576b95;
  background: rgba(87, 107, 149, 0.1);
}

/* 移除原有 .topic-tag 样式，话题和圈子统一用 g-item/g-item-img/text 结构 */
.topic-tag[data-v-feabea4d],
.topic-tag uni-text[data-v-feabea4d] {
  /* 移除样式 */
  display: none !important;
}

/* 评论样式 */
.gg-item .gg-item-comments[data-v-feabea4d] {
  margin-top: 0.625rem;
  width: calc(100% - 1.25rem);
  padding: 0.625rem;
  font-size: 0.75rem;
  border-radius: 0.25rem;
  background: #f8f8f8;
}
.gg-item-comments .comment-item[data-v-feabea4d] {
  margin-bottom: 0.46875rem;
  padding-bottom: 0.46875rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}
.gg-item-comments .comment-item[data-v-feabea4d]:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}
.gg-item-comments .comment-user[data-v-feabea4d] {
  font-size: 0.75rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.1875rem;
}
.gg-item-comments .comment-content[data-v-feabea4d] {
  font-size: 0.75rem;
  color: #666;
  line-height: 1rem;
  word-break: break-all;
}
.gg-item-comments .comment-footer[data-v-feabea4d] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 0.3125rem;
}
.gg-item-comments .comment-time[data-v-feabea4d] {
  font-size: 0.625rem;
  color: #999;
}
.gg-item-comments .comment-like[data-v-feabea4d] {
  display: flex;
  align-items: center;
}
.gg-item-comments .comment-like uni-image[data-v-feabea4d] {
  width: 0.875rem;
  height: 0.875rem;
  margin-right: 0.125rem;
}
.gg-item-comments .comment-like uni-text[data-v-feabea4d] {
  font-size: 0.625rem;
  color: #999;
}
.gg-item-comments .comment-like uni-text.active[data-v-feabea4d] {
  color: #FA5150;
}
.gg-item-comments .more-comments[data-v-feabea4d] {
  margin-top: 0.46875rem;
  text-align: center;
  font-size: 0.6875rem;
  color: #576b95;
  padding: 0.3125rem 0;
}

/* ==== 投票展示样式（复用add.vue） ==== */
.vote-box[data-v-feabea4d] {
  width: 100%;
  margin-top: 0.5rem;
}
.vote-container[data-v-feabea4d] {
  width: 100%;
  background-color: #f5f5f5;
  border-radius: 0.5rem;
  position: relative;
}
.vote-header[data-v-feabea4d] {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  padding: 0.625rem;
}
.vote-title-container[data-v-feabea4d] {
  display: flex;
  align-items: center;
}
.vote-icon[data-v-feabea4d] {
  width: 1rem;
  height: 1rem;
  margin-right: 0.3125rem;
}
.vote-title[data-v-feabea4d] {
  font-size: 0.875rem;
  font-weight: 500;
  color: #333;
  text-align: left;
  white-space: normal;
  word-break: break-word;
}
.vote-options[data-v-feabea4d] {
  display: flex;
  flex-direction: column;
  padding: 0 0;
}
.vote-option-voted[data-v-feabea4d] {
  background: #f5f5f5;
  border-radius: 1rem;
  margin-bottom: 0.5rem;
  padding: 0;
  border: none;
  position: relative;
  transition: background 0.2s, border 0.2s;
}
.vote-option-voted.selected[data-v-feabea4d] {
  background: #fff;
}
.vote-bar-bg[data-v-feabea4d] {
    border-radius: 18px;
    font-size: 17px;
    color: #333;
    margin-bottom: 10px;
    border: none;
    box-shadow: none;
    text-align: center;
    transition: background 0.2s, border 0.2s;
    margin-left: 11px;
    margin-right: 11px;
    min-height: 27px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 11px;
    box-sizing: border-box;
}
.vote-option-voted.selected .vote-bar-bg[data-v-feabea4d] {
  background: #fff;
}
.vote-bar[data-v-feabea4d] {
  height: 100%;
  border-radius: 1rem;
  transition: width 0.3s;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 1;
}
.vote-row[data-v-feabea4d] {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
  pointer-events: none;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 0.625rem;
}
.vote-left[data-v-feabea4d] {
  display: flex;
  align-items: center;
}
.vote-option-unvoted[data-v-feabea4d] {
  background: #fff;
  border-radius: 1rem;
  font-size: 0.8125rem;
  color: #333;
  margin-bottom: 0.5625rem;
  border: none;
  box-shadow: none;
  text-align: center;
  transition: background 0.2s, border 0.2s;
  margin-left: 0.625rem;
  margin-right: 0.625rem;
  position: relative;
  min-height: 2.0625rem;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 0.625rem;
  box-sizing: border-box;
}
.vote-progress-bar[data-v-feabea4d] {
  position: absolute;
  left: 0;
  bottom: 0;
  height: 0.375rem;
  border-radius: 1rem;
  z-index: 1;
  background: #ffd600;
  transition: width 0.3s;
}
.vote-option-unvoted:not(.selected) .vote-progress-bar[data-v-feabea4d] {
  background: #eaeaea;
}
.vote-checked-icon[data-v-feabea4d] {
  width: 0.875rem;
  height: 0.875rem;
  background: #ffd600;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.3125rem;
}
.vote-checked-icon uni-image[data-v-feabea4d] {
  width: 0.625rem;
  height: 0.625rem;
  display: block;
}
.vote-content[data-v-feabea4d] {
  font-size: 0.8125rem;
  color: #333;
  text-align: left;
  white-space: normal;
  word-break: break-word;
  font-weight: 500;
}
.vote-option-voted.selected .vote-content[data-v-feabea4d] {
  color: #000;
}
.vote-percent[data-v-feabea4d] {
  font-size: 0.8125rem;
  color: #000000;
  text-align: right;
  margin-left: 0.375rem;
  min-width: 1.5rem;
  flex-shrink: 0;
  white-space: nowrap;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  font-weight: 500;
  margin-right: 1.25rem;
  position: relative;
}
.vote-people[data-v-feabea4d] {
  margin-top: 0.5rem;
  color: #999;
  font-size: 0.75rem;
  text-align: left;
  padding-left: 0.625rem;
  padding-bottom: 0.625rem;
}



.container {
  width: 100%;
  padding-bottom: calc(env(safe-area-inset-bottom) + 5rem);
}
.nav-box {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 99;
  box-sizing: border-box;
}
.nav-box .nav-back {
  padding: 0 0.9375rem;
  width: 1.0625rem;
  height: 100%;
}
.nav-box .nav-back uni-image {
  width: 1.0625rem;
  height: 1.0625rem;
}
.nav-box .nav-title {
  max-width: 60%;
  font-size: 1rem;
  font-weight: 700;
}
.swiper-box {
  z-index: -1;
  width: 100%;
  height: auto;
  aspect-ratio: 5/4;
  overflow: hidden;
}
.swiper-box .swiper-item {
  width: 100%;
  height: 100%;
}
.indicator {
  margin-top: -2.125rem;
  width: 100%;
  justify-content: center;
}
.indicator .indicator-item {
  z-index: 1;
  margin: 0 0.1875rem;
  width: 0.25rem;
  height: 0.25rem;
  border-radius: 0.25rem;
  background: rgba(255, 255, 255, 0.3);
  transition: all 0.3s;
}
.indicator .active {
  width: 0.75rem;
  background: rgba(255, 255, 255, 0.9);
}
.arr {
  width: 1.375rem;
  height: 1.375rem;
  background: #fff;
  border-radius: 50%;
  justify-content: center;
}
.arr uni-image {
  width: 0.625rem;
  height: 0.625rem;
}
.content-box {
  width: 100%;
  padding-top: 0.9375rem;
}
.content-box .content-bar {
  position: -webkit-sticky;
  position: sticky;
  left: 0;
  z-index: 99;
  width: 100%;
  height: 3.125rem;
  background: #fff;
  border-radius: 0.9375rem 0.9375rem 0 0;
  justify-content: space-between;
}
.content-bar .bar-nav {
  padding: 0 0.46875rem;
  height: 2.5rem;
  flex-direction: column;
  justify-content: center;
  position: relative;
}
.content-bar .bar-nav uni-text {
  font-weight: 700;
  transition: all 0.3s ease-in-out;
}
.content-bar .bar-nav .line {
  position: absolute;
  bottom: 0.375rem;
  width: 0.5625rem;
  height: 0.1875rem;
  border-radius: 0.1875rem;
  background: #000;
  transition: opacity 0.3s ease-in-out;
}
.content-bar .bar-btn {
  padding: 0 0.5rem;
  height: 1.5625rem;
  line-height: 1.5625rem;
  border-radius: 0.25rem;
  background: #fff;
  border: 1px solid #f5f5f5;
}
.bar-btn uni-text {
  margin-left: 0.25rem;
  font-size: 0.625rem;
  font-weight: 700;
}
.bar-btn .avatar {
  width: 0.9375rem;
  height: 0.9375rem;
  border-radius: 50%;
}
.bar-btn .icon {
  width: 0.625rem;
  height: 0.625rem;
}
.content-bar .s1 {
  color: #fa5150;
  background: rgba(250, 81, 80, 0.082);
  border: 1px solid #FA515015;
}
.content-bar .s2 {
  color: #000;
  background: rgba(0, 0, 0, 0.082);
  border: 1px solid #000;
}
.content-box .content-item {
  width: calc(100% - 1.875rem);
  padding: 0 0.9375rem;
}
.content-box .dynamic-box {
  width: calc(100% - 0.5rem);
  padding: 0 0.25rem;
}
.content-box .joins-box {
  margin-bottom: 0.9375rem;
  width: calc(100% - 1.875rem);
  padding: 0.9375rem;
  background: #000;
  border-radius: 0.25rem;
  justify-content: space-between;
}
.content-box .joins-box .txt {
  color: #fff;
  font-size: 0.6875rem;
  font-weight: 700;
}
.content-box .joins-box .txt uni-image {
  margin-right: 0.25rem;
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
}
.content-box .info-map {
  width: calc(100% - 2px);
  border-radius: 0.25rem;
  border: 1px solid #f5f5f5;
  position: relative;
  overflow: hidden;
}
.content-box .info-map .bg, .content-box .info-map .mk {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.content-box .info-map .mk {
  z-index: -1;
  background-image: linear-gradient(45deg, #fff, rgba(255, 255, 255, 0.6));
}
.content-box .info-item {
  width: calc(100% - 1.875rem);
  padding: 0.9375rem;
  font-weight: 700;
}
.info-item .info-item-tit {
  color: #999;
  font-size: 0.75rem;
  margin-bottom: 0.25rem;
}
.info-item .info-item-tit uni-text {
  color: #000;
}
.info-item .adds-box {
  width: 1.375rem;
  height: 1.375rem;
  justify-content: center;
  border-radius: 0.6875rem 0.6875rem 0.125rem;
  box-shadow: 5px 5px 5px -4px rgba(0, 0, 0, 0.1);
  background: #000;
  transform: rotate(45deg);
}
.info-item .adds-box uni-image {
  width: 0.75rem;
  height: 0.75rem;
  transform: rotate(-45deg);
}
.info-item .cu-img-group {
  margin-left: 0.5rem;
  direction: ltr;
  unicode-bidi: bidi-override;
  display: inline-block;
}
.cu-img-group .img-group {
  width: 1rem;
  height: 1rem;
  display: inline-flex;
  position: relative;
  margin-left: -0.5rem;
  border: 0.0625rem solid #f8f8f8;
  background: #eee;
  vertical-align: middle;
  border-radius: 0.25rem;
  border-radius: 50%;
}
.cu-img-group .img-group uni-image {
  width: 100%;
  height: 100%;
  border-radius: 0.25rem;
  border-radius: 50%;
}
.cu-img-group .img-tit {
  display: inline-flex;
  margin-left: 0.25rem;
  color: #999;
  font-size: 0.625rem;
}
.content-box .info-title {
  margin: 0.9375rem 0;
  width: 100%;
  font-size: 1rem;
  font-weight: 700;
}
.content-box .info-intro {
  width: 100%;
  color: #333;
  font-size: 0.8125rem;
  font-weight: 400;
  word-break: break-word;
  white-space: pre-line;
}
.footer-box {
  position: fixed;
  z-index: 99;
  left: 0;
  bottom: 0;
  width: calc(100% - 1.875rem);
  padding: 0.9375rem;
  border-top: 1px solid #f8f8f8;
  padding-bottom: max(env(safe-area-inset-bottom), 0.9375rem);
}
.btn-box {
  width: 100%;
  justify-content: space-between;
}
.btn-box .btn-price {
  height: 3.125rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.btn-price .nian {
  margin-right: 0.1875rem;
  font-size: 0.625rem;
  font-weight: 700;
}
.btn-price .through {
  color: #999;
  font-size: 0.625rem;
  text-decoration: line-through;
}
.btn-box .btn-item {
  padding: 0 1.875rem;
  height: 3.125rem;
  font-size: 0.75rem;
  color: #fff;
  font-weight: 700;
  background: #000;
  border-radius: 3.125rem;
}
.btn-box .btn-means {
  padding: 0 0.9375rem;
  height: 2.8125rem;
  font-size: 0.625rem;
  font-weight: 700;
  background: #f5f5f5;
  border-radius: 3.125rem;
}
.btn-box .btn-means uni-image {
  margin-right: 0.375rem;
  width: 0.625rem;
  height: 0.625rem;
}
.btn-box .btn-item .icon {
  margin-right: 0.375rem;
  width: 1rem;
  height: 1rem;
}
.popup-box {
  width: calc(100% - 1.875rem);
  padding: 0.9375rem;
  background: #fff;
  border-radius: 0.9375rem 0.9375rem 0 0;
  padding-bottom: max(env(safe-area-inset-bottom), 1.875rem);
  position: relative;
}
.popup-box .popup-top {
  width: 100%;
  justify-content: space-between;
}
.popup-top .popup-title .t1 {
  font-size: 1.1875rem;
  font-weight: 700;
}
.popup-top .popup-title .t2 {
  color: #999;
  font-size: 0.625rem;
  font-weight: 300;
}
.popup-top .popup-close {
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 50%;
  background: #f8f8f8;
  justify-content: center;
  transform: rotate(45deg);
}
.popup-box .popup-btn {
  margin: 1.875rem 0 0.9375rem;
  width: 100%;
  height: 3.125rem;
  font-size: 0.75rem;
  color: #fff;
  font-weight: 700;
  background: #000;
  border-radius: 3.125rem;
  justify-content: center;
}
.popup-box .popup-btn uni-image {
  margin-right: 0.375rem;
  width: 1rem;
  height: 1rem;
}
.scroll-box {
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
}
.popup-box .product-box {
  width: 100%;
  padding: 1.4375rem 0 0.9375rem;
  display: flex;
}
.product-box .product-item {
  flex-shrink: 0;
  margin-right: 0.625rem;
  padding: 1.125rem 0.625rem 0.625rem;
  border-radius: 0.25rem;
  background: #f8f8f8;
  border: 1px solid #f8f8f8;
  position: relative;
}
.product-box .active {
  background: #fff;
  border: 1px solid #000;
}
.product-item .tag {
  position: absolute;
  top: -0.5rem;
  left: -1px;
  padding: 0 0.5rem;
  height: 1rem;
  line-height: 1rem;
  text-align: center;
  font-size: 0.5625rem;
  font-weight: 500;
  color: #fff;
  background: linear-gradient(to right, #000, #555);
  border-radius: 0.75rem 1rem 1rem 0;
}
.product-item .name {
  font-size: 0.75rem;
  line-height: 0.75rem;
  font-weight: 500;
}
.product-item .time {
  margin: 0.46875rem 0;
  font-weight: 300;
  font-size: 0.625rem;
  line-height: 0.625rem;
}
.product-item .td-lt {
  margin-left: 0.46875rem;
  color: #999;
  font-size: 0.625rem;
  line-height: 0.625rem;
  text-decoration: line-through;
}
.popup-box .quantity-box {
  padding: 0.9375rem 0;
  width: 100%;
  justify-content: space-between;
  border-top: 1px solid #f8f8f8;
}
.quantity-box .quantity-tit {
  font-size: 0.75rem;
}
.quantity-box .quantity-item {
  height: 2rem;
  line-height: 2rem;
  border-radius: 1rem;
  border: 1px solid #f5f5f5;
  font-size: 0.75rem;
  font-weight: 700;
  text-align: center;
}
.quantity-item uni-input {
  width: 1.5rem;
  height: 2rem;
  line-height: 2rem;
  color: #000;
}
.quantity-item .quantity-btn {
  width: 2rem;
  height: 2rem;
  line-height: 2rem;
}
.share-popup {
  background: #fff;
  border-radius: 0.9375rem;
  padding: 0.9375rem;
  overflow: hidden;
}
.share-popup .share-img {
  width: 14.78125rem;
  height: 7.42188rem;
  background: #f8f8f8;
  border-radius: 0.25rem;
  display: block;
}
.share-popup .share-tips {
  margin: 0.9375rem 0;
  width: 14.78125rem;
  font-size: 0.8125rem;
  line-height: 1.5rem;
  position: relative;
}
.share-popup .share-tips uni-image {
  position: absolute;
  top: 0;
  width: 1.5rem;
  height: 1.5rem;
  margin: 0 0.46875rem;
}
.share-popup .share-btn {
  width: 100%;
  height: 2.5rem;
  line-height: 2.5rem;
  text-align: center;
  font-size: 0.75rem;
  font-weight: 700;
  color: #fff;
  background: #000;
  border-radius: 0.5rem;
}
.note-box {
  padding: 0.46875rem;
  background: #fff;
  border-radius: 0.9375rem;
}
.note-box .note-add {
  margin: 0.9375rem;
  width: 12.5rem;
  height: 2.8125rem;
  font-size: 0.75rem;
  font-weight: 700;
  color: #fff;
  background: #000;
  border-radius: 1.40625rem;
  justify-content: center;
}
.note-box .note-add uni-image {
  margin-right: 0.3125rem;
  width: 1.25rem;
  height: 1.25rem;
}
.empty-box {
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3.125rem 0;
}
.empty-box uni-image {
  width: 6.25rem;
  height: 6.25rem;
  margin-bottom: 0.9375rem;
}
.empty-box .e1 {
  font-size: 0.875rem;
  font-weight: bold;
  margin-bottom: 0.3125rem;
}
.empty-box .e2 {
  font-size: 0.75rem;
  color: #999;
}
.tips-box {
  padding: 0.625rem 0.9375rem;
  border-radius: 0.375rem;
  justify-content: center;
}
.tips-box .tips-item {
  color: #fff;
  font-size: 0.875rem;
  font-weight: 700;
}
.xwb {
  filter: brightness(0);
}
.df {
  display: flex;
  align-items: center;
}
.bfw {
  background: #fff;
}
.bUp {
  box-shadow: 0 -2px 5px 0 rgba(0, 0, 0, 0.05);
}
.ohto {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.effect {
  transition: all 0.3s ease-in-out;
}
