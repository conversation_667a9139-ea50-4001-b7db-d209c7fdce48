
.uni-popup[data-v-4dd3c44b] {
  position: fixed;
  z-index: 99;
}
.uni-popup.top[data-v-4dd3c44b],
.uni-popup.left[data-v-4dd3c44b],
.uni-popup.right[data-v-4dd3c44b] {
  top: 0;
}
.uni-popup .uni-popup__wrapper[data-v-4dd3c44b] {
  display: block;
  position: relative;
  /* iphonex 等安全区设置，底部安全区适配 */
}
.uni-popup .uni-popup__wrapper.left[data-v-4dd3c44b],
.uni-popup .uni-popup__wrapper.right[data-v-4dd3c44b] {
  padding-top: 0;
  flex: 1;
}
.fixforpc-z-index[data-v-4dd3c44b] {

  z-index: 999;
}
.fixforpc-top[data-v-4dd3c44b] {
  top: 0;
}


.empty-page[data-v-e63e7e29] {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3.125rem 1.25rem;
  min-height: 12.5rem;
}
.empty-icon[data-v-e63e7e29] {
  margin-bottom: 0.9375rem;
}
.empty-icon .icon[data-v-e63e7e29] {
  font-size: 3.75rem;
  opacity: 0.3;
}
.empty-title[data-v-e63e7e29] {
  margin-bottom: 0.625rem;
}
.empty-title uni-text[data-v-e63e7e29] {
  font-size: 0.875rem;
  color: #999;
  text-align: center;
}
.empty-description[data-v-e63e7e29] {
  text-align: center;
}
.empty-description uni-text[data-v-e63e7e29] {
  font-size: 0.75rem;
  color: #ccc;
  line-height: 1.5;
}


.content-box {
  width: calc(100% - 1.875rem);
  padding: 0.9375rem;
  background: #f8f9fa;
  min-height: 100vh;
}

/* 加载中状态样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 1.875rem;
  margin-bottom: 0.625rem;
}
.loading-indicator {
  width: 0.9375rem;
  height: 0.9375rem;
  border: 0.09375rem solid #f3f3f3;
  border-top: 0.09375rem solid #000;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}
@keyframes spin {
0% { transform: rotate(0deg);
}
100% { transform: rotate(360deg);
}
}

/* 签到记录列表样式 */
.sign-list {
  width: 100%;
}
.month-group {
  margin-bottom: 1.25rem;
}
.month-title {
  font-size: 0.875rem;
  color: #999;
  margin-bottom: 0.625rem;
  padding-left: 0.3125rem;
}
.sign-items {
  background: #fff;
  border-radius: 0.5rem;
  overflow: hidden;
}
.sign-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 0.9375rem;
  border-bottom: 0.03125rem solid #f5f5f5;
}
.sign-item:last-child {
  border-bottom: none;
}
.sign-info {
  flex: 1;
}
.sign-title {
  font-size: 0.9375rem;
  color: #333;
  font-weight: 500;
  margin-bottom: 0.25rem;
  line-height: 1.4;
}
.sign-date {
  font-size: 0.8125rem;
  color: #999;
}
.sign-reward {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}
.reward-text {
  font-size: 1rem;
  color: #ff6b35;
  font-weight: bold;
}
.reward-icon {
  width: 1.25rem;
  height: 1.25rem;
}

/* 底部加载状态 */
.load-more {
  text-align: center;
  padding: 1.25rem 0;
}
.loading-text,
.no-more-text,
.load-more-text {
  font-size: 0.875rem;
  color: #999;
}
.load-more-text {
  color: #007aff;
}

/* 空状态 */
.empty-box {
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3.125rem 0;
}
.empty-box uni-image {
  width: 6.25rem;
  height: 6.25rem;
  margin-bottom: 0.9375rem;
}
.empty-box .e1 {
  font-size: 0.875rem;
  font-weight: bold;
  margin-bottom: 0.3125rem;
}
.empty-box .e2 {
  font-size: 0.75rem;
  color: #999;
}
.df {
  display: flex;
  align-items: center;
}
.bfh {
  background: #000;
  color: #fff;
  padding: 0.625rem 1.25rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 700;
}
.tips-box {
  justify-content: center;
  width: 100%;
}
