"use strict";
const common_vendor = require("../../common/vendor.js");
const api_user = require("../../api/user.js");
const api_social = require("../../api/social.js");
const libs_login = require("../../libs/login.js");
const stores_user = require("../../stores/user.js");
const stores_app = require("../../stores/app.js");
const stores_social = require("../../stores/social.js");
const common_assets = require("../../common/assets.js");
const lazyImage = () => "../../components/lazyImage/lazyImage.js";
const uniLoadMore = () => "../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js";
const cardWd = () => "../../components/card-wd/card-wd.js";
const cardGg = () => "../../components/card-gg/card-gg.js";
const tabbar = () => "../../components/tabbar/tabbar.js";
const uniPopup = () => "../../uni_modules/uni-popup/components/uni-popup/uni-popup.js";
const _sfc_main = {
  components: {
    lazyImage,
    uniLoadMore,
    cardWd,
    cardGg,
    tabbar,
    uniPopup
  },
  computed: {
    userStore() {
      return stores_user.useUserStore();
    },
    appStore() {
      return stores_app.useAppStore();
    },
    socialStore() {
      return stores_social.useSocialStore();
    },
    isLogin() {
      try {
        const token = common_vendor.index.getStorageSync("token") || common_vendor.index.getStorageSync("LOGIN_STATUS_TOKEN");
        const userInfo = common_vendor.index.getStorageSync("USER_INFO");
        const isLoggedIn = !!(token && userInfo && userInfo.uid);
        common_vendor.index.__f__("log", "at pages/tabbar/center.vue:335", "登录状态检查详情:", {
          token: token ? "有token" : "无token",
          tokenLength: token ? token.length : 0,
          userInfo: userInfo ? "有用户信息" : "无用户信息",
          userInfoType: typeof userInfo,
          uid: userInfo ? userInfo.uid : "无uid",
          isLoggedIn
        });
        return isLoggedIn;
      } catch (error) {
        common_vendor.index.__f__("warn", "at pages/tabbar/center.vue:346", "检查登录状态失败:", error);
        return false;
      }
    }
  },
  data() {
    return {
      statusBarHeight: 20,
      titleBarHeight: 44,
      currentMsg: 0,
      scrollTop: 0,
      navbarTrans: 0,
      userInfo: {
        avatar: "",
        nickname: "您还未登录哦~",
        follow_count: 0,
        fans_count: 0,
        like_count: 0,
        like_count_str: "0",
        visitor_count: 0,
        visitor_badge: 0,
        is_verified: 0,
        is_money_level: 0,
        svip_open: 0,
        sex: 0,
        user_id_number: "",
        about_me: "",
        interest_tags: [],
        vip_status: 0,
        overdue_time: "",
        card_count: 0,
        activity_count: 0,
        activity_img: ""
      },
      blockList: [
        { name: "圈子", img: "", icon: "/static/img/qz.png", url: "center/circle?type=1", count: 0 },
        { name: "购物车", img: "", icon: "/static/img/gwc.png", url: "goods/cart", count: 0 },
        { name: "订单", img: "", icon: "/static/img/dd.png", url: "order/index", count: 0 }
      ],
      barList: ["笔记", "赞过"],
      barIdx: 0,
      list: [],
      page: 1,
      totalCount: 0,
      isEmpty: true,
      // 初始状态应该是空的
      loadStatus: "more",
      showSidebar: false,
      sidebarMenu: [],
      isLoading: false,
      isThrottling: false,
      appCard: true,
      // 简化的加载状态管理
      loading: {
        userInfo: false,
        dynamicList: false,
        refreshing: false
      },
      // 简化的缓存管理
      lastRefreshTime: 0,
      cacheTimeout: 3e5,
      // 5分钟缓存
      // 首次加载标记
      isFirstLoad: true
    };
  },
  onPullDownRefresh() {
    if (!this.isLogin) {
      common_vendor.index.stopPullDownRefresh();
      common_vendor.index.__f__("log", "at pages/tabbar/center.vue:416", "未登录，停止下拉刷新");
      return;
    }
    this.refreshData();
  },
  onLoad() {
    this.initBasicSettings();
    this.initData();
  },
  onUnload() {
    common_vendor.index.$off("userInfoUpdated", this.handleUserInfoUpdate);
    common_vendor.index.$off("loginStateChanged", this.handleLoginStateChanged);
    this.clearAllTimers();
  },
  onShow() {
    this.navigationBarColor(0);
    if (this.isFirstLoad) {
      this.isFirstLoad = false;
      return;
    }
    common_vendor.index.__f__("log", "at pages/tabbar/center.vue:444", "onShow - 页面显示:", {
      isLogin: this.isLogin,
      listLength: this.list.length,
      isEmpty: this.isEmpty,
      currentTab: this.barList[this.barIdx]
    });
    setTimeout(() => {
      this.checkAndRefreshLoginStatus();
    }, 300);
  },
  methods: {
    // 初始化基础设置
    initBasicSettings() {
      this.navigationBarColor(0);
      this.statusBarHeight = 20;
      this.titleBarHeight = 44;
      common_vendor.index.$on("loginStateChanged", this.handleLoginStateChanged);
      common_vendor.index.$on("userInfoUpdated", this.handleUserInfoUpdate);
    },
    // 初始化数据
    async initData() {
      common_vendor.index.__f__("log", "at pages/tabbar/center.vue:472", "初始化数据开始");
      const isLoggedIn = this.checkLoginStatus();
      common_vendor.index.__f__("log", "at pages/tabbar/center.vue:477", "登录状态检查结果:", isLoggedIn);
      if (isLoggedIn) {
        await this.loadData();
      } else {
        this.handleNotLoggedIn();
      }
    },
    // 检查并刷新登录状态
    checkAndRefreshLoginStatus() {
      common_vendor.index.__f__("log", "at pages/tabbar/center.vue:490", "检查并刷新登录状态");
      const currentLoginStatus = this.isLogin;
      common_vendor.index.__f__("log", "at pages/tabbar/center.vue:495", "当前登录状态:", currentLoginStatus);
      if (currentLoginStatus) {
        this.loadUserFromCache();
        this.$forceUpdate();
        if (this.list.length === 0 && !this.loading.dynamicList) {
          common_vendor.index.__f__("log", "at pages/tabbar/center.vue:506", "检测到无数据，强制刷新");
          this.forceRefreshCurrentTab();
        } else if (this.shouldRefreshData()) {
          this.refreshData();
        }
      } else {
        common_vendor.index.__f__("log", "at pages/tabbar/center.vue:513", "未登录状态，重置用户信息");
        this.resetUserInfo();
      }
    },
    // 简化的登录状态检查
    checkLoginStatus() {
      try {
        const token = common_vendor.index.getStorageSync("token") || common_vendor.index.getStorageSync("LOGIN_STATUS_TOKEN");
        const userInfo = common_vendor.index.getStorageSync("USER_INFO");
        const isLoggedIn = !!(token && userInfo && userInfo.uid);
        common_vendor.index.__f__("log", "at pages/tabbar/center.vue:526", "登录状态检查:", {
          hasToken: !!token,
          hasUserInfo: !!userInfo,
          hasUid: !!(userInfo && userInfo.uid),
          isLoggedIn
        });
        return isLoggedIn;
      } catch (error) {
        common_vendor.index.__f__("warn", "at pages/tabbar/center.vue:535", "检查登录状态失败:", error);
        return false;
      }
    },
    // 处理未登录状态
    handleNotLoggedIn() {
      this.list = [];
      this.isEmpty = true;
      this.loadStatus = "more";
      this.resetUserInfo();
      common_vendor.index.__f__("log", "at pages/tabbar/center.vue:550", "未登录，设置空状态");
      common_vendor.index.__f__("log", "at pages/tabbar/center.vue:561", "小程序环境，显示未登录状态");
    },
    // 统一的数据加载方法
    async loadData() {
      try {
        await Promise.allSettled([
          this.loadUserInfo(),
          this.loadDynamicList()
        ]);
        this.loadSidebarMenu();
        this.setVisit();
        this.lastRefreshTime = Date.now();
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/tabbar/center.vue:582", "数据加载失败:", error);
        this.showErrorToast("加载失败，请稍后重试");
      }
    },
    // 检查是否需要刷新数据
    shouldRefreshData() {
      const now = Date.now();
      return now - this.lastRefreshTime > this.cacheTimeout;
    },
    // 刷新数据
    async refreshData() {
      if (this.loading.refreshing)
        return;
      this.loading.refreshing = true;
      this.page = 1;
      try {
        await this.loadData();
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/tabbar/center.vue:603", "刷新数据失败:", error);
      } finally {
        this.loading.refreshing = false;
        common_vendor.index.stopPullDownRefresh();
      }
    },
    // 强制刷新当前标签数据
    async forceRefreshCurrentTab() {
      common_vendor.index.__f__("log", "at pages/tabbar/center.vue:612", "强制刷新当前标签:", this.barList[this.barIdx]);
      this.page = 1;
      this.list = [];
      this.isEmpty = true;
      this.loadStatus = "loading";
      await this.loadDynamicList();
    },
    // 简化的用户信息加载
    async loadUserInfo() {
      if (this.loading.userInfo || !this.isLogin) {
        common_vendor.index.__f__("log", "at pages/tabbar/center.vue:627", "loadUserInfo被阻止:", {
          loading: this.loading.userInfo,
          isLogin: this.isLogin
        });
        return;
      }
      this.loading.userInfo = true;
      try {
        const res = await api_social.getUserSocialInfo();
        if (res.status === 200 || res.code === 200) {
          const userData = res.data;
          if (userData.like_count !== void 0) {
            userData.like_count_str = userData.like_count > 999 ? (userData.like_count / 1e3).toFixed(1) + "k" : userData.like_count.toString();
          }
          this.userStore.updateUserInfo(userData);
          this.userInfo = { ...this.userInfo, ...this.userStore.userInfo };
          this.currentMsg = userData.service_num || 0;
          this.userClick();
        } else {
          common_vendor.index.__f__("warn", "at pages/tabbar/center.vue:659", "获取用户信息失败:", res);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/tabbar/center.vue:662", "加载用户信息失败:", error);
        if (!this.handleApiError(error)) {
          this.showErrorToast("加载用户信息失败");
        }
      } finally {
        this.loading.userInfo = false;
      }
    },
    // 简化的动态列表加载
    async loadDynamicList() {
      if (this.loading.dynamicList || !this.isLogin) {
        common_vendor.index.__f__("log", "at pages/tabbar/center.vue:674", "loadDynamicList被阻止:", {
          loading: this.loading.dynamicList,
          isLogin: this.isLogin
        });
        return;
      }
      this.loading.dynamicList = true;
      try {
        const userId = this.userStore.uid;
        common_vendor.index.__f__("log", "at pages/tabbar/center.vue:685", "开始加载动态列表:", {
          barIdx: this.barIdx,
          page: this.page,
          userId,
          tabName: this.barIdx === 0 ? "笔记" : "赞过"
        });
        if (!userId) {
          throw new Error("用户ID无效");
        }
        let apiCall;
        if (this.barIdx === 0) {
          apiCall = api_social.getMyDynamicList({
            page: this.page,
            limit: 10
          });
        } else {
          apiCall = api_social.getLikeDynamicList(userId, {
            page: this.page,
            limit: 10
          });
        }
        const res = await apiCall;
        common_vendor.index.__f__("log", "at pages/tabbar/center.vue:712", "API响应:", res);
        if (res.status === 200 && res.data) {
          const newList = res.data.list || [];
          common_vendor.index.__f__("log", "at pages/tabbar/center.vue:717", "处理数据:", {
            newListLength: newList.length,
            totalCount: res.data.count,
            currentPage: this.page
          });
          if (this.page === 1) {
            this.list = newList;
          } else {
            this.list.push(...newList);
          }
          this.totalCount = res.data.count || 0;
          this.isEmpty = this.list.length === 0;
          this.loadStatus = newList.length < 10 ? "noMore" : "more";
          common_vendor.index.__f__("log", "at pages/tabbar/center.vue:733", "数据更新完成:", {
            listLength: this.list.length,
            isEmpty: this.isEmpty,
            loadStatus: this.loadStatus
          });
        } else {
          common_vendor.index.__f__("warn", "at pages/tabbar/center.vue:739", "API响应异常:", res);
          if (this.page === 1) {
            this.isEmpty = true;
            this.list = [];
          }
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/tabbar/center.vue:746", "加载动态列表失败:", error);
        if (this.page === 1) {
          this.isEmpty = true;
          this.list = [];
        }
        this.loadStatus = "more";
        if (!this.handleApiError(error)) {
          this.showErrorToast("加载动态列表失败");
        }
      } finally {
        this.loading.dynamicList = false;
      }
    },
    // 简化的侧边栏菜单加载
    async loadSidebarMenu() {
      try {
        const res = await api_user.getMenuList();
        common_vendor.index.__f__("log", "at pages/tabbar/center.vue:765", "菜单接口返回数据:", res);
        if (res.status === 200 && res.data) {
          const diyData = res.data.diy_data || {};
          const menuStatus = diyData.my_menus_status;
          common_vendor.index.__f__("log", "at pages/tabbar/center.vue:772", "菜单状态:", menuStatus);
          if (menuStatus === 1) {
            const menuData = res.data.routine_my_menus || [];
            this.sidebarMenu = menuData.map((item) => ({
              id: item.id,
              name: item.name,
              icon: item.pic,
              // 将 pic 字段映射为 icon
              url: item.url,
              badge: item.badge || null
              // 如果有徽章数据的话
            }));
            common_vendor.index.__f__("log", "at pages/tabbar/center.vue:786", "侧边栏菜单加载成功:", this.sidebarMenu);
          } else {
            common_vendor.index.__f__("log", "at pages/tabbar/center.vue:788", "菜单状态未启用，不显示菜单");
            this.sidebarMenu = [];
          }
        }
      } catch (error) {
        common_vendor.index.__f__("warn", "at pages/tabbar/center.vue:793", "加载侧边栏菜单失败:", error);
      }
    },
    // 通用错误处理方法
    handleApiError(error) {
      common_vendor.index.__f__("error", "at pages/tabbar/center.vue:799", "API错误:", error);
      if (error.name === "QuotaExceededError") {
        this.showErrorToast("存储空间不足，正在清理缓存...");
        try {
          this.userStore.clearStorageCache();
        } catch (e) {
          common_vendor.index.__f__("warn", "at pages/tabbar/center.vue:807", "清理缓存失败:", e);
        }
        return true;
      }
      if (error.code === "NETWORK_ERROR") {
        this.showErrorToast("网络连接失败，请检查网络");
        return true;
      }
      if (error.status === 401 || error.statusCode === 401) {
        try {
          this.userStore.logout();
        } catch (e) {
          common_vendor.index.__f__("warn", "at pages/tabbar/center.vue:823", "登出失败:", e);
        }
        this.showErrorToast("登录已过期，请重新登录");
        this.handleNotLoggedIn();
        return true;
      }
      return false;
    },
    // 检查登录状态（简化版）
    checkLoginStatus(redirectToLogin = false) {
      try {
        const token = common_vendor.index.getStorageSync("token") || common_vendor.index.getStorageSync("LOGIN_STATUS_TOKEN");
        const userInfo = common_vendor.index.getStorageSync("USER_INFO");
        const isLoggedIn = !!(token && userInfo && userInfo.uid);
        common_vendor.index.__f__("log", "at pages/tabbar/center.vue:847", "登录状态检查:", { hasToken: !!token, hasUserInfo: !!userInfo, isLoggedIn });
        return isLoggedIn;
      } catch (error) {
        common_vendor.index.__f__("warn", "at pages/tabbar/center.vue:858", "检查登录状态失败:", error);
        return false;
      }
    },
    // 清除定时器
    clearAllTimers() {
      if (this.refreshTimer) {
        clearTimeout(this.refreshTimer);
        this.refreshTimer = null;
      }
    },
    showErrorToast(message, duration = 2e3) {
      common_vendor.index.showToast({
        title: message,
        icon: "none",
        duration
      });
    },
    loadUserFromCache() {
      try {
        const cachedUserInfo = common_vendor.index.getStorageSync("USER_INFO");
        common_vendor.index.__f__("log", "at pages/tabbar/center.vue:888", "本地缓存用户信息:", cachedUserInfo);
        if (cachedUserInfo) {
          let parsedInfo = cachedUserInfo;
          if (typeof cachedUserInfo === "string") {
            try {
              parsedInfo = JSON.parse(cachedUserInfo);
            } catch (e) {
              common_vendor.index.__f__("error", "at pages/tabbar/center.vue:898", "解析缓存用户信息失败:", e);
              return;
            }
          }
          if (parsedInfo && typeof parsedInfo === "object" && parsedInfo.uid) {
            this.userInfo = { ...this.userInfo, ...parsedInfo };
            common_vendor.index.__f__("log", "at pages/tabbar/center.vue:907", "用户信息加载成功:", parsedInfo);
            if (this.list.length === 0 && !this.loading.dynamicList) {
              common_vendor.index.__f__("log", "at pages/tabbar/center.vue:911", "用户信息加载成功，开始加载动态数据");
              this.loadData();
            }
          } else {
            common_vendor.index.__f__("warn", "at pages/tabbar/center.vue:915", "缓存的用户信息数据不完整:", parsedInfo);
            this.resetUserInfo();
          }
        } else {
          common_vendor.index.__f__("log", "at pages/tabbar/center.vue:919", "没有本地缓存用户信息");
          this.resetUserInfo();
        }
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/tabbar/center.vue:923", "读取缓存用户信息失败:", e);
        this.resetUserInfo();
      }
    },
    handleLoginStateChanged(isLoggedIn) {
      if (isLoggedIn) {
        this.loadData();
      } else {
        this.resetUserInfo();
      }
    },
    resetUserInfo() {
      this.userInfo = {
        avatar: "/static/img/avatar.png",
        nickname: "您还未登录哦~",
        about_me: "登录后查看更多内容",
        follow_count: 0,
        fans_count: 0,
        like_count: 0,
        like_count_str: "0",
        visitor_count: 0,
        visitor_badge: 0,
        is_verified: 0,
        is_money_level: 0,
        svip_open: 0,
        sex: 0,
        user_id_number: "",
        interest_tags: [],
        vip_status: 0,
        overdue_time: "",
        card_count: 0,
        activity_count: 0,
        activity_img: ""
      };
      this.list = [];
      this.isEmpty = true;
      this.loadStatus = "more";
      this.page = 1;
      Object.keys(this.loading).forEach((key) => {
        this.loading[key] = false;
      });
      this.lastRefreshTime = 0;
      this.sidebarMenu = [];
    },
    handleApiError(err) {
      if (err && (err.statusCode === 401 || err.code === 401 || err.status === 401)) {
        common_vendor.index.showToast({
          title: "登录信息已过期，请重新登录",
          icon: "none",
          duration: 2e3
        });
        this.resetUserInfo();
        setTimeout(() => libs_login.toLogin(), 1500);
        return true;
      }
      return false;
    },
    updateStoreState() {
      try {
        this.appStore.setCurrentMsg(true);
      } catch (error) {
        common_vendor.index.__f__("warn", "at pages/tabbar/center.vue:1008", "更新 store 状态失败:", error.message);
      }
    },
    barClick(e) {
      if (this.loading.dynamicList)
        return;
      const newBarIdx = parseInt(e.currentTarget.dataset.idx);
      if (newBarIdx === this.barIdx)
        return;
      common_vendor.index.__f__("log", "at pages/tabbar/center.vue:1018", "切换标签:", {
        from: this.barIdx,
        to: newBarIdx,
        fromName: this.barList[this.barIdx],
        toName: this.barList[newBarIdx]
      });
      this.barIdx = newBarIdx;
      this.page = 1;
      this.list = [];
      this.isEmpty = true;
      this.loadStatus = "loading";
      this.loadDynamicList();
    },
    delClick(e) {
      common_vendor.index.showModal({
        content: "确定删除该笔记吗？",
        confirmColor: "#FA5150",
        success: (res) => {
          if (res.confirm) {
            api_social.deleteDynamic(this.list[e.idx].id).then((res2) => {
              if (res2.status == 200) {
                this.list.splice(e.idx, 1);
                if (this.list.length <= 0) {
                  this.isEmpty = true;
                }
                common_vendor.index.showToast({ title: "删除成功", icon: "success" });
              } else {
                common_vendor.index.showToast({ title: res2.msg || "删除失败", icon: "none" });
              }
            }).catch(() => {
              common_vendor.index.showToast({ title: "删除失败，请重试", icon: "none" });
            });
          }
        }
      });
    },
    likePopupClick(open) {
      if (open) {
        this.$refs.likePopup.open();
      } else {
        this.$refs.likePopup.close();
      }
    },
    navigateToFun(e) {
      if (!this.checkLoginStatus(true)) {
        return;
      }
      if (this.showSidebar) {
        this.showSidebar = false;
      }
      const url = e.currentTarget.dataset.url;
      if (url === "center/visitor") {
        this.getVisitorList();
      }
      common_vendor.index.navigateTo({ url: "/pages/" + url });
    },
    toFollowList(type) {
      if (!this.checkLoginStatus(true)) {
        return;
      }
      common_vendor.index.navigateTo({
        url: `/pages/center/follow?type=${type}&id=${this.userInfo.uid}&name=${this.userInfo.nickname}`
      });
    },
    userClick() {
      this.blockList[0].img = this.userInfo.circle_img || "";
      this.blockList[0].count = this.userInfo.circle_count || 0;
      this.blockList[1].img = this.userInfo.cart_img || "";
      this.blockList[1].count = this.userInfo.cart_count || 0;
      this.blockList[2].img = this.userInfo.order_img || "";
      this.blockList[2].count = this.userInfo.order_count || 0;
    },
    navigationBarColor(status) {
      common_vendor.index.setNavigationBarColor({
        frontColor: status ? "#000000" : "#ffffff",
        backgroundColor: "transparent",
        animation: { duration: 300, timingFunc: "easeIn" }
      });
    },
    toggleSidebar() {
      this.showSidebar = !this.showSidebar;
    },
    getVisitorList() {
      const updatedInfo = {
        ...this.userStore.userInfo,
        visitor_badge: 0
      };
      this.userStore.updateUserInfo(updatedInfo);
      this.userInfo = { ...this.userInfo, ...updatedInfo };
      api_social.getVisitorDetails({ page: 1, limit: 20, type: 0 }).then((res) => {
        const emptyData = { visitors: [], total: 0, has_more: false };
        if (res.status === 200 || res.code === 200) {
          const resData = res.data || {};
          const visitorInfo = {
            ...this.userStore.userInfo,
            visitor_count: resData.total || 0,
            visitor_badge: 0
          };
          this.userStore.updateUserInfo(visitorInfo);
          this.userInfo = { ...this.userInfo, ...visitorInfo };
          const hasMore = resData.page * resData.limit < resData.total;
          common_vendor.index.$emit("updateVisitorList", {
            visitors: resData.list || [],
            total: resData.total || 0,
            has_more: hasMore,
            page: resData.page || 1,
            limit: resData.limit || 20
          });
        } else {
          this.userInfo.visitor_count = 0;
          this.userInfo.visitor_badge = 0;
          this.$store.commit("UPDATE_USERINFO", this.userInfo);
          common_vendor.index.$emit("updateVisitorList", emptyData);
        }
      }).catch((error) => {
        common_vendor.index.__f__("error", "at pages/tabbar/center.vue:1165", "获取访客列表失败:", error);
        const errorInfo = {
          ...this.userStore.userInfo,
          visitor_count: 0,
          visitor_badge: 0
        };
        this.userStore.updateUserInfo(errorInfo);
        this.userInfo = { ...this.userInfo, ...errorInfo };
        common_vendor.index.$emit("updateVisitorList", { visitors: [], total: 0, has_more: false });
      });
    },
    setVisit() {
      api_user.setVisit({ url: "/pages/tabbar/center" }).catch(() => {
      });
    },
    formatDate(timestamp) {
      if (!timestamp)
        return "";
      const date = new Date(timestamp * 1e3);
      return `${date.getFullYear()}-${("0" + (date.getMonth() + 1)).slice(-2)}-${("0" + date.getDate()).slice(-2)}`;
    },
    goToVipPage() {
      if (!this.checkLoginStatus(true)) {
        return;
      }
      common_vendor.index.navigateTo({ url: "/pages/annex/vip_paid/index" });
    },
    handleUserInfoUpdate() {
      if (this.userStore.userInfo && this.userStore.userInfo.uid) {
        this.userInfo = { ...this.userInfo, ...this.userStore.userInfo };
        this.userClick();
      } else {
        this.loadUserFromCache();
      }
    },
    handleBottomNav(type) {
      if (!this.checkLoginStatus(true)) {
        return;
      }
      this.showSidebar = false;
      switch (type) {
        case "scan":
          common_vendor.index.scanCode({
            success: (res) => {
              if (res.result) {
                common_vendor.index.showToast({ title: "扫码成功", icon: "success" });
              }
            },
            fail: () => {
              common_vendor.index.showToast({ title: "扫码失败", icon: "none" });
            }
          });
          break;
        case "help":
          common_vendor.index.navigateTo({ url: "/pages/setting/service" });
          break;
        case "setting":
          common_vendor.index.navigateTo({ url: "/pages/setting/index" });
          break;
      }
    },
    // 模板辅助方法
    getEmptyTitle() {
      const titles = ["暂无笔记内容", "暂无喜欢的内容"];
      return titles[this.barIdx] || "暂无内容";
    },
    getEmptySubtitle() {
      if (this.barIdx === 1) {
        return "快在推荐中寻找更多笔记吧";
      }
      return "发笔记，记录灵感日常";
    },
    handleLogin() {
      libs_login.toLogin();
    }
  },
  onReachBottom() {
    if (!this.isLogin) {
      common_vendor.index.__f__("log", "at pages/tabbar/center.vue:1264", "未登录，停止加载更多");
      return;
    }
    if (this.loading.dynamicList || this.loadStatus === "noMore") {
      return;
    }
    if (this.list.length < this.totalCount) {
      this.page++;
      this.loadDynamicList();
    }
  },
  onPageScroll(e) {
    if (this.showSidebar) {
      return;
    }
    this.scrollTop = e.scrollTop;
    const threshold = this.statusBarHeight + this.titleBarHeight + 80;
    if (this.scrollTop <= threshold) {
      this.navbarTrans = Math.min(this.scrollTop / threshold, 1);
    } else {
      this.navbarTrans = 1;
    }
    if (this.scrollTop > threshold) {
      this.navigationBarColor(1);
    } else {
      this.navigationBarColor(0);
    }
  }
};
if (!Array) {
  const _component_lazy_image = common_vendor.resolveComponent("lazy-image");
  const _easycom_uni_load_more2 = common_vendor.resolveComponent("uni-load-more");
  const _component_card_gg = common_vendor.resolveComponent("card-gg");
  const _component_card_wd = common_vendor.resolveComponent("card-wd");
  const _easycom_uni_popup2 = common_vendor.resolveComponent("uni-popup");
  const _component_tabbar = common_vendor.resolveComponent("tabbar");
  (_component_lazy_image + _easycom_uni_load_more2 + _component_card_gg + _component_card_wd + _easycom_uni_popup2 + _component_tabbar)();
}
const _easycom_uni_load_more = () => "../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js";
const _easycom_uni_popup = () => "../../uni_modules/uni-popup/components/uni-popup/uni-popup.js";
if (!Math) {
  (_easycom_uni_load_more + _easycom_uni_popup)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_0$6,
    b: common_vendor.o((...args) => $options.toggleSidebar && $options.toggleSidebar(...args)),
    c: $data.userInfo.avatar,
    d: common_vendor.t($data.userInfo.nickname),
    e: "translateY(" + (1 - $data.navbarTrans) * 30 + "px)",
    f: $data.navbarTrans,
    g: $data.titleBarHeight + "px",
    h: $data.statusBarHeight + "px",
    i: "rgba(255,255,255," + $data.navbarTrans + ")",
    j: common_vendor.p({
      src: $data.userInfo.avatar || "/static/img/avatar.png",
      mode: "aspectFill"
    }),
    k: common_vendor.p({
      src: $data.userInfo.avatar
    }),
    l: common_vendor.t($data.userInfo.nickname),
    m: $data.userInfo.is_money_level > 0 && $data.userInfo.svip_open
  }, $data.userInfo.is_money_level > 0 && $data.userInfo.svip_open ? {
    n: common_assets._imports_1$3
  } : {}, {
    o: $data.userInfo.is_verified
  }, $data.userInfo.is_verified ? {
    p: common_assets._imports_0$5
  } : {}, {
    q: $data.userInfo.sex != 2
  }, $data.userInfo.sex != 2 ? {
    r: $data.userInfo.sex == 1 ? "/static/img/nan.png" : "/static/img/nv.png"
  } : {}, {
    s: common_vendor.t($data.userInfo.user_id_number),
    t: common_assets._imports_1$4,
    v: common_vendor.o((...args) => $options.navigateToFun && $options.navigateToFun(...args)),
    w: common_vendor.t($data.userInfo.follow_count),
    x: common_vendor.o(($event) => $options.toFollowList(0)),
    y: common_vendor.t($data.userInfo.fans_count),
    z: common_vendor.o(($event) => $options.toFollowList(1)),
    A: common_vendor.t($data.userInfo.like_count_str),
    B: common_vendor.o(($event) => $options.likePopupClick(true)),
    C: common_vendor.t($data.userInfo.visitor_count),
    D: $data.userInfo.visitor_badge
  }, $data.userInfo.visitor_badge ? {
    E: common_vendor.t($data.userInfo.visitor_badge > 99 ? "99+" : $data.userInfo.visitor_badge)
  } : {}, {
    F: common_vendor.o((...args) => $options.navigateToFun && $options.navigateToFun(...args)),
    G: $data.userInfo.interest_tags && $data.userInfo.interest_tags.length
  }, $data.userInfo.interest_tags && $data.userInfo.interest_tags.length ? {
    H: common_vendor.f($data.userInfo.interest_tags, (tag, index, i0) => {
      return {
        a: common_vendor.t(tag),
        b: index
      };
    })
  } : {}, {
    I: common_assets._imports_7,
    J: common_vendor.o((...args) => $options.navigateToFun && $options.navigateToFun(...args)),
    K: common_vendor.t($data.userInfo.about_me ? $data.userInfo.about_me : "添加个人简介，让大家认识你..."),
    L: common_assets._imports_5,
    M: common_vendor.o((...args) => $options.navigateToFun && $options.navigateToFun(...args)),
    N: $data.statusBarHeight + $data.titleBarHeight + "px",
    O: $data.userInfo.activity_count
  }, $data.userInfo.activity_count ? common_vendor.e({
    P: common_vendor.t($data.userInfo.activity_count ? "共" + $data.userInfo.activity_count + "个活动" : "没有参加活动"),
    Q: !$data.userInfo.activity_img
  }, !$data.userInfo.activity_img ? {
    R: common_assets._imports_3$5
  } : {
    S: $data.userInfo.activity_img
  }, {
    T: $data.userInfo.activity_img ? "#CECECE" : "#000",
    U: common_assets._imports_1$4,
    V: common_vendor.o((...args) => $options.navigateToFun && $options.navigateToFun(...args))
  }) : {}, {
    W: common_vendor.f($data.blockList, (item, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(item.name),
        b: item.count
      }, item.count ? {
        c: common_vendor.t(item.count),
        d: common_vendor.t(index == 0 ? "个圈子" : index == 1 ? "件商品" : "笔订单")
      } : {
        e: common_vendor.t(index == 0 ? "没有加入圈子" : index == 1 ? "购物车空空的" : "订单空空的")
      }, {
        f: !item.img
      }, !item.img ? {
        g: item.icon
      } : {
        h: item.img
      }, {
        i: item.img ? "#CECECE" : "#000",
        j: index,
        k: item.url,
        l: common_vendor.o((...args) => $options.navigateToFun && $options.navigateToFun(...args), index)
      });
    }),
    X: common_assets._imports_1$4,
    Y: $data.appCard || $data.userInfo.card_count
  }, $data.appCard || $data.userInfo.card_count ? {
    Z: common_vendor.t($data.userInfo.card_count ? "共" + $data.userInfo.card_count + "张卡券" : "暂无可用卡券"),
    aa: common_assets._imports_3$4,
    ab: common_assets._imports_1$4,
    ac: common_vendor.o((...args) => $options.navigateToFun && $options.navigateToFun(...args))
  } : {}, {
    ad: common_vendor.f($data.barList, (item, index, i0) => {
      return {
        a: common_vendor.t(item),
        b: index == $data.barIdx ? "#000" : "#999",
        c: index == $data.barIdx ? "28rpx" : "26rpx",
        d: index == $data.barIdx ? 1 : 0,
        e: index,
        f: common_vendor.o((...args) => $options.barClick && $options.barClick(...args), index),
        g: index
      };
    }),
    ae: $data.statusBarHeight + $data.titleBarHeight - 1 + "px",
    af: $data.loading.refreshing && $data.list.length === 0
  }, $data.loading.refreshing && $data.list.length === 0 ? {
    ag: common_vendor.p({
      status: "loading"
    })
  } : {}, {
    ah: $data.loading.refreshing && $data.list.length === 0 ? "60rpx" : "0px",
    ai: !$options.isLogin
  }, !$options.isLogin ? {
    aj: common_assets._imports_8,
    ak: common_vendor.o((...args) => $options.handleLogin && $options.handleLogin(...args))
  } : $data.isEmpty && !$data.loading.dynamicList ? {
    am: common_assets._imports_3$1,
    an: common_vendor.t($options.getEmptyTitle()),
    ao: common_vendor.t($options.getEmptySubtitle())
  } : $data.loading.dynamicList && $data.list.length === 0 ? {
    aq: common_vendor.p({
      status: "loading"
    })
  } : {
    ar: common_vendor.f($data.list, (item, index, i0) => {
      return common_vendor.e($data.barIdx == 1 ? {
        a: "25c53cf4-4-" + i0,
        b: common_vendor.p({
          item,
          idx: index
        })
      } : {
        c: common_vendor.o($options.delClick, item.id || index),
        d: "25c53cf4-5-" + i0,
        e: common_vendor.p({
          item,
          idx: index,
          bar: $data.barIdx
        })
      }, {
        f: item.id || index
      });
    }),
    as: $data.barIdx == 1
  }, {
    al: $data.isEmpty && !$data.loading.dynamicList,
    ap: $data.loading.dynamicList && $data.list.length === 0,
    at: $data.list.length > 0
  }, $data.list.length > 0 ? {
    av: common_vendor.p({
      status: $data.loadStatus
    })
  } : {}, {
    aw: common_assets._imports_10,
    ax: common_vendor.t($data.userInfo.nickname),
    ay: common_vendor.t($data.userInfo.like_count || 0),
    az: common_vendor.o(($event) => $options.likePopupClick(false)),
    aA: common_vendor.sr("likePopup", "25c53cf4-7"),
    aB: common_vendor.p({
      currentPage: 4,
      currentMsg: $data.currentMsg,
      userAvatar: $data.userInfo.avatar
    }),
    aC: $data.userInfo.avatar || "/static/img/avatar.png",
    aD: common_vendor.t($data.userInfo.nickname),
    aE: $data.userInfo.is_money_level > 0 && $data.userInfo.svip_open
  }, $data.userInfo.is_money_level > 0 && $data.userInfo.svip_open ? {
    aF: common_assets._imports_1$3
  } : {}, {
    aG: $data.userInfo.is_verified
  }, $data.userInfo.is_verified ? {
    aH: common_assets._imports_0$5
  } : {}, {
    aI: common_assets._imports_0$4,
    aJ: common_vendor.o((...args) => $options.toggleSidebar && $options.toggleSidebar(...args)),
    aK: $data.statusBarHeight + 10 + "px",
    aL: $data.userInfo.vip_status == 1
  }, $data.userInfo.vip_status == 1 ? {} : $data.userInfo.vip_status == 3 ? {
    aN: common_vendor.t($options.formatDate($data.userInfo.overdue_time))
  } : $data.userInfo.vip_status == -1 ? {} : $data.userInfo.vip_status == 2 ? {} : {}, {
    aM: $data.userInfo.vip_status == 3,
    aO: $data.userInfo.vip_status == -1,
    aP: $data.userInfo.vip_status == 2,
    aQ: $data.userInfo.vip_status != 1
  }, $data.userInfo.vip_status != 1 ? {
    aR: common_vendor.o((...args) => $options.goToVipPage && $options.goToVipPage(...args))
  } : {}, {
    aS: common_vendor.o((...args) => $options.goToVipPage && $options.goToVipPage(...args)),
    aT: $data.sidebarMenu && $data.sidebarMenu.length > 0
  }, $data.sidebarMenu && $data.sidebarMenu.length > 0 ? {
    aU: common_vendor.f($data.sidebarMenu, (item, index, i0) => {
      return common_vendor.e({
        a: item.icon,
        b: item.badge
      }, item.badge ? {
        c: common_vendor.t(item.badge)
      } : {}, {
        d: common_vendor.t(item.name),
        e: "menu-" + index,
        f: item.url,
        g: common_vendor.o((...args) => $options.navigateToFun && $options.navigateToFun(...args), "menu-" + index)
      });
    })
  } : {}, {
    aV: common_assets._imports_12,
    aW: common_vendor.o(($event) => $options.handleBottomNav("scan")),
    aX: common_assets._imports_2$3,
    aY: common_vendor.o(($event) => $options.handleBottomNav("help")),
    aZ: common_assets._imports_7,
    ba: common_vendor.o(($event) => $options.handleBottomNav("setting")),
    bb: common_vendor.t((/* @__PURE__ */ new Date()).getFullYear()),
    bc: $data.showSidebar ? 1 : "",
    bd: $data.showSidebar
  }, $data.showSidebar ? {
    be: common_vendor.o((...args) => $options.toggleSidebar && $options.toggleSidebar(...args)),
    bf: common_vendor.o(() => {
    }),
    bg: common_vendor.o(() => {
    }),
    bh: common_vendor.o(() => {
    })
  } : {}, {
    bi: $data.showSidebar ? 1 : ""
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
_sfc_main.__runtimeHooks = 1;
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tabbar/center.js.map
