{"version": 3, "file": "details.js", "sources": ["pages/user/details.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvdXNlci9kZXRhaWxzLnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"container\">\n    <!-- 导航栏 -->\n    <view class=\"nav-box df\" :style=\"{'padding-top': statusBarHeight + 'px', 'background': 'rgba(255, 255, 255,' + navbarTrans + ')'}\">\n      <view class=\"nav-back df\" :style=\"{'height': titleBarHeight + 'px'}\" @tap=\"navBack\">\n        <image :class=\"[navbarTrans != 1 ? 'xwb' : '']\" :src=\"navbarTrans == 1 ? '/static/img/z.png' : '/static/img/z1.png'\" style=\"width:34rpx;height:34rpx\"></image>\n      </view>\n      <view v-if=\"navbarTrans == 1\" class=\"nav-title ohto\">{{userInfo.name}}</view>\n    </view>\n    \n    <!-- 用户信息 -->\n    <view class=\"user-box\" :style=\"{'padding-top': statusBarHeight + titleBarHeight + 'px'}\">\n      <view class=\"user-bg\"></view>\n      <view class=\"user-img\" style=\"z-index:-2\">\n        <!-- 背景轮播区域 -->\n        <view v-if=\"backgroundImages.length > 0\" class=\"background-carousel\">\n          <view \n            v-for=\"(img, index) in backgroundImages\" \n            :key=\"index\"\n            class=\"carousel-item\"\n            :class=\"{'active': index === currentBgIndex}\"\n          >\n            <lazy-image :src=\"img.url\" mode=\"aspectFill\"></lazy-image>\n          </view>\n          <!-- 轮播指示器 -->\n          <view v-if=\"backgroundImages.length > 1\" class=\"carousel-indicators\">\n            <view \n              v-for=\"(item, index) in backgroundImages\" \n              :key=\"index\"\n              class=\"indicator\"\n              :class=\"{'active': index === currentBgIndex}\"\n              @tap=\"switchBackground(index)\"\n            ></view>\n          </view>\n        </view>\n        <!-- 默认头像背景 -->\n        <view v-else class=\"default-background\">\n          <lazy-image :src=\"userInfo.avatar || '/static/img/avatar.png'\" mode=\"aspectFill\"></lazy-image>\n        </view>\n      </view>\n      <view class=\"user-top df\">\n        <view class=\"avatar\">\n          <lazy-image :src=\"userInfo.avatar\"></lazy-image>\n        </view>\n        <view :class=\"['btn', followBtnClass]\" @tap=\"followClick\">\n          {{followBtnText}}\n        </view>\n      </view>\n      <view class=\"user-name\">{{userInfo.name}}</view>\n      <view v-if=\"userInfo.intro\" class=\"user-intro\">\n        <text user-select=\"true\">{{userInfo.intro}}</text>\n      </view>\n      <view class=\"user-tag df\">\n        <view v-if=\"userInfo.gender != undefined || userInfo.sex != undefined\" class=\"tag-item df\">\n          <image :src=\"(userInfo.gender == 1 || userInfo.sex == 1) ? '/static/img/nan.png' : '/static/img/nv.png'\"></image>\n        </view>\n        <view v-if=\"userInfo.constellation_label\" class=\"tag-item df\">\n          {{userInfo.constellation_label}}\n        </view>\n        <view v-else-if=\"userInfo.age && userInfo.age != '暂不展示'\" class=\"tag-item df\">\n          {{userInfo.age}}\n        </view>\n        <view class=\"tag-item df\">IP属地：{{userInfo.province || '未知'}}</view>\n      </view>\n      <view class=\"user-num df\">\n        <view class=\"num-item df\" data-type=\"0\" @tap=\"toFollow\">\n          <text class=\"t1\">{{userInfo.follow_count}}</text>\n          <text>关注</text>\n        </view>\n        <view class=\"num-item df\" data-type=\"1\" @tap=\"toFollow\">\n          <text class=\"t1\">{{userInfo.fans_count}}</text>\n          <text>粉丝</text>\n        </view>\n        <view class=\"num-item df\" @tap=\"likePopupClick(true)\">\n          <text class=\"t1\">{{userInfo.like_count_str}}</text>\n          <text>获赞</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 内容区域 -->\n    <view class=\"content-box\">\n      <!-- 圈子信息 -->\n      <view v-if=\"userInfo.circle.length\" class=\"block-box\">\n        <view class=\"block-title\">{{userInfo.name}} 加入的圈子</view>\n        <scroll-view scroll-x=\"true\" style=\"width:100%;white-space:nowrap\">\n          <view class=\"circle-box\">\n            <view v-for=\"(item, index) in userInfo.circle\" \n                  :key=\"index\" \n                  class=\"circle-item df\" \n                  :data-url=\"'note/circle?id=' + item.id\" \n                  @tap=\"navigateToFun\">\n              <image class=\"circle-avatar\" :src=\"item.avatar\" mode=\"aspectFill\"></image>\n              <view class=\"circle-name ohto\">{{item.name}}</view>\n            </view>\n            <view style=\"flex-shrink:0;width:15rpx;height:15rpx\"></view>\n          </view>\n        </scroll-view>\n      </view>\n      \n      <!-- 导航栏选项 -->\n      <view class=\"bar-box df\" :style=\"{'top': statusBarHeight + titleBarHeight - 1 + 'px'}\">\n        <view v-for=\"(item, index) in barList\" \n              :key=\"index\" \n              class=\"bar-item df\" \n              @tap=\"barClick\" \n              :data-idx=\"index\">\n          <text :style=\"{\n            'color': index == barIdx ? '#000' : '#999',\n            'font-size': index == barIdx ? '28rpx' : '26rpx'\n          }\">{{item}}</text>\n          <view :style=\"{'opacity': index == barIdx ? 1 : 0}\" class=\"bar-line\"></view>\n        </view>\n      </view>\n      \n      <!-- 加载中提示 -->\n      <view v-if=\"loadStatus == 'loading' && !isThrottling\" class=\"loading-box df\">\n        <uni-load-more :status=\"loadStatus\"></uni-load-more>\n      </view>\n      \n      <!-- 错误状态提示 -->\n      <view v-if=\"hasError\" class=\"error-box df\">\n        <image src=\"/static/img/error.png\"/>\n        <view class=\"e1\">加载失败</view>\n        <view class=\"e2\">{{errorMessage || '网络连接异常，请稍后重试'}}</view>\n        <view class=\"retry-btn\" @tap=\"retryLoad\">重新加载</view>\n      </view>\n      \n      <!-- 空内容提示 -->\n      <emptyPage\n        v-else-if=\"isEmpty && !hasError\"\n        :title=\"barIdx == 0 ? '暂无笔记内容' : '暂无喜欢的内容'\"\n        :description=\"userInfo.name + (barIdx == 0 ? ' 还没有发布过' : ' 还没有点赞')\"\n        image=\"/static/img/empty.png\"\n      />\n\n      <!-- 隐私设置提示 -->\n      <emptyPage\n        v-else-if=\"barIdx == 1 && userInfo.privacy.like == 0\"\n        title=\"点赞内容不可见\"\n        description=\"该用户已将点赞设为私密\"\n        image=\"/static/img/private.png\"\n      />\n      \n      <!-- 内容列表 -->\n      <view v-else-if=\"list.length > 0\" :class=\"[isWaterfall ? 'dynamic-box' : '']\">\n        <waterfall v-if=\"isWaterfall\" :note=\"list\" :page=\"page\"></waterfall>\n        <block v-else>\n          <block v-for=\"(item, index) in list\" :key=\"index\">\n            <card-gg :item=\"item\" :idx=\"index\" @likeback=\"likeClick\" @followback=\"followBack\"></card-gg>\n          </block>\n        </block>\n        <!-- 底部加载更多状态 -->\n        <view v-if=\"loadStatus !== 'loading'\" class=\"load-more-box\">\n        <uni-load-more :status=\"loadStatus\"></uni-load-more>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 获赞弹窗 -->\n    <uni-popup ref=\"likePopup\">\n      <view class=\"like-popup\">\n        <image class=\"like-img\" src=\"/static/img/like-big.png\" mode=\"aspectFill\"></image>\n        <view class=\"like-content\">\n          <text>\"</text>{{userInfo.name}}<text>\"</text>共获得 {{userInfo.like_count}} 个赞\n        </view>\n        <view class=\"like-btn\" @tap=\"likePopupClick(false)\">确认</view>\n      </view>\n    </uni-popup>\n    \n    <!-- 提示弹窗 -->\n    <uni-popup ref=\"tipsPopup\" type=\"top\" :mask-background-color=\"'rgba(0, 0, 0, 0)'\">\n      <view class=\"tips-box df\">\n        <view class=\"tips-item\">{{tipsTitle}}</view>\n      </view>\n    </uni-popup>\n  </view>\n</template>\n\n<script setup>\nimport { ref, reactive, computed, onMounted, onUnmounted, getCurrentInstance } from 'vue'\nimport { onLoad, onUnload, onPullDownRefresh, onReachBottom, onPageScroll, onShareAppMessage, onShareTimeline } from '@dcloudio/uni-app'\nimport lazyImage from '@/components/lazyImage/lazyImage'\nimport uniLoadMore from '@/uni_modules/uni-load-more/components/uni-load-more/uni-load-more.vue'\nimport waterfall from '@/components/waterfall/waterfall'\nimport cardGg from '@/components/card-gg/card-gg'\nimport emptyPage from '@/components/emptyPage.vue'\nimport { getUserHomepage, getOtherUserDynamicList, followUser, getLikeDynamicList } from '@/api/social.js'\nimport { useUserStore } from '@/stores/user.js'\n\n// 获取当前实例\nconst instance = getCurrentInstance()\nconst userStore = useUserStore()\n\nconst statusBarHeight = ref(20)\nconst titleBarHeight = ref(44)\nconst navbarTrans = ref(0)\nconst userInfo = reactive({\n  id: 0,\n  uid: 0,\n  avatar: \"\",\n  name: \"昵称加载中\",\n  nickname: \"\",\n  intro: \"\",\n  about_me: \"\",\n  gender: 0,\n  sex: 0,\n  age: \"\",\n  constellation: 0,\n  constellation_label: \"\",\n  province: \"\",\n  follow_count: 0,\n  fans_count: 0,\n  like_count: 0,\n  like_count_str: 0,\n  is_follow: 0,\n  is_mutual_follow: 0,\n  circle: [],\n  user_id_number: \"\",\n  privacy: {\n    like: 1,\n    follow: 1\n  },\n  home_background: \"[]\",\n  visitors: [], // 访客记录\n  // VIP相关信息\n  vip: false,\n  vip_id: 0,\n  vip_icon: '',\n  vip_name: '',\n  vip_status: 2,\n  svip_open: false,\n  is_ever_level: 0,\n  is_money_level: 0,\n  overdue_time: 0\n})\nconst barList = ref([\"笔记\", \"赞过\"])\nconst barIdx = ref(0)\nconst isThrottling = ref(false)\nconst list = ref([])\nconst page = ref(1)\nconst isEmpty = ref(false)\nconst loadStatus = ref(\"loading\")\nconst tipsTitle = ref(\"\")\nconst isWaterfall = ref(false)\nconst backgroundImages = ref([])\nconst currentBgIndex = ref(0)\nconst carouselTimer = ref(null)\nconst userId = ref(0)\nconst isFollowing = ref(false)\nconst limit = ref(10)\nconst isLoading = ref(false)\nconst isProcessing = ref(false)\n// 页面状态管理\nconst pageLoaded = ref(false)\nconst userLoaded = ref(false)\n// 错误状态\nconst hasError = ref(false)\nconst errorMessage = ref(\"\")\n\n// refs\nconst likePopup = ref(null)\nconst tipsPopup = ref(null)\n// 计算属性\nconst isLogin = computed(() => {\n  return userStore.isLoggedIn\n})\n\nconst loginUserId = computed(() => {\n  return userStore.uid || 0\n})\n\n// 关注按钮样式\nconst followBtnClass = computed(() => {\n  // 检查是否为互相关注\n  if (parseInt(userInfo.is_mutual_follow) === 1) {\n    return 'mutual'\n  }\n  // 检查是否已关注\n  else if (isFollowing.value || parseInt(userInfo.is_follow) === 1) {\n    return 'active'\n  }\n  // 未关注状态\n  else {\n    return ''\n  }\n})\n\n// 关注按钮文本\nconst followBtnText = computed(() => {\n  // 检查是否为互相关注\n  if (parseInt(userInfo.is_mutual_follow) === 1) {\n    return '互相关注'\n  }\n  // 检查是否已关注\n  else if (isFollowing.value || parseInt(userInfo.is_follow) === 1) {\n    return '已关注'\n  }\n  // 未关注状态\n  else {\n    return '＋关注'\n  }\n})\n// 页面加载\nonLoad(async (options) => {\n  // 显示分享菜单 - 添加平台兼容性检查\n  try {\n    // #ifdef MP-WEIXIN || MP-ALIPAY || MP-BAIDU || MP-TOUTIAO || MP-QQ\n    if (typeof uni.showShareMenu === 'function') {\n      uni.showShareMenu()\n    }\n    // #endif\n  } catch (e) {\n    console.warn('showShareMenu not supported on this platform:', e)\n  }\n\n  // 等待应用初始化完成\n  await instance.appContext.app.config.globalProperties.$onLaunched\n\n  console.log('页面onLoad参数:', options);\n\n  try {\n    // 同时支持id和user_id参数\n    const userIdParam = options?.id || options?.user_id || options?.uid || 0;\n\n    if (userIdParam) {\n      // 确保ID为数字类型\n      userId.value = parseInt(userIdParam);\n\n      if (isNaN(userId.value) || userId.value <= 0) {\n        console.error('用户ID无效:', userIdParam);\n        opTipsPopup(\"用户ID无效\", true);\n        return;\n      }\n\n      // 检查是否是查看自己的页面\n      const currentLoginUserId = loginUserId.value || uni.getStorageSync('uid') || 0;\n      const isSelfProfile = userId.value === parseInt(currentLoginUserId);\n\n      console.log('加载用户ID:', userId.value, '是否自己:', isSelfProfile);\n\n      // 如果是查看自己的页面，可以添加一些特殊处理\n      if (isSelfProfile) {\n        barList.value = [\"笔记\", \"赞过\"]; // 确保可以看到自己的点赞\n      } else {\n        barList.value = [\"笔记\", \"赞过\"]; // 其他用户的点赞可能受隐私设置影响\n      }\n\n      // 调用获取用户详情方法\n      getUserInfo();\n      loadDynamicList();\n    } else {\n      console.error('缺少用户ID参数');\n      opTipsPopup(\"用户状态异常或已注销！\", true);\n    }\n  } catch (e) {\n    console.error('onLoad异常:', e);\n    opTipsPopup(\"加载用户信息失败\", true);\n  }\n})\n\n// 页面卸载\nonUnload(() => {\n  // 页面卸载时清理轮播定时器\n  clearCarouselTimer();\n})\n/**\n * 获取用户信息\n */\nconst getUserInfo = () => {\n  console.log('开始获取用户信息, userId:', userId.value);\n\n  // 确保userId有效\n  if (!userId.value || userId.value <= 0) {\n    console.error('获取用户信息失败: 用户ID无效', userId.value);\n    opTipsPopup(\"用户ID无效\", true);\n    return;\n  }\n\n  // 显示加载状态\n  uni.showLoading({\n    title: '加载中...',\n    mask: true\n  });\n\n  // 简化调用，后端会自动获取当前登录用户ID\n  getUserHomepage({\n    user_id: userId.value\n  }).then(res => {\n    console.log('获取用户信息成功:', res);\n    if (res.data) {\n      // 保存原始数据\n      Object.assign(userInfo, res.data);\n\n      // 处理ID字段\n      if (!userInfo.id && userInfo.uid) {\n        userInfo.id = userInfo.uid;\n      }\n\n      // 处理昵称字段\n      if (!userInfo.name && userInfo.nickname) {\n        userInfo.name = userInfo.nickname;\n      }\n\n      // 处理简介字段\n      if (!userInfo.intro && userInfo.about_me) {\n        userInfo.intro = userInfo.about_me;\n      }\n\n      // 确保关注状态是数字类型\n      userInfo.is_follow = parseInt(userInfo.is_follow || 0);\n      userInfo.is_mutual_follow = parseInt(userInfo.is_mutual_follow || 0);\n\n      // 设置关注状态\n      isFollowing.value = userInfo.is_follow === 1;\n\n      // 确保隐私设置存在\n      if (!userInfo.privacy) {\n        userInfo.privacy = {\n          like: 1,\n          follow: 1\n        };\n      }\n\n      // 处理访客记录（只有查看自己主页时才有数据）\n      if (res.data.visitors && Array.isArray(res.data.visitors)) {\n        userInfo.visitors = res.data.visitors;\n        console.log('获取到访客记录:', res.data.visitors.length, '条');\n      } else {\n        userInfo.visitors = [];\n      }\n\n      // 处理VIP信息\n      if (res.data.vip !== undefined) {\n        userInfo.vip = res.data.vip;\n        userInfo.vip_id = res.data.vip_id || 0;\n        userInfo.vip_icon = res.data.vip_icon || '';\n        userInfo.vip_name = res.data.vip_name || '';\n        userInfo.vip_status = res.data.vip_status || 2;\n        userInfo.svip_open = res.data.svip_open || false;\n        userInfo.is_ever_level = res.data.is_ever_level || 0;\n        userInfo.is_money_level = res.data.is_money_level || 0;\n        userInfo.overdue_time = res.data.overdue_time || 0;\n        console.log('获取到VIP信息:', {\n          vip: userInfo.vip,\n          vip_name: userInfo.vip_name,\n          vip_status: userInfo.vip_status,\n          svip_open: userInfo.svip_open\n        });\n      }\n\n      console.log('处理后的用户信息:', {\n        id: userInfo.id,\n        name: userInfo.name,\n        is_follow: userInfo.is_follow,\n        is_mutual_follow: userInfo.is_mutual_follow,\n        isFollowing: isFollowing.value,\n        privacy: userInfo.privacy,\n        visitorsCount: userInfo.visitors.length,\n        vip: userInfo.vip,\n        vip_status: userInfo.vip_status\n      });\n\n      // 处理背景图片\n      updateBackgroundImages();\n\n      // 设置页面标题\n      uni.setNavigationBarTitle({\n        title: userInfo.name || '用户详情'\n      });\n    } else {\n      console.error('获取用户信息返回数据为空');\n      opTipsPopup(\"获取用户信息失败\", true);\n    }\n  }).catch(err => {\n    console.error('获取用户信息失败', err);\n    opTipsPopup(\"获取用户信息失败: \" + (err.msg || \"网络错误\"), true);\n  }).finally(() => {\n    uni.hideLoading();\n  });\n}\n/**\n * 加载用户动态列表\n */\nconst loadDynamicList = () => {\n  if (isLoading.value || loadStatus.value === 'noMore') return;\n\n  isLoading.value = true;\n  loadStatus.value = 'loading';\n\n  // 判断是否是查看\"赞过\"标签，且是查看自己的页面\n  const isLikedTab = barIdx.value === 1;\n  const isOwnProfile = userId.value === loginUserId.value;\n\n  let apiCall;\n\n  if (isLikedTab) {\n    // \"赞过\"标签，调用getLikeDynamicList接口\n    console.log('加载点赞动态列表 - 用户ID:', userId.value);\n    apiCall = getLikeDynamicList(userId.value, {\n      page: page.value,\n      limit: limit.value\n    });\n  } else {\n    // \"笔记\"标签，查看用户发布的动态列表\n    console.log('加载用户动态列表, userId:', userId.value, 'barIdx:', barIdx.value);\n    apiCall = getOtherUserDynamicList(userId.value, {\n      page: page.value,\n      limit: limit.value\n    });\n  }\n\n  apiCall.then(res => {\n    console.log('动态列表接口返回:', res);\n    const listData = res.data.list || [];\n\n    if (page.value === 1) {\n      list.value = listData;\n      isEmpty.value = listData.length === 0;\n    } else {\n      list.value = [...list.value, ...listData];\n    }\n\n    loadStatus.value = listData.length < limit.value ? 'noMore' : 'more';\n    page.value++;\n\n    // 如果是第一页且有数据，判断是否需要使用瀑布流布局\n    if (page.value === 2 && listData.length > 0) {\n      // 检查是否有图片或视频内容\n      const hasMediaContent = listData.some(item =>\n        item.type === 2 || item.type === 3 ||\n        (item.images && item.images.length > 0)\n      );\n      isWaterfall.value = hasMediaContent;\n    }\n  }).catch(err => {\n    console.error('获取动态列表失败', err);\n    loadStatus.value = 'more';\n\n    // 如果是点赞列表且出错，可能需要特殊处理\n    if (isLikedTab && !isOwnProfile) {\n      // 检查是否是隐私设置问题\n      if (err.code === 403 || err.status === 403) {\n        opTipsPopup(\"该用户已将点赞设为私密\");\n      } else {\n        uni.showToast({\n          title: '加载失败，请重试',\n          icon: 'none'\n        });\n      }\n    } else {\n      uni.showToast({\n        title: '加载失败，请重试',\n        icon: 'none'\n      });\n    }\n  }).finally(() => {\n    isLoading.value = false;\n    uni.stopPullDownRefresh();\n  });\n}\n/**\n * 处理关注/取消关注\n */\nconst handleFollow = () => {\n  if (!isLogin.value) {\n    uni.navigateTo({\n      url: '/pages/login/index'\n    });\n    return;\n  }\n\n  // 防止重复点击\n  if (isProcessing.value) return;\n  isProcessing.value = true;\n\n  // 获取目标用户ID，确保是整数类型\n  const targetUserId = parseInt(userId.value);\n\n  // 验证用户ID\n  if (!targetUserId || targetUserId <= 0) {\n    uni.showToast({\n      title: \"获取用户ID失败\",\n      icon: \"none\"\n    });\n    console.error('关注操作失败: 无效的用户ID', targetUserId);\n    isProcessing.value = false;\n    return;\n  }\n\n  // 当前的关注状态\n  const isMutual = parseInt(userInfo.is_mutual_follow) === 1;\n  const isFollowed = parseInt(userInfo.is_follow) === 1 || isMutual;\n\n  console.log('当前关注状态:', {isFollowed, isMutual, targetUserId});\n\n  // 先更新UI状态\n  userInfo.is_follow = isFollowed ? 0 : 1;\n\n  // 如果取消关注，也需要更新互相关注状态\n  if (isFollowed) {\n    userInfo.is_mutual_follow = 0;\n  }\n\n  isFollowing.value = !isFollowed;\n\n  // 显示加载提示\n  uni.showToast({\n    title: isFollowed ? \"取消关注中...\" : \"关注中...\",\n    icon: \"none\",\n    duration: 500\n  });\n\n  // 准备请求参数\n  const params = {\n    follow_uid: targetUserId,\n    is_follow: isFollowed ? 0 : 1\n  };\n\n  console.log('发送关注请求参数:', JSON.stringify(params));\n\n  followUser(params).then(res => {\n    console.log('关注接口返回:', res);\n\n    if (res.status === 200) {\n      // 处理互相关注状态\n      if (res.data && res.data.is_mutual !== undefined) {\n        const isMutual = parseInt(res.data.is_mutual) === 1;\n        userInfo.is_mutual_follow = isMutual ? 1 : 0;\n        console.log('更新互相关注状态:', userInfo.is_mutual_follow);\n      }\n\n      // 更新粉丝数量\n      if (res.data && res.data.fans_count !== undefined) {\n        userInfo.fans_count = res.data.fans_count;\n      } else {\n        // 如果接口没有返回粉丝数，手动更新\n        const currentFansCount = parseInt(userInfo.fans_count) || 0;\n        userInfo.fans_count = isFollowed ? Math.max(0, currentFansCount - 1) : currentFansCount + 1;\n      }\n\n      uni.showToast({\n        title: isFollowed ? '已取消关注' : '关注成功',\n        icon: 'none'\n      });\n    } else {\n      // 恢复原状态\n      userInfo.is_follow = isFollowed ? 1 : 0;\n      if (isMutual) {\n        userInfo.is_mutual_follow = isMutual ? 1 : 0;\n      }\n      isFollowing.value = isFollowed;\n\n      uni.showToast({\n        title: res.msg || '操作失败，请重试',\n        icon: 'none'\n      });\n    }\n  }).catch(err => {\n    // 失败时恢复状态\n    userInfo.is_follow = isFollowed ? 1 : 0;\n    if (isMutual) {\n      userInfo.is_mutual_follow = isMutual ? 1 : 0;\n    }\n    isFollowing.value = isFollowed;\n\n    uni.showToast({\n      title: '网络错误，请重试',\n      icon: 'none'\n    });\n    console.error('关注操作异常:', err);\n  }).finally(() => {\n    isProcessing.value = false;\n  });\n}\n\n/**\n * 处理点赞回调\n */\nconst likeClick = (e) => {\n  const { id, isLike } = e;\n  // 点赞状态已经在组件内部处理，这里可以做额外操作\n  console.log('点赞状态变更', id, isLike);\n}\n\n/**\n * 处理关注回调\n */\nconst followBack = (e) => {\n  const { idx, uid, is_follow, is_mutual } = e;\n  console.log('关注状态回调', { idx, uid, is_follow, is_mutual });\n\n  // 更新列表中对应项的关注状态\n  if (list.value[idx]) {\n    list.value[idx].is_follow = is_follow;\n    list.value[idx].is_mutual_follow = is_mutual;\n\n    // 如果有user_info字段，也要更新\n    if (list.value[idx].user_info) {\n      list.value[idx].user_info.is_follow = is_follow;\n      list.value[idx].user_info.is_mutual_follow = is_mutual;\n    }\n  }\n}\n// 下拉刷新\nonPullDownRefresh(() => {\n  console.log('下拉刷新');\n\n  // 重置分页状态\n  page.value = 1;\n  list.value = [];\n  isEmpty.value = false;\n  loadStatus.value = 'loading';\n\n  // 重新获取用户信息和动态列表\n  Promise.all([\n    getUserInfo(),\n    loadDynamicList()\n  ]).finally(() => {\n    uni.stopPullDownRefresh();\n  });\n})\n\n// 触底加载更多\nonReachBottom(() => {\n  console.log('触底加载更多, 当前页:', page.value, '加载状态:', loadStatus.value);\n\n  // 只有在有数据且不是加载中状态时才加载更多\n  if (loadStatus.value === 'more' && !isLoading.value) {\n    loadDynamicList();\n  }\n})\nconst barClick = (e) => {\n  // 防止重复点击\n  if (isThrottling.value) return;\n\n  const newBarIdx = parseInt(e.currentTarget.dataset.idx);\n\n  // 如果点击的是当前标签，不做任何操作\n  if (newBarIdx === barIdx.value) return;\n\n  isThrottling.value = true;\n  barIdx.value = newBarIdx;\n\n  console.log('切换标签:', barIdx.value, '用户ID:', userId.value, '登录用户ID:', loginUserId.value);\n\n  // 检查隐私设置：只有查看其他人的\"赞过\"时才需要检查隐私设置\n  const isOtherUserLikedTab = barIdx.value === 1 && userId.value !== loginUserId.value;\n  if (isOtherUserLikedTab && userInfo.privacy && userInfo.privacy.like === 0) {\n    console.log('该用户的点赞内容不可见');\n    isEmpty.value = true;\n    isThrottling.value = false;\n    return;\n  }\n\n  // 显示加载状态\n  uni.showLoading({\n    title: '加载中...',\n    mask: true\n  });\n\n  // 重置分页和状态\n  page.value = 1;\n  list.value = [];\n  isEmpty.value = false;\n  loadStatus.value = 'loading';\n  isWaterfall.value = false; // 重置瀑布流状态\n\n  // 加载数据\n  loadDynamicList();\n\n  // 重置节流状态\n  setTimeout(() => {\n    isThrottling.value = false;\n    uni.hideLoading();\n  }, 300);\n}\n\nconst followClick = () => {\n  // 直接调用handleFollow处理关注逻辑\n  handleFollow();\n}\n\nconst toFollow = (e) => {\n  let type = e.currentTarget.dataset.type\n\n  if (userInfo.privacy.follow == 0) {\n    let msg = \"由于该用户隐私设置，关注列表不可见\"\n    if (type == 1) {\n      msg = \"由于该用户隐私设置，粉丝列表不可见\"\n    }\n    return opTipsPopup(msg)\n  }\n\n  uni.navigateTo({\n    url: \"/pages/center/follow?type=\" + type + \"&id=\" + userInfo.id + \"&name=\" + userInfo.name\n  })\n}\n\nconst likePopupClick = (show) => {\n  if (!show) {\n    likePopup.value.close()\n  }\n  if (show) {\n    likePopup.value.open()\n  }\n}\n\nconst navigateToFun = (e) => {\n  uni.navigateTo({\n    url: \"/pages/\" + e.currentTarget.dataset.url\n  })\n}\n\nconst navBack = () => {\n  if (getCurrentPages().length > 1) {\n    uni.navigateBack()\n  } else {\n    uni.switchTab({\n      url: \"//pages/index/index\"\n    })\n  }\n}\n\nconst opTipsPopup = (msg, back) => {\n  tipsTitle.value = msg\n  tipsPopup.value.open()\n\n  setTimeout(function() {\n    tipsPopup.value.close()\n    if (back) {\n      navBack()\n    }\n  }, 2000)\n}\nconst navigationBarColor = (color) => {\n  uni.setNavigationBarColor({\n    frontColor: color,\n    backgroundColor: \"#ffffff\",\n    animation: {\n      duration: 400,\n      timingFunc: \"easeIn\"\n    }\n  })\n}\n\nconst switchBackground = (index) => {\n  currentBgIndex.value = index\n  // 重新启动轮播定时器\n  startCarousel()\n}\nconst updateBackgroundImages = () => {\n  try {\n    console.log('处理用户背景图片数据:', userInfo.home_background);\n\n    // 清空当前背景图片数组\n    backgroundImages.value = [];\n\n    // 如果存在背景图片数据\n    if (userInfo.home_background) {\n      let bgImages = [];\n\n      // 尝试解析JSON字符串\n      if (typeof userInfo.home_background === 'string') {\n        try {\n          // 先检查是否是空字符串或\"[]\"\n          if (userInfo.home_background.trim() === '' || userInfo.home_background.trim() === '[]') {\n            bgImages = [];\n          } else {\n          bgImages = JSON.parse(userInfo.home_background);\n          console.log('解析背景图片JSON成功:', bgImages);\n          }\n        } catch (error) {\n          console.error('解析背景图片JSON失败:', error);\n          // 如果解析失败，可能是单个图片URL\n          if (userInfo.home_background.trim().startsWith('http')) {\n            bgImages = [{ url: userInfo.home_background }];\n          }\n        }\n      }\n      // 如果已经是数组，直接使用\n      else if (Array.isArray(userInfo.home_background)) {\n        bgImages = userInfo.home_background;\n      }\n\n      // 确保每个背景图片对象都有url属性\n      backgroundImages.value = bgImages.filter(item => item && item.url && item.url.trim() !== '');\n    }\n\n    // 如果没有背景图片，使用用户头像作为背景\n    if (backgroundImages.value.length === 0) {\n      const avatarUrl = userInfo.avatar || '/static/img/default-bg.png';\n      backgroundImages.value = [{ url: avatarUrl }];\n    }\n\n    console.log('处理后的背景图片数组:', backgroundImages.value);\n\n    // 重置轮播索引\n    currentBgIndex.value = 0;\n\n    // 只有当有多张图片时才启动轮播\n    if (backgroundImages.value.length > 1) {\n      startCarousel();\n    } else {\n      clearCarouselTimer();\n    }\n  } catch (error) {\n    console.error('处理背景图片数据异常:', error);\n    // 出错时使用默认背景\n    backgroundImages.value = [{ url: userInfo.avatar || '/static/img/default-bg.png' }];\n    currentBgIndex.value = 0;\n    clearCarouselTimer();\n  }\n}\n\n// 启动背景轮播\nconst startCarousel = () => {\n  clearCarouselTimer();\n\n  // 如果有多张图片才启动轮播\n  if (backgroundImages.value.length > 1) {\n    carouselTimer.value = setInterval(() => {\n      nextBackground();\n    }, 4000); // 每4秒切换一次\n  }\n}\n\n// 清理轮播定时器\nconst clearCarouselTimer = () => {\n  if (carouselTimer.value) {\n    clearInterval(carouselTimer.value);\n    carouselTimer.value = null;\n  }\n}\n\n// 下一张背景\nconst nextBackground = () => {\n  if (backgroundImages.value.length > 0) {\n    currentBgIndex.value = (currentBgIndex.value + 1) % backgroundImages.value.length;\n  }\n}\n/**\n * 重试加载\n */\nconst retryLoad = () => {\n  hasError.value = false;\n  errorMessage.value = \"\";\n  page.value = 1;\n  list.value = [];\n  isEmpty.value = false;\n  loadStatus.value = 'loading';\n\n  // 重新加载数据\n  getUserInfo();\n  loadDynamicList();\n}\n\n/**\n * 设置错误状态\n */\nconst setError = (message) => {\n  hasError.value = true;\n  errorMessage.value = message;\n  loadStatus.value = 'more';\n}\n\n/**\n * 清除错误状态\n */\nconst clearError = () => {\n  hasError.value = false;\n  errorMessage.value = \"\";\n}\n\n// 页面滚动\nonPageScroll((e) => {\n  let frontColor = \"#ffffff\"\n  let ratio = (e.scrollTop > 180 ? 180 : e.scrollTop) / 180\n\n  if (ratio >= 1) {\n    frontColor = \"#000000\"\n  }\n\n  navbarTrans.value = ratio\n  navigationBarColor(frontColor)\n})\n\n// 分享到微信\nonShareAppMessage(() => {\n  return {\n    title: userInfo.name + \" 的个人名片\",\n    imageUrl: userInfo.avatar,\n    path: \"/pages/user/details?id=\" + userInfo.id\n  }\n})\n\n// 分享到朋友圈\nonShareTimeline(() => {\n  return {\n    title: userInfo.name + \" 的个人名片\",\n    imageUrl: userInfo.avatar,\n    query: \"id=\" + userInfo.id\n  }\n})\n</script>\n\n<style>\n.nav-box{\n  position: fixed;\n  z-index: 99;\n  top: 0;\n  left: 0;\n  width: 100%;\n  box-sizing: border-box;\n}\n.nav-box .nav-back{\n  padding: 0 30rpx;\n  width: 34rpx;\n}\n.nav-box .nav-title{\n  max-width: 60%;\n  font-size: 32rpx;\n  font-weight: 700;\n}\n.user-box{\n  width: calc(100% - 60rpx);\n  padding: 60rpx 30rpx;\n  color: #fff;\n  position: relative;\n  overflow: hidden;\n}\n.user-box .user-img, .user-box .user-bg{\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n}\n.user-box .user-bg{\n  z-index: -1;\n  background: rgba(0, 0, 0, .5);\n}\n.user-box .user-top{\n  width: 100%;\n  justify-content: space-between;\n}\n.user-top .avatar{\n  width: 180rpx;\n  height: 180rpx;\n  border-radius: 50%;\n  background: #fff;\n  border: 2px solid #f5f5f5;\n  overflow: hidden;\n}\n.user-top .btn{\n  padding: 0 30rpx;\n  height: 64rpx;\n  line-height: 64rpx;\n  border-radius: 8rpx;\n  font-size: 20rpx;\n  font-weight: 700;\n  color: #000;\n  background: #fff;\n}\n.user-top .active{\n  color: rgba(255, 255, 255, .52);\n  background: rgba(255, 255, 255, .1);\n}\n.user-top .mutual{\n  color: #576b95;\n  background: rgba(255, 255, 255, .2);\n}\n.user-box .user-name{\n  margin: 20rpx 0 10rpx;\n  width: 100%;\n  font-size: 34rpx;\n  font-weight: 700;\n}\n.user-box .user-intro{\n  width: 100%;\n  word-break: break-word;\n  white-space: pre-line;\n}\n.user-box .user-intro text{\n  color: #ccc;\n  font-size: 24rpx;\n  font-weight: 400;\n}\n.user-box .user-tag{\n  margin: 20rpx 0;\n  width: 100%;\n}\n.user-tag .tag-item{\n  margin-right: 16rpx;\n  height: 44rpx;\n  padding: 0 14rpx;\n  border-radius: 8rpx;\n  background: rgba(255, 255, 255, .1);\n  font-weight: 500;\n  font-size: 20rpx;\n  justify-content: center;\n}\n.user-tag .tag-item image{\n  width: 24rpx;\n  height: 24rpx;\n}\n.user-num .num-item{\n  margin-right: 30rpx;\n  font-size: 20rpx;\n  font-weight: 300;\n  color: #ccc;\n}\n.user-num .num-item .t1{\n  color: #fff;\n  font-size: 28rpx;\n  font-weight: 700;\n  margin-right: 6rpx;\n}\n.content-box{\n  margin-top: -30rpx;\n  background: #fff;\n  padding: 30rpx 0;\n  border-radius: 30rpx 30rpx 0 0;\n}\n.block-box .block-title{\n  padding: 0 30rpx;\n  font-size: 26rpx;\n  font-weight: 700;\n}\n.block-box .circle-box{\n  width: calc(100% - 20rpx);\n  padding: 30rpx 10rpx;\n  display: flex;\n}\n.circle-box .circle-item{\n  flex-shrink: 0;\n  margin: 0 10rpx;\n  flex-direction: column;\n  justify-content: center;\n}\n.circle-item .circle-avatar{\n  width: 100rpx;\n  height: 100rpx;\n  border-radius: 50%;\n  background: #f8f8f8;\n}\n.circle-item .circle-name{\n  margin-top: 15rpx;\n  width: 120rpx;\n  color: #999;\n  font-size: 20rpx;\n  text-align: center;\n}\n.bar-box{\n  position: -webkit-sticky;\n  position: sticky;\n  left: 0;\n  z-index: 99;\n  margin-top: -1px;\n  width: 100%;\n  height: 80rpx;\n  background: #fff;\n}\n.bar-box .bar-item{\n  padding: 0 30rpx;\n  height: 100%;\n  flex-direction: column;\n  justify-content: center;\n  position: relative;\n}\n.bar-box .bar-item text{\n  font-weight: 700;\n  transition: all .3s ease-in-out;\n}\n.bar-item .bar-line{\n  position: absolute;\n  bottom: 12rpx;\n  width: 18rpx;\n  height: 6rpx;\n  border-radius: 6rpx;\n  background: #000;\n  transition: opacity .3s ease-in-out;\n}\n.content-box .dynamic-box{\n  width: calc(100% - 16rpx);\n  padding: 22rpx 8rpx 0;\n}\n.loading-box {\n  width: 100%;\n  padding: 60rpx 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n.load-more-box {\n  width: 100%;\n  padding: 30rpx 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.error-box{\n  width: 100%;\n  padding: 100rpx 0;\n  flex-direction: column;\n}\n.error-box image{\n  width: 300rpx;\n  height: 300rpx;\n  margin-bottom: 30rpx;\n}\n.error-box .e1{\n  font-size: 30rpx;\n  font-weight: 700;\n  color: #333;\n}\n.error-box .e2{\n  margin-top: 10rpx;\n  color: #999;\n  font-size: 26rpx;\n  text-align: center;\n  padding: 0 60rpx;\n}\n.error-box .retry-btn{\n  margin-top: 40rpx;\n  width: 200rpx;\n  height: 80rpx;\n  line-height: 80rpx;\n  text-align: center;\n  font-size: 28rpx;\n  font-weight: 700;\n  color: #fff;\n  background: #007aff;\n  border-radius: 40rpx;\n}\n.tips-box{\n  justify-content: center;\n  width: 100%;\n}\n.tips-box .tips-item{\n  background: #000;\n  color: #fff;\n  padding: 20rpx 40rpx;\n  border-radius: 12rpx;\n  font-size: 24rpx;\n  font-weight: 700;\n}\n.df{\n  display: flex;\n  align-items: center;\n}\n.ohto{\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n.xwb{\n  filter: invert(1);\n}\n.background-carousel {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n.carousel-item {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  opacity: 0;\n  transition: opacity 0.5s ease-in-out;\n}\n.carousel-item.active {\n  opacity: 1;\n}\n.carousel-indicators {\n  position: absolute;\n  bottom: 20rpx;\n  left: 0;\n  right: 0;\n  display: flex;\n  justify-content: center;\n}\n.indicator {\n  width: 12rpx;\n  height: 4rpx;\n  background-color: rgba(255, 255, 255, 0.5);\n  border-radius: 2rpx;\n  margin: 0 4rpx;\n  cursor: pointer;\n}\n.indicator.active {\n  background-color: #fff;\n}\n.default-background {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n.like-popup{\n  width: 400rpx;\n  background: #fff;\n  padding: 30rpx;\n  border-radius: 30rpx;\n  overflow: hidden;\n}\n.like-popup .like-img{\n  margin: 0 40rpx;\n  width: 320rpx;\n  height: 200rpx;\n}\n.like-popup .like-content{\n  margin: 20rpx 0 40rpx;\n  width: 100%;\n  color: #333;\n  font-size: 26rpx;\n  text-align: center;\n}\n.like-popup .like-btn{\n  width: 100%;\n  height: 80rpx;\n  line-height: 80rpx;\n  text-align: center;\n  font-size: 24rpx;\n  font-weight: 700;\n  color: #fff;\n  background: #000;\n  border-radius: 16rpx;\n}\n</style> ", "import MiniProgramPage from 'Z:/WWW/shejiao/vue3/pages/user/details.vue'\nwx.createPage(MiniProgramPage)"], "names": ["getCurrentInstance", "useUserStore", "ref", "reactive", "computed", "onLoad", "uni", "onUnload", "getUserHomepage", "getLikeDynamicList", "getOtherUserDynamicList", "followUser", "isMutual", "onPullDownRefresh", "onReachBottom", "onPageScroll", "onShareAppMessage", "onShareTimeline", "MiniProgramPage"], "mappings": ";;;;;;;;;;;;;AAsLA,MAAA,YAAA,MAAA;AACA,MAAA,cAAA,MAAA;AACA,MAAA,YAAA,MAAA;AACA,MAAA,SAAA,MAAA;AACA,MAAA,YAAA,MAAA;;;;AAKA,UAAA,WAAAA,cAAAA,mBAAA;AACA,UAAA,YAAAC,YAAAA,aAAA;AAEA,UAAA,kBAAAC,cAAA,IAAA,EAAA;AACA,UAAA,iBAAAA,cAAA,IAAA,EAAA;AACA,UAAA,cAAAA,cAAA,IAAA,CAAA;AACA,UAAA,WAAAC,cAAAA,SAAA;AAAA,MACA,IAAA;AAAA,MACA,KAAA;AAAA,MACA,QAAA;AAAA,MACA,MAAA;AAAA,MACA,UAAA;AAAA,MACA,OAAA;AAAA,MACA,UAAA;AAAA,MACA,QAAA;AAAA,MACA,KAAA;AAAA,MACA,KAAA;AAAA,MACA,eAAA;AAAA,MACA,qBAAA;AAAA,MACA,UAAA;AAAA,MACA,cAAA;AAAA,MACA,YAAA;AAAA,MACA,YAAA;AAAA,MACA,gBAAA;AAAA,MACA,WAAA;AAAA,MACA,kBAAA;AAAA,MACA,QAAA,CAAA;AAAA,MACA,gBAAA;AAAA,MACA,SAAA;AAAA,QACA,MAAA;AAAA,QACA,QAAA;AAAA,MACA;AAAA,MACA,iBAAA;AAAA,MACA,UAAA,CAAA;AAAA;AAAA;AAAA,MAEA,KAAA;AAAA,MACA,QAAA;AAAA,MACA,UAAA;AAAA,MACA,UAAA;AAAA,MACA,YAAA;AAAA,MACA,WAAA;AAAA,MACA,eAAA;AAAA,MACA,gBAAA;AAAA,MACA,cAAA;AAAA,IACA,CAAA;AACA,UAAA,UAAAD,cAAA,IAAA,CAAA,MAAA,IAAA,CAAA;AACA,UAAA,SAAAA,cAAA,IAAA,CAAA;AACA,UAAA,eAAAA,cAAA,IAAA,KAAA;AACA,UAAA,OAAAA,cAAA,IAAA,EAAA;AACA,UAAA,OAAAA,cAAA,IAAA,CAAA;AACA,UAAA,UAAAA,cAAA,IAAA,KAAA;AACA,UAAA,aAAAA,cAAA,IAAA,SAAA;AACA,UAAA,YAAAA,cAAA,IAAA,EAAA;AACA,UAAA,cAAAA,cAAA,IAAA,KAAA;AACA,UAAA,mBAAAA,cAAA,IAAA,EAAA;AACA,UAAA,iBAAAA,cAAA,IAAA,CAAA;AACA,UAAA,gBAAAA,cAAA,IAAA,IAAA;AACA,UAAA,SAAAA,cAAA,IAAA,CAAA;AACA,UAAA,cAAAA,cAAA,IAAA,KAAA;AACA,UAAA,QAAAA,cAAA,IAAA,EAAA;AACA,UAAA,YAAAA,cAAA,IAAA,KAAA;AACA,UAAA,eAAAA,cAAA,IAAA,KAAA;AAEAA,kBAAA,IAAA,KAAA;AACAA,kBAAA,IAAA,KAAA;AAEA,UAAA,WAAAA,cAAA,IAAA,KAAA;AACA,UAAA,eAAAA,cAAA,IAAA,EAAA;AAGA,UAAA,YAAAA,cAAA,IAAA,IAAA;AACA,UAAA,YAAAA,cAAA,IAAA,IAAA;AAEA,UAAA,UAAAE,cAAA,SAAA,MAAA;AACA,aAAA,UAAA;AAAA,IACA,CAAA;AAEA,UAAA,cAAAA,cAAA,SAAA,MAAA;AACA,aAAA,UAAA,OAAA;AAAA,IACA,CAAA;AAGA,UAAA,iBAAAA,cAAA,SAAA,MAAA;AAEA,UAAA,SAAA,SAAA,gBAAA,MAAA,GAAA;AACA,eAAA;AAAA,MACA,WAEA,YAAA,SAAA,SAAA,SAAA,SAAA,MAAA,GAAA;AACA,eAAA;AAAA,MACA,OAEA;AACA,eAAA;AAAA,MACA;AAAA,IACA,CAAA;AAGA,UAAA,gBAAAA,cAAA,SAAA,MAAA;AAEA,UAAA,SAAA,SAAA,gBAAA,MAAA,GAAA;AACA,eAAA;AAAA,MACA,WAEA,YAAA,SAAA,SAAA,SAAA,SAAA,MAAA,GAAA;AACA,eAAA;AAAA,MACA,OAEA;AACA,eAAA;AAAA,MACA;AAAA,IACA,CAAA;AAEAC,kBAAA,OAAA,OAAA,YAAA;AAEA,UAAA;AAEA,YAAA,OAAAC,cAAAA,MAAA,kBAAA,YAAA;AACAA,wBAAAA,MAAA,cAAA;AAAA,QACA;AAAA,MAEA,SAAA,GAAA;AACAA,sBAAAA,MAAA,MAAA,QAAA,iCAAA,iDAAA,CAAA;AAAA,MACA;AAGA,YAAA,SAAA,WAAA,IAAA,OAAA,iBAAA;AAEAA,oBAAA,MAAA,MAAA,OAAA,iCAAA,eAAA,OAAA;AAEA,UAAA;AAEA,cAAA,eAAA,mCAAA,QAAA,mCAAA,aAAA,mCAAA,QAAA;AAEA,YAAA,aAAA;AAEA,iBAAA,QAAA,SAAA,WAAA;AAEA,cAAA,MAAA,OAAA,KAAA,KAAA,OAAA,SAAA,GAAA;AACAA,0BAAA,MAAA,MAAA,SAAA,iCAAA,WAAA,WAAA;AACA,wBAAA,UAAA,IAAA;AACA;AAAA,UACA;AAGA,gBAAA,qBAAA,YAAA,SAAAA,cAAA,MAAA,eAAA,KAAA,KAAA;AACA,gBAAA,gBAAA,OAAA,UAAA,SAAA,kBAAA;AAEAA,8BAAA,MAAA,OAAA,iCAAA,WAAA,OAAA,OAAA,SAAA,aAAA;AAGA,cAAA,eAAA;AACA,oBAAA,QAAA,CAAA,MAAA,IAAA;AAAA,UACA,OAAA;AACA,oBAAA,QAAA,CAAA,MAAA,IAAA;AAAA,UACA;AAGA;AACA;QACA,OAAA;AACAA,wBAAAA,MAAA,MAAA,SAAA,iCAAA,UAAA;AACA,sBAAA,eAAA,IAAA;AAAA,QACA;AAAA,MACA,SAAA,GAAA;AACAA,sBAAA,MAAA,MAAA,SAAA,iCAAA,aAAA,CAAA;AACA,oBAAA,YAAA,IAAA;AAAA,MACA;AAAA,IACA,CAAA;AAGAC,kBAAAA,SAAA,MAAA;AAEA;IACA,CAAA;AAIA,UAAA,cAAA,MAAA;AACAD,oBAAA,MAAA,MAAA,OAAA,iCAAA,qBAAA,OAAA,KAAA;AAGA,UAAA,CAAA,OAAA,SAAA,OAAA,SAAA,GAAA;AACAA,sBAAA,MAAA,MAAA,SAAA,iCAAA,oBAAA,OAAA,KAAA;AACA,oBAAA,UAAA,IAAA;AACA;AAAA,MACA;AAGAA,oBAAAA,MAAA,YAAA;AAAA,QACA,OAAA;AAAA,QACA,MAAA;AAAA,MACA,CAAA;AAGAE,iCAAA;AAAA,QACA,SAAA,OAAA;AAAA,MACA,CAAA,EAAA,KAAA,SAAA;AACAF,sBAAA,MAAA,MAAA,OAAA,iCAAA,aAAA,GAAA;AACA,YAAA,IAAA,MAAA;AAEA,iBAAA,OAAA,UAAA,IAAA,IAAA;AAGA,cAAA,CAAA,SAAA,MAAA,SAAA,KAAA;AACA,qBAAA,KAAA,SAAA;AAAA,UACA;AAGA,cAAA,CAAA,SAAA,QAAA,SAAA,UAAA;AACA,qBAAA,OAAA,SAAA;AAAA,UACA;AAGA,cAAA,CAAA,SAAA,SAAA,SAAA,UAAA;AACA,qBAAA,QAAA,SAAA;AAAA,UACA;AAGA,mBAAA,YAAA,SAAA,SAAA,aAAA,CAAA;AACA,mBAAA,mBAAA,SAAA,SAAA,oBAAA,CAAA;AAGA,sBAAA,QAAA,SAAA,cAAA;AAGA,cAAA,CAAA,SAAA,SAAA;AACA,qBAAA,UAAA;AAAA,cACA,MAAA;AAAA,cACA,QAAA;AAAA,YACA;AAAA,UACA;AAGA,cAAA,IAAA,KAAA,YAAA,MAAA,QAAA,IAAA,KAAA,QAAA,GAAA;AACA,qBAAA,WAAA,IAAA,KAAA;AACAA,0BAAAA,MAAA,MAAA,OAAA,iCAAA,YAAA,IAAA,KAAA,SAAA,QAAA,GAAA;AAAA,UACA,OAAA;AACA,qBAAA,WAAA;UACA;AAGA,cAAA,IAAA,KAAA,QAAA,QAAA;AACA,qBAAA,MAAA,IAAA,KAAA;AACA,qBAAA,SAAA,IAAA,KAAA,UAAA;AACA,qBAAA,WAAA,IAAA,KAAA,YAAA;AACA,qBAAA,WAAA,IAAA,KAAA,YAAA;AACA,qBAAA,aAAA,IAAA,KAAA,cAAA;AACA,qBAAA,YAAA,IAAA,KAAA,aAAA;AACA,qBAAA,gBAAA,IAAA,KAAA,iBAAA;AACA,qBAAA,iBAAA,IAAA,KAAA,kBAAA;AACA,qBAAA,eAAA,IAAA,KAAA,gBAAA;AACAA,0BAAAA,MAAA,MAAA,OAAA,iCAAA,aAAA;AAAA,cACA,KAAA,SAAA;AAAA,cACA,UAAA,SAAA;AAAA,cACA,YAAA,SAAA;AAAA,cACA,WAAA,SAAA;AAAA,YACA,CAAA;AAAA,UACA;AAEAA,wBAAAA,MAAA,MAAA,OAAA,iCAAA,aAAA;AAAA,YACA,IAAA,SAAA;AAAA,YACA,MAAA,SAAA;AAAA,YACA,WAAA,SAAA;AAAA,YACA,kBAAA,SAAA;AAAA,YACA,aAAA,YAAA;AAAA,YACA,SAAA,SAAA;AAAA,YACA,eAAA,SAAA,SAAA;AAAA,YACA,KAAA,SAAA;AAAA,YACA,YAAA,SAAA;AAAA,UACA,CAAA;AAGA;AAGAA,wBAAAA,MAAA,sBAAA;AAAA,YACA,OAAA,SAAA,QAAA;AAAA,UACA,CAAA;AAAA,QACA,OAAA;AACAA,wBAAAA,MAAA,MAAA,SAAA,iCAAA,cAAA;AACA,sBAAA,YAAA,IAAA;AAAA,QACA;AAAA,MACA,CAAA,EAAA,MAAA,SAAA;AACAA,sBAAA,MAAA,MAAA,SAAA,iCAAA,YAAA,GAAA;AACA,oBAAA,gBAAA,IAAA,OAAA,SAAA,IAAA;AAAA,MACA,CAAA,EAAA,QAAA,MAAA;AACAA,sBAAA,MAAA,YAAA;AAAA,MACA,CAAA;AAAA,IACA;AAIA,UAAA,kBAAA,MAAA;AACA,UAAA,UAAA,SAAA,WAAA,UAAA;AAAA;AAEA,gBAAA,QAAA;AACA,iBAAA,QAAA;AAGA,YAAA,aAAA,OAAA,UAAA;AACA,YAAA,eAAA,OAAA,UAAA,YAAA;AAEA,UAAA;AAEA,UAAA,YAAA;AAEAA,sBAAA,MAAA,MAAA,OAAA,iCAAA,oBAAA,OAAA,KAAA;AACA,kBAAAG,WAAAA,mBAAA,OAAA,OAAA;AAAA,UACA,MAAA,KAAA;AAAA,UACA,OAAA,MAAA;AAAA,QACA,CAAA;AAAA,MACA,OAAA;AAEAH,sBAAAA,MAAA,MAAA,OAAA,iCAAA,qBAAA,OAAA,OAAA,WAAA,OAAA,KAAA;AACA,kBAAAI,WAAAA,wBAAA,OAAA,OAAA;AAAA,UACA,MAAA,KAAA;AAAA,UACA,OAAA,MAAA;AAAA,QACA,CAAA;AAAA,MACA;AAEA,cAAA,KAAA,SAAA;AACAJ,sBAAA,MAAA,MAAA,OAAA,iCAAA,aAAA,GAAA;AACA,cAAA,WAAA,IAAA,KAAA,QAAA,CAAA;AAEA,YAAA,KAAA,UAAA,GAAA;AACA,eAAA,QAAA;AACA,kBAAA,QAAA,SAAA,WAAA;AAAA,QACA,OAAA;AACA,eAAA,QAAA,CAAA,GAAA,KAAA,OAAA,GAAA,QAAA;AAAA,QACA;AAEA,mBAAA,QAAA,SAAA,SAAA,MAAA,QAAA,WAAA;AACA,aAAA;AAGA,YAAA,KAAA,UAAA,KAAA,SAAA,SAAA,GAAA;AAEA,gBAAA,kBAAA,SAAA;AAAA,YAAA,UACA,KAAA,SAAA,KAAA,KAAA,SAAA,KACA,KAAA,UAAA,KAAA,OAAA,SAAA;AAAA,UACA;AACA,sBAAA,QAAA;AAAA,QACA;AAAA,MACA,CAAA,EAAA,MAAA,SAAA;AACAA,sBAAA,MAAA,MAAA,SAAA,iCAAA,YAAA,GAAA;AACA,mBAAA,QAAA;AAGA,YAAA,cAAA,CAAA,cAAA;AAEA,cAAA,IAAA,SAAA,OAAA,IAAA,WAAA,KAAA;AACA,wBAAA,aAAA;AAAA,UACA,OAAA;AACAA,0BAAAA,MAAA,UAAA;AAAA,cACA,OAAA;AAAA,cACA,MAAA;AAAA,YACA,CAAA;AAAA,UACA;AAAA,QACA,OAAA;AACAA,wBAAAA,MAAA,UAAA;AAAA,YACA,OAAA;AAAA,YACA,MAAA;AAAA,UACA,CAAA;AAAA,QACA;AAAA,MACA,CAAA,EAAA,QAAA,MAAA;AACA,kBAAA,QAAA;AACAA,sBAAA,MAAA,oBAAA;AAAA,MACA,CAAA;AAAA,IACA;AAIA,UAAA,eAAA,MAAA;AACA,UAAA,CAAA,QAAA,OAAA;AACAA,sBAAAA,MAAA,WAAA;AAAA,UACA,KAAA;AAAA,QACA,CAAA;AACA;AAAA,MACA;AAGA,UAAA,aAAA;AAAA;AACA,mBAAA,QAAA;AAGA,YAAA,eAAA,SAAA,OAAA,KAAA;AAGA,UAAA,CAAA,gBAAA,gBAAA,GAAA;AACAA,sBAAAA,MAAA,UAAA;AAAA,UACA,OAAA;AAAA,UACA,MAAA;AAAA,QACA,CAAA;AACAA,sBAAA,MAAA,MAAA,SAAA,iCAAA,mBAAA,YAAA;AACA,qBAAA,QAAA;AACA;AAAA,MACA;AAGA,YAAA,WAAA,SAAA,SAAA,gBAAA,MAAA;AACA,YAAA,aAAA,SAAA,SAAA,SAAA,MAAA,KAAA;AAEAA,0BAAA,MAAA,OAAA,iCAAA,WAAA,EAAA,YAAA,UAAA,aAAA,CAAA;AAGA,eAAA,YAAA,aAAA,IAAA;AAGA,UAAA,YAAA;AACA,iBAAA,mBAAA;AAAA,MACA;AAEA,kBAAA,QAAA,CAAA;AAGAA,oBAAAA,MAAA,UAAA;AAAA,QACA,OAAA,aAAA,aAAA;AAAA,QACA,MAAA;AAAA,QACA,UAAA;AAAA,MACA,CAAA;AAGA,YAAA,SAAA;AAAA,QACA,YAAA;AAAA,QACA,WAAA,aAAA,IAAA;AAAA,MACA;AAEAA,0BAAA,MAAA,OAAA,iCAAA,aAAA,KAAA,UAAA,MAAA,CAAA;AAEAK,iBAAAA,WAAA,MAAA,EAAA,KAAA,SAAA;AACAL,sBAAA,MAAA,MAAA,OAAA,iCAAA,WAAA,GAAA;AAEA,YAAA,IAAA,WAAA,KAAA;AAEA,cAAA,IAAA,QAAA,IAAA,KAAA,cAAA,QAAA;AACA,kBAAAM,YAAA,SAAA,IAAA,KAAA,SAAA,MAAA;AACA,qBAAA,mBAAAA,YAAA,IAAA;AACAN,0BAAA,MAAA,MAAA,OAAA,iCAAA,aAAA,SAAA,gBAAA;AAAA,UACA;AAGA,cAAA,IAAA,QAAA,IAAA,KAAA,eAAA,QAAA;AACA,qBAAA,aAAA,IAAA,KAAA;AAAA,UACA,OAAA;AAEA,kBAAA,mBAAA,SAAA,SAAA,UAAA,KAAA;AACA,qBAAA,aAAA,aAAA,KAAA,IAAA,GAAA,mBAAA,CAAA,IAAA,mBAAA;AAAA,UACA;AAEAA,wBAAAA,MAAA,UAAA;AAAA,YACA,OAAA,aAAA,UAAA;AAAA,YACA,MAAA;AAAA,UACA,CAAA;AAAA,QACA,OAAA;AAEA,mBAAA,YAAA,aAAA,IAAA;AACA,cAAA,UAAA;AACA,qBAAA,mBAAA,WAAA,IAAA;AAAA,UACA;AACA,sBAAA,QAAA;AAEAA,wBAAAA,MAAA,UAAA;AAAA,YACA,OAAA,IAAA,OAAA;AAAA,YACA,MAAA;AAAA,UACA,CAAA;AAAA,QACA;AAAA,MACA,CAAA,EAAA,MAAA,SAAA;AAEA,iBAAA,YAAA,aAAA,IAAA;AACA,YAAA,UAAA;AACA,mBAAA,mBAAA,WAAA,IAAA;AAAA,QACA;AACA,oBAAA,QAAA;AAEAA,sBAAAA,MAAA,UAAA;AAAA,UACA,OAAA;AAAA,UACA,MAAA;AAAA,QACA,CAAA;AACAA,sBAAA,MAAA,MAAA,SAAA,iCAAA,WAAA,GAAA;AAAA,MACA,CAAA,EAAA,QAAA,MAAA;AACA,qBAAA,QAAA;AAAA,MACA,CAAA;AAAA,IACA;AAKA,UAAA,YAAA,CAAA,MAAA;AACA,YAAA,EAAA,IAAA,OAAA,IAAA;AAEAA,oBAAA,MAAA,MAAA,OAAA,iCAAA,UAAA,IAAA,MAAA;AAAA,IACA;AAKA,UAAA,aAAA,CAAA,MAAA;AACA,YAAA,EAAA,KAAA,KAAA,WAAA,UAAA,IAAA;AACAA,oBAAAA,MAAA,MAAA,OAAA,iCAAA,UAAA,EAAA,KAAA,KAAA,WAAA,UAAA,CAAA;AAGA,UAAA,KAAA,MAAA,GAAA,GAAA;AACA,aAAA,MAAA,GAAA,EAAA,YAAA;AACA,aAAA,MAAA,GAAA,EAAA,mBAAA;AAGA,YAAA,KAAA,MAAA,GAAA,EAAA,WAAA;AACA,eAAA,MAAA,GAAA,EAAA,UAAA,YAAA;AACA,eAAA,MAAA,GAAA,EAAA,UAAA,mBAAA;AAAA,QACA;AAAA,MACA;AAAA,IACA;AAEAO,kBAAAA,kBAAA,MAAA;AACAP,oBAAAA,MAAA,MAAA,OAAA,iCAAA,MAAA;AAGA,WAAA,QAAA;AACA,WAAA,QAAA;AACA,cAAA,QAAA;AACA,iBAAA,QAAA;AAGA,cAAA,IAAA;AAAA,QACA,YAAA;AAAA,QACA,gBAAA;AAAA,MACA,CAAA,EAAA,QAAA,MAAA;AACAA,sBAAA,MAAA,oBAAA;AAAA,MACA,CAAA;AAAA,IACA,CAAA;AAGAQ,kBAAAA,cAAA,MAAA;AACAR,oBAAAA,MAAA,MAAA,OAAA,iCAAA,gBAAA,KAAA,OAAA,SAAA,WAAA,KAAA;AAGA,UAAA,WAAA,UAAA,UAAA,CAAA,UAAA,OAAA;AACA;MACA;AAAA,IACA,CAAA;AACA,UAAA,WAAA,CAAA,MAAA;AAEA,UAAA,aAAA;AAAA;AAEA,YAAA,YAAA,SAAA,EAAA,cAAA,QAAA,GAAA;AAGA,UAAA,cAAA,OAAA;AAAA;AAEA,mBAAA,QAAA;AACA,aAAA,QAAA;AAEAA,oBAAAA,MAAA,MAAA,OAAA,iCAAA,SAAA,OAAA,OAAA,SAAA,OAAA,OAAA,WAAA,YAAA,KAAA;AAGA,YAAA,sBAAA,OAAA,UAAA,KAAA,OAAA,UAAA,YAAA;AACA,UAAA,uBAAA,SAAA,WAAA,SAAA,QAAA,SAAA,GAAA;AACAA,sBAAAA,MAAA,MAAA,OAAA,iCAAA,aAAA;AACA,gBAAA,QAAA;AACA,qBAAA,QAAA;AACA;AAAA,MACA;AAGAA,oBAAAA,MAAA,YAAA;AAAA,QACA,OAAA;AAAA,QACA,MAAA;AAAA,MACA,CAAA;AAGA,WAAA,QAAA;AACA,WAAA,QAAA;AACA,cAAA,QAAA;AACA,iBAAA,QAAA;AACA,kBAAA,QAAA;AAGA;AAGA,iBAAA,MAAA;AACA,qBAAA,QAAA;AACAA,sBAAA,MAAA,YAAA;AAAA,MACA,GAAA,GAAA;AAAA,IACA;AAEA,UAAA,cAAA,MAAA;AAEA;IACA;AAEA,UAAA,WAAA,CAAA,MAAA;AACA,UAAA,OAAA,EAAA,cAAA,QAAA;AAEA,UAAA,SAAA,QAAA,UAAA,GAAA;AACA,YAAA,MAAA;AACA,YAAA,QAAA,GAAA;AACA,gBAAA;AAAA,QACA;AACA,eAAA,YAAA,GAAA;AAAA,MACA;AAEAA,oBAAAA,MAAA,WAAA;AAAA,QACA,KAAA,+BAAA,OAAA,SAAA,SAAA,KAAA,WAAA,SAAA;AAAA,MACA,CAAA;AAAA,IACA;AAEA,UAAA,iBAAA,CAAA,SAAA;AACA,UAAA,CAAA,MAAA;AACA,kBAAA,MAAA,MAAA;AAAA,MACA;AACA,UAAA,MAAA;AACA,kBAAA,MAAA,KAAA;AAAA,MACA;AAAA,IACA;AAEA,UAAA,gBAAA,CAAA,MAAA;AACAA,oBAAAA,MAAA,WAAA;AAAA,QACA,KAAA,YAAA,EAAA,cAAA,QAAA;AAAA,MACA,CAAA;AAAA,IACA;AAEA,UAAA,UAAA,MAAA;AACA,UAAA,gBAAA,EAAA,SAAA,GAAA;AACAA,sBAAAA,MAAA,aAAA;AAAA,MACA,OAAA;AACAA,sBAAAA,MAAA,UAAA;AAAA,UACA,KAAA;AAAA,QACA,CAAA;AAAA,MACA;AAAA,IACA;AAEA,UAAA,cAAA,CAAA,KAAA,SAAA;AACA,gBAAA,QAAA;AACA,gBAAA,MAAA,KAAA;AAEA,iBAAA,WAAA;AACA,kBAAA,MAAA,MAAA;AACA,YAAA,MAAA;AACA,kBAAA;AAAA,QACA;AAAA,MACA,GAAA,GAAA;AAAA,IACA;AACA,UAAA,qBAAA,CAAA,UAAA;AACAA,oBAAAA,MAAA,sBAAA;AAAA,QACA,YAAA;AAAA,QACA,iBAAA;AAAA,QACA,WAAA;AAAA,UACA,UAAA;AAAA,UACA,YAAA;AAAA,QACA;AAAA,MACA,CAAA;AAAA,IACA;AAEA,UAAA,mBAAA,CAAA,UAAA;AACA,qBAAA,QAAA;AAEA,oBAAA;AAAA,IACA;AACA,UAAA,yBAAA,MAAA;AACA,UAAA;AACAA,sBAAA,MAAA,MAAA,OAAA,iCAAA,eAAA,SAAA,eAAA;AAGA,yBAAA,QAAA;AAGA,YAAA,SAAA,iBAAA;AACA,cAAA,WAAA,CAAA;AAGA,cAAA,OAAA,SAAA,oBAAA,UAAA;AACA,gBAAA;AAEA,kBAAA,SAAA,gBAAA,WAAA,MAAA,SAAA,gBAAA,KAAA,MAAA,MAAA;AACA,2BAAA,CAAA;AAAA,cACA,OAAA;AACA,2BAAA,KAAA,MAAA,SAAA,eAAA;AACAA,8BAAA,MAAA,MAAA,OAAA,iCAAA,iBAAA,QAAA;AAAA,cACA;AAAA,YACA,SAAA,OAAA;AACAA,4BAAA,MAAA,MAAA,SAAA,iCAAA,iBAAA,KAAA;AAEA,kBAAA,SAAA,gBAAA,KAAA,EAAA,WAAA,MAAA,GAAA;AACA,2BAAA,CAAA,EAAA,KAAA,SAAA,gBAAA,CAAA;AAAA,cACA;AAAA,YACA;AAAA,UACA,WAEA,MAAA,QAAA,SAAA,eAAA,GAAA;AACA,uBAAA,SAAA;AAAA,UACA;AAGA,2BAAA,QAAA,SAAA,OAAA,UAAA,QAAA,KAAA,OAAA,KAAA,IAAA,KAAA,MAAA,EAAA;AAAA,QACA;AAGA,YAAA,iBAAA,MAAA,WAAA,GAAA;AACA,gBAAA,YAAA,SAAA,UAAA;AACA,2BAAA,QAAA,CAAA,EAAA,KAAA,UAAA,CAAA;AAAA,QACA;AAEAA,sBAAA,MAAA,MAAA,OAAA,iCAAA,eAAA,iBAAA,KAAA;AAGA,uBAAA,QAAA;AAGA,YAAA,iBAAA,MAAA,SAAA,GAAA;AACA;QACA,OAAA;AACA;QACA;AAAA,MACA,SAAA,OAAA;AACAA,sBAAA,MAAA,MAAA,SAAA,iCAAA,eAAA,KAAA;AAEA,yBAAA,QAAA,CAAA,EAAA,KAAA,SAAA,UAAA,6BAAA,CAAA;AACA,uBAAA,QAAA;AACA;MACA;AAAA,IACA;AAGA,UAAA,gBAAA,MAAA;AACA;AAGA,UAAA,iBAAA,MAAA,SAAA,GAAA;AACA,sBAAA,QAAA,YAAA,MAAA;AACA;QACA,GAAA,GAAA;AAAA,MACA;AAAA,IACA;AAGA,UAAA,qBAAA,MAAA;AACA,UAAA,cAAA,OAAA;AACA,sBAAA,cAAA,KAAA;AACA,sBAAA,QAAA;AAAA,MACA;AAAA,IACA;AAGA,UAAA,iBAAA,MAAA;AACA,UAAA,iBAAA,MAAA,SAAA,GAAA;AACA,uBAAA,SAAA,eAAA,QAAA,KAAA,iBAAA,MAAA;AAAA,MACA;AAAA,IACA;AAIA,UAAA,YAAA,MAAA;AACA,eAAA,QAAA;AACA,mBAAA,QAAA;AACA,WAAA,QAAA;AACA,WAAA,QAAA;AACA,cAAA,QAAA;AACA,iBAAA,QAAA;AAGA;AACA;IACA;AAoBAS,kBAAA,aAAA,CAAA,MAAA;AACA,UAAA,aAAA;AACA,UAAA,SAAA,EAAA,YAAA,MAAA,MAAA,EAAA,aAAA;AAEA,UAAA,SAAA,GAAA;AACA,qBAAA;AAAA,MACA;AAEA,kBAAA,QAAA;AACA,yBAAA,UAAA;AAAA,IACA,CAAA;AAGAC,kBAAAA,kBAAA,MAAA;AACA,aAAA;AAAA,QACA,OAAA,SAAA,OAAA;AAAA,QACA,UAAA,SAAA;AAAA,QACA,MAAA,4BAAA,SAAA;AAAA,MACA;AAAA,IACA,CAAA;AAGAC,kBAAAA,gBAAA,MAAA;AACA,aAAA;AAAA,QACA,OAAA,SAAA,OAAA;AAAA,QACA,UAAA,SAAA;AAAA,QACA,OAAA,QAAA,SAAA;AAAA,MACA;AAAA,IACA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC1+BA,GAAG,WAAWC,SAAe;"}