
  ;(function(){
  let u=void 0,isReady=false,onReadyCallbacks=[],isServiceReady=false,onServiceReadyCallbacks=[];
  const __uniConfig = {"pages":[],"globalStyle":{"backgroundColor":"#f8f8f8","navigationBar":{"backgroundColor":"#ffffff","titleText":"ViBe慢社交软件","style":"custom","type":"default","titleColor":"#000000"},"isNVue":false},"nvue":{"compiler":"uni-app","styleCompiler":"weex","flex-direction":"column"},"renderer":"auto","appname":"Zing!","splashscreen":{"alwaysShowBeforeRender":false,"autoclose":true},"compilerVersion":"4.75","entryPagePath":"pages/index/index","entryPageQuery":"","realEntryPagePath":"","networkTimeout":{"request":60000,"connectSocket":60000,"uploadFile":60000,"downloadFile":60000},"locales":{},"darkmode":false,"themeConfig":{},"qqMapKey":"F7LBZ-NLU6D-6524Z-PK6ZQ-D47AJ-KRB2I"};
  const __uniRoutes = [{"path":"pages/index/index","meta":{"isQuit":true,"isEntry":true,"enablePullDownRefresh":true,"backgroundColorTop":"#ffffff","backgroundColorBottom":"#ffffff","navigationBar":{"titleText":"首页","style":"custom","type":"default","titleColor":"#000000"},"isNVue":false}},{"path":"pages/note/circlemember","meta":{"navigationBar":{"backgroundColor":"#FFFFFF","titleText":"圈子成员","style":"default","type":"default"},"isNVue":false}},{"path":"pages/index/shop","meta":{"enablePullDownRefresh":true,"backgroundColorTop":"#ffffff","backgroundColorBottom":"#ffffff","navigationBar":{"titleText":"商城","style":"custom","type":"default","titleColor":"#000000"},"isNVue":false}},{"path":"pages/index/dynamic","meta":{"enablePullDownRefresh":true,"backgroundColorTop":"#ffffff","backgroundColorBottom":"#ffffff","navigationBar":{"titleText":"动态","style":"custom","type":"default","titleColor":"#000000"},"isNVue":false}},{"path":"pages/index/message","meta":{"enablePullDownRefresh":true,"backgroundColorTop":"#ffffff","backgroundColorBottom":"#ffffff","navigationBar":{"titleText":"消息","style":"custom","type":"default","titleColor":"#000000"},"isNVue":false}},{"path":"pages/index/center","meta":{"enablePullDownRefresh":true,"backgroundColorTop":"#ffffff","backgroundColorBottom":"#ffffff","navigationBar":{"titleText":"个人中心","style":"custom","type":"default","titleColor":"#000000"},"isNVue":false}},{"path":"pages/activity/index","meta":{"enablePullDownRefresh":true,"navigationBar":{"titleText":"活动列表","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/activity/details","meta":{"navigationBar":{"titleText":"活动详情","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/search/index","meta":{"navigationBar":{"titleText":"搜索","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/user/details","meta":{"navigationBar":{"titleText":"用户详情","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/note/add","meta":{"navigationBar":{"titleText":"发布笔记","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/note/details","meta":{"navigationBar":{"titleText":"笔记详情","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/note/video","meta":{"navigationBar":{"titleText":"视频播放","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/note/circle","meta":{"navigationBar":{"titleText":"圈子详情","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/note/manghe","meta":{"navigationBar":{"titleText":"树洞盲盒","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/note/publish-tree-hole-simple","meta":{"navigationBar":{"titleText":"发布纸条","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/note/my-tree-hole","meta":{"navigationBar":{"titleText":"我的纸条","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/note/detail","meta":{"navigationBar":{"titleText":"纸条详情","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/users/user_sgin_list/index","meta":{"enablePullDownRefresh":true,"navigationBar":{"backgroundColor":"#FFFFFF","titleText":"签到记录","style":"default","type":"default"},"isNVue":false}},{"path":"pages/users/user_sgin/index","meta":{"navigationBar":{"titleText":"签到","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/goods/classify","meta":{"navigationBar":{"titleText":"商品分类","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/goods/cart","meta":{"navigationBar":{"titleText":"购物车","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/goods/details","meta":{"navigationBar":{"titleText":"商品详情","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/goods/evaluate","meta":{"navigationBar":{"titleText":"商品评价","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/order/settlement","meta":{"navigationBar":{"titleText":"订单结算","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/order/index","meta":{"enablePullDownRefresh":true,"navigationBar":{"titleText":"我的订单","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/order/details","meta":{"navigationBar":{"titleText":"订单详情","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/center/card","meta":{"navigationBar":{"titleText":"我的卡券","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/center/means","meta":{"navigationBar":{"backgroundColor":"#FFFFFF","titleText":"编辑资料","style":"default","type":"default"},"isNVue":false}},{"path":"pages/center/circle","meta":{"enablePullDownRefresh":true,"navigationBar":{"titleText":"我的圈子","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/center/follow","meta":{"enablePullDownRefresh":true,"navigationBar":{"titleText":"关注/粉丝","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/center/address","meta":{"navigationBar":{"titleText":"地址管理","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/setting/privacy","meta":{"navigationBar":{"titleText":"隐私设置","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/setting/index","meta":{"navigationBar":{"backgroundColor":"#FFFFFF","titleText":"设置","style":"default","type":"default"},"isNVue":false}},{"path":"pages/setting/logout","meta":{"navigationBar":{"backgroundColor":"#FFFFFF","titleText":"账号注销","style":"default","type":"default"},"isNVue":false}},{"path":"pages/setting/xinxuan","meta":{"navigationBar":{"backgroundColor":"#FFFFFF","titleText":"","style":"default","type":"default"},"isNVue":false}},{"path":"pages/setting/webview","meta":{"navigationBar":{"titleText":"网页","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/users/wechat_login/index","meta":{"navigationBar":{"titleText":"微信登录","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/users/login/index","meta":{"navigationBar":{"titleText":"登录","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/users/user_pwd_edit/index","meta":{"navigationBar":{"backgroundColor":"#FFFFFF","titleText":"修改密码","style":"default","type":"default"},"isNVue":false}},{"path":"pages/users/user_phone/index","meta":{"navigationBar":{"backgroundColor":"#FFFFFF","titleText":"绑定手机号","style":"default","type":"default"},"isNVue":false}},{"path":"pages/users/binding_phone/index","meta":{"navigationBar":{"backgroundColor":"#FFFFFF","titleText":"绑定手机","style":"default","type":"default"},"isNVue":false}},{"path":"pages/center/circle-create","meta":{"navigationBar":{"backgroundColor":"#FFFFFF","titleText":"创建圈子","style":"default","type":"default"},"isNVue":false}},{"path":"pages/setting/realname","meta":{"navigationBar":{"backgroundColor":"#FFFFFF","titleText":"实名认证","style":"default","type":"default"},"isNVue":false}},{"path":"pages/users/user_integral/index","meta":{"enablePullDownRefresh":true,"navigationBar":{"titleText":"我的积分","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/topic/index","meta":{"enablePullDownRefresh":true,"navigationBar":{"titleText":"话题列表","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/topic/details","meta":{"enablePullDownRefresh":true,"navigationBar":{"titleText":"话题详情","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/center/visitor","meta":{"enablePullDownRefresh":true,"navigationBar":{"backgroundColor":"#FFFFFF","titleText":"访客记录","style":"default","type":"default"},"isNVue":false}},{"path":"pages/users/privacy/index","meta":{"navigationBar":{"titleText":"隐私政策","style":"custom","type":"default"},"isNVue":false}}].map(uniRoute=>(uniRoute.meta.route=uniRoute.path,__uniConfig.pages.push(uniRoute.path),uniRoute.path='/'+uniRoute.path,uniRoute));
  __uniConfig.styles=[];//styles
  __uniConfig.onReady=function(callback){if(__uniConfig.ready){callback()}else{onReadyCallbacks.push(callback)}};Object.defineProperty(__uniConfig,"ready",{get:function(){return isReady},set:function(val){isReady=val;if(!isReady){return}const callbacks=onReadyCallbacks.slice(0);onReadyCallbacks.length=0;callbacks.forEach(function(callback){callback()})}});
  __uniConfig.onServiceReady=function(callback){if(__uniConfig.serviceReady){callback()}else{onServiceReadyCallbacks.push(callback)}};Object.defineProperty(__uniConfig,"serviceReady",{get:function(){return isServiceReady},set:function(val){isServiceReady=val;if(!isServiceReady){return}const callbacks=onServiceReadyCallbacks.slice(0);onServiceReadyCallbacks.length=0;callbacks.forEach(function(callback){callback()})}});
  service.register("uni-app-config",{create(a,b,c){if(!__uniConfig.viewport){var d=b.weex.config.env.scale,e=b.weex.config.env.deviceWidth,f=Math.ceil(e/d);Object.assign(__uniConfig,{viewport:f,defaultFontSize:16})}return{instance:{__uniConfig:__uniConfig,__uniRoutes:__uniRoutes,global:u,window:u,document:u,frames:u,self:u,location:u,navigator:u,localStorage:u,history:u,Caches:u,screen:u,alert:u,confirm:u,prompt:u,fetch:u,XMLHttpRequest:u,WebSocket:u,webkit:u,print:u}}}}); 
  })();
  