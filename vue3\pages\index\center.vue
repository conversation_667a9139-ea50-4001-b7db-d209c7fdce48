<template>
  <view class="container" :class="{'no-scroll': showSidebar}">
    <view class="nav-box" :style="{'padding-top': statusBarHeight + 'px', 'background': 'rgba(255,255,255,'+ navbarTrans +')'}">
      <view class="nav-item df" :style="{'height': titleBarHeight + 'px'}">
        <view class="nav-menu-btn" @tap="toggleSidebar">
          <image src="/static/img/menu.png"></image>
        </view>
        <view class="ohto df" :style="{'transform': 'translateY('+ ((1-navbarTrans) * 30) +'px)', 'opacity': navbarTrans}">
          <image :src="userInfo.avatar" mode="aspectFill" class="nav-user-avatar"></image>
          <text>{{userInfo.nickname}}</text>
        </view>
      </view>
    </view>
    <view class="user-box" :style="{'padding-top': statusBarHeight + titleBarHeight + 'px'}">
      <view class="user-bg"></view>
      <view class="user-img" style="z-index:-2">
        <lazy-image :src="userInfo.avatar || '/static/img/avatar.png'" mode="aspectFill"></lazy-image>
      </view>
      <view class="user-top df" data-url="center/means" @tap="navigateToFun" style="position: relative; padding-right: 40rpx; padding-top: 40rpx;">
        <view class="avatar-wrapper">
          <view class="avatar">
            <lazy-image :src="userInfo.avatar"></lazy-image>

          </view>
        </view>
        <view class="user-info">
          <view class="user-name-row df">
            <text class="user-name-text">{{userInfo.nickname}}</text>
            <view class="status-icon vip-icon" v-if="userInfo.is_money_level> 0 && userInfo.svip_open">
              <image src="/static/img/svip.gif"></image>
            </view>
            <view class="status-icon verified-icon" v-if="userInfo.is_verified">
              <image src="/static/img/rz.png"></image>
            </view>
          </view>
          <view class="user-id-row df">
            <view v-if="userInfo.sex != 2" class="sex-icon df">
              <image :src="userInfo.sex == 1 ? '/static/img/nan.png' : '/static/img/nv.png'"></image>
            </view>
            <text class="user-id">ID: {{userInfo.user_id_number}}</text>
          </view>
        </view>
        <view class="right-arrow">
          <image src="/static/img/x.png" mode="aspectFit"></image>
        </view>
      </view>

      <view class="user-num-wrap df">
        <view class="user-num df">
          <view class="num-item df" @tap="toFollowList(0)">
            <text class="t1">{{formattedFollowCount}}</text>
            <text class="t2">关注</text>
          </view>
          <view class="num-item df" @tap="toFollowList(1)">
            <text class="t1">{{formattedFansCount}}</text>
            <text class="t2">粉丝</text>
          </view>
          <view class="num-item df" @tap="likePopupClick(true)">
            <text class="t1">{{formattedLikeCount}}</text>
            <text class="t2">获赞</text>
          </view>
          <view class="num-item df visitor-item" data-url="center/visitor" @tap="navigateToFun">
            <text class="t1">{{formattedVisitorCount}}</text>
            <text class="t2">访客</text>
            <view v-if="userInfo.visitor_badge && userInfo.visitor_badge > 0" class="badge">
              +{{userInfo.visitor_badge > 99 ? '99+' : userInfo.visitor_badge}}
            </view>
          </view>
        </view>
      </view>
      <view class="tag-wrapper">
        <view v-if="userInfo.interest_tags && userInfo.interest_tags.length" class="user-tag df">
          <view v-for="(tag, index) in userInfo.interest_tags" :key="index" class="tag-item df">
            <text>{{tag}}</text>
          </view>
        </view>
        <view v-else class="tag-empty">
          <text>添加兴趣标签，让大家更了解你</text>
        </view>
        <view class="user-actions df">
          <view class="btn-icon" data-url="setting/index" @tap="navigateToFun">
            <image src="/static/img/setting/104.png"></image>
          </view>
          <!-- 调试按钮，仅开发环境显示 -->
          <view v-if="isDev" class="btn-icon debug-btn" @tap="debugLoadData" style="margin-left: 10rpx; background: #ff6b6b;">
            <text style="color: white; font-size: 20rpx;">刷新</text>
          </view>
        </view>
      </view>
      <view class="user-intro" data-url="center/means" @tap="navigateToFun">
        <text class="intro-text" user-select="true">{{userInfo.about_me ? userInfo.about_me : "添加个人简介，让大家认识你..."}}</text>
        <view class="more-btn">
          <image src="/static/img/more.png" mode="aspectFit"></image>
        </view>
      </view>
      

    </view>
    <scroll-view scroll-x="true" class="user-block">
      <view class="block-box">
        <view v-if="userInfo.activity_count" class="block-item df" data-url="activity/index?type=1" @tap="navigateToFun">
          <view class="block-title" style="margin-right:68rpx">
            <view class="t1">活动</view>
            <view class="t2">{{userInfo.activity_count ? '共' + userInfo.activity_count + '个活动' : '没有参加活动'}}</view>
          </view>
          <view class="cu-group df">
            <view class="cu-item" :style="{'background': userInfo.activity_img ? '#CECECE' : '#000'}">
              <image v-if="!userInfo.activity_img" class="icon" src="/static/img/hd.png"></image>
              <image v-else class="img" :src="userInfo.activity_img" mode="aspectFill"></image>
            </view>
            <view class="cu-lump1"></view>
            <view class="cu-lump2"></view>
          </view>
          <image class="block-icon" src="/static/img/x.png"></image>
        </view>
        <view v-for="(item, index) in blockList" :key="index" class="block-item df" :data-url="item.url" @tap="navigateToFun">
          <view class="block-title" style="margin-right:68rpx">
            <view class="t1">{{item.name}}</view>
            <view v-if="item.count" class="t2">
              共{{item.count}}{{index == 0 ? '个圈子' : index == 1 ? '件商品' : '笔订单'}}
            </view>
            <view v-else class="t2">
              {{index == 0 ? '没有加入圈子' : index == 1 ? '购物车空空的' : '订单空空的'}}
            </view>
          </view>
          <view class="cu-group df">
            <view class="cu-item" :style="{'background': item.img ? '#CECECE' : '#000'}">
              <image v-if="!item.img" class="icon" :src="item.icon"></image>
              <image v-else class="img" :src="item.img" mode="aspectFill"></image>
            </view>
            <view class="cu-lump1"></view>
            <view class="cu-lump2"></view>
          </view>
          <image class="block-icon" src="/static/img/x.png"></image>
        </view>
        <view v-if="appCard || userInfo.card_count" class="block-item df" data-url="center/card?type=1" @tap="navigateToFun">
          <view class="block-title" style="margin-right:68rpx">
            <view class="t1">卡券</view>
            <view class="t2">{{userInfo.card_count ? '共' + userInfo.card_count + '张卡券' : '暂无可用卡券'}}</view>
          </view>
          <view class="cu-group df">
            <view class="cu-item" style="background:#000">
              <image class="icon" src="/static/img/kq.png"></image>
            </view>
            <view class="cu-lump1"></view>
            <view class="cu-lump2"></view>
          </view>
          <image class="block-icon" src="/static/img/x.png"></image>
        </view>
        <view style="flex-shrink:0;width:15rpx;height:15rpx"></view>
      </view>
    </scroll-view>
    <view class="bar-box df" :style="{'top': statusBarHeight + titleBarHeight - 1 + 'px'}">
      <view v-for="(item, index) in barList" :key="index" class="bar-item df" @tap="barClick" :data-idx="index">
        <text :style="{'color': index == barIdx ? '#000' : '#999', 'font-size': index == barIdx ? '28rpx' : '26rpx'}">{{item}}</text>
        <view :style="{'opacity': index == barIdx ? 1 : 0}" class="bar-line"></view>
      </view>
    </view>
    <!-- 优化的内容区域 -->
    <view class="content-container">
      <!-- 首次加载状态 -->
      <view v-if="loading.refreshing && list.length === 0" class="loading-state df">
        <uni-load-more :status="'loading'"></uni-load-more>
        <view class="loading-text">正在加载内容...</view>
      </view>

      <!-- 空状态 -->
      <view v-else-if="isEmpty && !loading.dynamicList" class="empty-state df">
        <emptyPage
          :title="emptyTitle"
          :description="emptySubtitle"
          :image="emptyImage"
        />
      </view>

      <!-- 内容列表 -->
      <template v-else>
        <!-- 使用虚拟列表优化长列表渲染 -->
        <view class="dynamic-list">
          <template v-for="(item, index) in list" :key="item.id || `${barIdx}-${index}`">
            <card-gg
              v-if="barIdx == 1"
              :item="item"
              :idx="index"
              :lazy="index > 5"
            />
            <card-wd
              v-else
              :item="item"
              :idx="index"
              :bar="barIdx"
              :lazy="index > 5"
              @delback="delClick"
            />
          </template>
        </view>

        <!-- 底部加载更多状态 -->
        <view class="load-more-container">
          <uni-load-more
            v-if="list.length > 0"
            :status="loadStatus"
            :content-text="{
              contentdown: '上拉加载更多',
              contentrefresh: '正在加载...',
              contentnomore: isLogin ? '没有更多内容了' : '登录后查看更多'
            }"
          />
        </view>
      </template>
    </view>
    <uni-popup ref="likePopup" class="r">
      <view class="like-popup">
        <image class="like-img" src="/static/img/inset/like.png" mode="aspectFill"></image>
        <view class="like-content"><text>"</text>{{userInfo.nickname}}<text>"</text>共获得 {{userInfo.like_count || 0}} 个赞 </view>
        <view class="like-btn" @tap="likePopupClick(false)">确认</view>
      </view>
    </uni-popup>
    <tabbar :currentPage="4" :currentMsg="currentMsg" :userAvatar="userInfo.avatar"></tabbar>
    <view class="sidebar-menu" :class="{'active': showSidebar}">
      <view class="sidebar-header" :style="{'padding-top': statusBarHeight + 10 + 'px'}">
        <view class="sidebar-user-info">
          <image :src="userInfo.avatar || '/static/img/avatar.png'" mode="aspectFill" class="sidebar-avatar"></image>
          <view class="sidebar-user-details">
            <view class="sidebar-user-name">{{userInfo.nickname}}</view>
            <view class="user-status">
              <view class="status-item" v-if="userInfo.is_money_level> 0 && userInfo.svip_open">
                <image src="/static/img/svip.gif" class="status-icon"></image>
              </view>
              <view class="status-item verified-tag" v-if="userInfo.is_verified">
                <image src="/static/img/rz.png" class="status-icon"></image>
                <text>已认证</text>
              </view>
            </view>
          </view>
        </view>
        <view class="close-btn df" @tap="toggleSidebar">
          <image src="/static/img/tabbar/3.png" style="width:20rpx;height:20rpx;"></image>
        </view>
      </view>
      <view class="member-card">
        <view class="member-status">
          <view v-if="userInfo.vip_status == 1" class="member-label">永久会员</view>
          <view v-else-if="userInfo.vip_status == 3" class="member-label">
            会员到期：{{ formatDate(userInfo.overdue_time) }}
          </view>
          <view v-else-if="userInfo.vip_status == -1" class="member-label">会员已过期</view>
          <view v-else-if="userInfo.vip_status == 2" class="member-label">未开通会员</view>
          <view v-if="userInfo.vip_status != 1" class="member-price" @tap="goToVipPage">¥3.8续费</view>
        </view>
        <view class="member-benefits">
          <text class="member-rights" @tap="goToVipPage">会员权益 | 领取我的等级特权</text>
        </view>
        <view class="member-desc">专属优惠，VIP低至¥88，畅听1年！</view>
      </view>
      <scroll-view scroll-y class="sidebar-scroll">
        <view class="sidebar-content">
          <view v-if="sidebarMenu && sidebarMenu.length > 0" class="menu-section">
            <view class="section-title">我的服务</view>
            <view class="menu-grid">
            <view
                v-for="(item, index) in sidebarMenu"
                :key="'menu-' + index"
                class="grid-item"
              :data-url="item.url"
              @tap="navigateToFun">
                <view class="grid-icon-wrapper">
                  <image :src="item.icon" class="grid-icon"></image>
                  <view v-if="item.badge" class="grid-badge">{{item.badge}}</view>
              </view>
                <text class="grid-text">{{item.name}}</text>
              </view>
            </view>
          </view>
          <!-- 当没有菜单数据时显示提示 -->
          <view v-else class="menu-section">
            <view class="section-title">我的服务</view>
            <view class="empty-menu-tip">
              <text>暂无可用服务</text>
            </view>
          </view>
        </view>
      </scroll-view>
      <view class="sidebar-footer">
        <view class="bottom-nav df">
          <view class="bottom-nav-item df" @tap="handleBottomNav('scan')">
            <view class="nav-icon-box df">
              <image src="/static/img/scan.png" class="nav-icon"></image>
            </view>
            <text class="nav-text">扫一扫</text>
          </view>
          <view class="bottom-nav-item df" @tap="handleBottomNav('help')">
            <view class="nav-icon-box df">
              <image src="/static/img/kf.png" class="nav-icon"></image>
            </view>
            <text class="nav-text">帮助与客服</text>
          </view>
          <view class="bottom-nav-item df" @tap="handleBottomNav('setting')">
            <view class="nav-icon-box df">
              <image src="/static/img/setting/104.png" class="nav-icon"></image>
            </view>
            <text class="nav-text">设置</text>
          </view>
        </view>
        <view class="copyright-text">© {{new Date().getFullYear()}} 个人中心</view>
      </view>
    </view>
    <view class="sidebar-mask" v-if="showSidebar" @tap="toggleSidebar" @touchmove.stop.prevent @touchstart.stop @touchend.stop></view>
  </view>
</template>

<script>
import lazyImage from '@/components/lazyImage/lazyImage'
import uniLoadMore from '@/uni_modules/uni-load-more/components/uni-load-more/uni-load-more'
import cardWd from '@/components/card-wd/card-wd'
import cardGg from '@/components/card-gg/card-gg'
import tabbar from '@/components/tabbar/tabbar'
import uniPopup from '@/uni_modules/uni-popup/components/uni-popup/uni-popup.vue'
import emptyPage from '@/components/emptyPage/emptyPage.vue'
import { getMenuList, setVisit } from '@/api/user.js'
import { getMyDynamicList, deleteDynamic, getUserSocialInfo, getLikeDynamicList, getVisitorDetails } from '@/api/social.js'
import { toLogin } from '@/libs/login.js'
// 引入Pinia stores
import { useUserStore } from '@/stores/user.js'
import { useAppStore } from '@/stores/app.js'
import { useSocialStore } from '@/stores/social.js'

export default {
  components: {
    lazyImage,
    uniLoadMore,
    cardWd,
    cardGg,
    tabbar,
    uniPopup,
    emptyPage
  },
  computed: {
    isLogin() {
      return this.userStore.isLoggedIn;
    },

    // 空状态相关计算属性
    emptyTitle() {
      const titles = ['暂无笔记内容', '暂无喜欢的内容'];
      return titles[this.barIdx] || '暂无内容';
    },

    emptySubtitle() {
      if (this.barIdx === 1) {
        return '快在推荐中寻找更多笔记吧';
      }
      return '发笔记，记录灵感日常';
    },

    emptyImage() {
      const images = ['/static/img/empty-notes.png', '/static/img/empty-likes.png'];
      return images[this.barIdx] || '/static/img/empty.png';
    },

    // 格式化数字显示的计算属性
    formattedFollowCount() {
      const count = this.userInfo.follow_count || 0;
      if (count >= 10000) {
        return (count / 10000).toFixed(1) + 'w';
      } else if (count >= 1000) {
        return (count / 1000).toFixed(1) + 'k';
      }
      return count.toString();
    },

    formattedFansCount() {
      const count = this.userInfo.fans_count || 0;
      if (count >= 10000) {
        return (count / 10000).toFixed(1) + 'w';
      } else if (count >= 1000) {
        return (count / 1000).toFixed(1) + 'k';
      }
      return count.toString();
    },

    formattedLikeCount() {
      const count = this.userInfo.like_count || 0;
      if (count >= 10000) {
        return (count / 10000).toFixed(1) + 'w';
      } else if (count >= 1000) {
        return (count / 1000).toFixed(1) + 'k';
      }
      return count.toString();
    },

    formattedVisitorCount() {
      const count = this.userInfo.visitor_count || 0;
      if (count >= 10000) {
        return (count / 10000).toFixed(1) + 'w';
      } else if (count >= 1000) {
        return (count / 1000).toFixed(1) + 'k';
      }
      return count.toString();
    },

    // 开发环境判断
    isDev() {
      return process.env.NODE_ENV === 'development';
    }
  },
  data() {
    return {
      // Pinia stores实例
      userStore: useUserStore(),
      appStore: useAppStore(),
      socialStore: useSocialStore(),

      statusBarHeight: 20,
      titleBarHeight: 44,
      currentMsg: 0,
      scrollTop: 0,
      navbarTrans: 0,
      userInfo: {
        avatar: "",
        nickname: "您还未登录哦~",
        follow_count: 0,
        fans_count: 0,
        like_count: 0,
        like_count_str: "0",
        visitor_count: 0,
        visitor_badge: 0,
        is_verified: 0,
        is_money_level: 0,
        svip_open: 0,
        sex: 0,
        user_id_number: "",
        about_me: "",
        interest_tags: [],
        vip_status: 0,
        overdue_time: "",
        card_count: 0,
        activity_count: 0,
        activity_img: ""
      },
      blockList: [
        {name: '圈子', img: '', icon: '/static/img/qz.png', url: 'center/circle?type=1', count: 0},
        {name: '购物车', img: '', icon: '/static/img/gwc.png', url: 'goods/cart', count: 0},
        {name: '订单', img: '', icon: '/static/img/dd.png', url: 'order/index', count: 0}
      ],
      barList: ['笔记', '赞过'],
      barIdx: 0,
      list: [],
      page: 1,
      totalCount: 0,
      isEmpty: true, // 初始状态应该是空的
      loadStatus: 'more',
      showSidebar: false,
      sidebarMenu: [],
      isLoading: false,
      isThrottling: false,
      appCard: true,

      // 简化的加载状态管理
      loading: {
        userInfo: false,
        dynamicList: false,
        refreshing: false
      },

      // 简化的缓存管理
      lastRefreshTime: 0,
      cacheTimeout: 300000, // 5分钟缓存

      // 首次加载标记
      isFirstLoad: true,

      // 滚动优化相关
      throttledScrollHandler: null,
      lastNavColorState: false
    }
  },




  // 生命周期方法
  onPullDownRefresh() {
    if (!this.isLogin) {
      uni.stopPullDownRefresh();
      return;
    }
    this.refreshData();
  },

  onLoad() {
    // 初始化基础设置
    this.initBasicSettings();
    // 初始化数据
    this.initData();
  },

  onUnload() {
    uni.$off('userInfoUpdated', this.handleUserInfoUpdate);
    uni.$off('loginStateChanged', this.handleLoginStateChanged);
    // 清除所有定时器
    this.clearAllTimers();
  },

  onShow() {
    // 设置导航栏颜色
    uni.setNavigationBarColor({
      frontColor: '#ffffff',
      backgroundColor: 'transparent',
      animation: {duration: 300, timingFunc: 'easeIn'}
    });

    // 避免首次加载时的重复调用
    if (this.isFirstLoad) {
      this.isFirstLoad = false;
      return;
    }

    console.log('onShow - 页面显示:', {
      isLogin: this.isLogin,
      listLength: this.list.length,
      isEmpty: this.isEmpty,
      currentTab: this.barList[this.barIdx],
      userUid: this.userStore.uid,
      hasToken: !!this.userStore.token,
      isLoggedIn: this.userStore.isLoggedIn,
      storeIsLogin: this.userStore.isLogin,
      computedIsLogin: this.isLogin
    });

    // 检查是否需要刷新数据
    const isLoggedIn = this.isLogin && this.userStore.uid && this.userStore.token;

    if (isLoggedIn) {
      console.log('用户已登录，检查数据状态');

      // 临时：总是强制刷新数据（用于调试）
      if (this.isDev) {
        console.log('开发模式：强制刷新数据');
        this.page = 1;
        this.list = [];
        this.isEmpty = true;
        this.loadStatus = 'loading';
        this.lastRefreshTime = 0; // 清除缓存时间
        this.loadDynamicList();
        return;
      }

      // 如果当前没有数据，强制加载
      if (this.list.length === 0 && !this.loading.dynamicList) {
        console.log('检测到无数据，强制刷新');
        // 直接调用数据加载方法
        this.page = 1;
        this.list = [];
        this.isEmpty = true;
        this.loadStatus = 'loading';
        this.loadDynamicList();
      } else {
        // 检查是否需要刷新数据
        const now = Date.now();
        const shouldRefresh = (now - this.lastRefreshTime) > this.cacheTimeout;
        if (shouldRefresh) {
          console.log('数据过期，刷新数据');
          this.refreshData();
        } else {
          console.log('数据仍然有效，无需刷新');
        }
      }
    } else {
      console.log('用户未登录，显示空状态');
      this.list = [];
      this.isEmpty = true;
      this.loadStatus = 'more';
    }
  },

  // 优化的触底加载
  onReachBottom() {
    // 防止重复加载或已经加载完所有数据
    if (this.loading.dynamicList || this.loadStatus === 'noMore' || !this.isLogin) {
      return;
    }

    // 检查是否还有更多数据
    if (this.list.length < this.totalCount) {
      console.log('触底加载更多数据:', {
        currentPage: this.page,
        listLength: this.list.length,
        totalCount: this.totalCount
      });

      this.page++;
      this.loadStatus = 'loading';
      this.loadDynamicList();
    } else {
      // 确保状态正确
      this.loadStatus = 'noMore';
    }
  },

  // 优化的页面滚动处理
  onPageScroll(e) {
    // 使用节流优化滚动性能
    if (!this.throttledScrollHandler) {
      this.throttledScrollHandler = this.throttle(this.handlePageScroll, 16); // 60fps
    }
    this.throttledScrollHandler(e);
  },

  methods: {
    // 初始化基础设置
    initBasicSettings() {
      // 设置导航栏颜色
      uni.setNavigationBarColor({
        frontColor: '#ffffff',
        backgroundColor: 'transparent',
        animation: {duration: 300, timingFunc: 'easeIn'}
      });

      // 初始化Pinia stores（带错误处理）
      try {
        this.userStore.initFromStorage();
        this.appStore.initFromStorage();
      } catch (error) {
        console.warn('Pinia stores初始化失败:', error);
        // 如果初始化失败，使用默认值
        this.statusBarHeight = 20;
        this.titleBarHeight = 44;
      }

      // 设置系统信息
      this.statusBarHeight = this.appStore.systemInfo.statusBarHeight || 20;
      this.titleBarHeight = this.appStore.systemInfo.titleBarHeight || 44;

      // 注册事件监听
      uni.$on('loginStateChanged', this.handleLoginStateChanged);
      uni.$on('userInfoUpdated', this.handleUserInfoUpdate);
    },

    // 初始化数据
    async initData() {
      // 加载缓存的用户信息
      this.loadUserFromCache();

      // 调试：验证缓存数据一致性
      if (process.env.NODE_ENV === 'development') {
        this.validateCacheConsistency();
      }

      console.log('初始化数据:', {
        isLogin: this.isLogin,
        userUid: this.userStore.uid,
        hasToken: !!this.userStore.token
      });

      // 如果已登录，加载数据
      if (this.isLogin) {
        await this.loadData();
      } else {
        // 未登录时确保显示空状态
        this.list = [];
        this.isEmpty = true;
        this.loadStatus = 'more';
        console.log('未登录，设置空状态');
      }
    },

    // 优化的统一数据加载方法
    async loadData() {
      try {
        // 使用Promise.allSettled确保即使某个请求失败也不影响其他请求
        const results = await Promise.allSettled([
          this.loadUserInfo(),
          this.loadDynamicList()
        ]);

        // 检查加载结果
        results.forEach((result, index) => {
          if (result.status === 'rejected') {
            const taskName = index === 0 ? '用户信息' : '动态列表';
            console.warn(`${taskName}加载失败:`, result.reason);
          }
        });

        // 异步加载非关键数据
        this.loadNonCriticalData();

        this.lastRefreshTime = Date.now();
      } catch (error) {
        console.error('数据加载失败:', error);
        this.showErrorToast('加载失败，请稍后重试');
      }
    },

    // 新增：加载非关键数据
    async loadNonCriticalData() {
      try {
        // 并行加载侧边栏菜单和访问记录
        await Promise.allSettled([
          this.loadSidebarMenu(),
          this.setVisit()
        ]);
      } catch (error) {
        console.warn('非关键数据加载失败:', error);
      }
    },

    // 刷新数据方法
    async refreshData() {
      if (this.loading.refreshing) return;

      this.loading.refreshing = true;
      this.page = 1;

      try {
        await this.loadData();
      } catch (error) {
        console.error('刷新数据失败:', error);
      } finally {
        this.loading.refreshing = false;
        uni.stopPullDownRefresh();
      }
    },

    // 检查是否需要刷新数据
    shouldRefreshData() {
      const now = Date.now();
      return (now - this.lastRefreshTime) > this.cacheTimeout;
    },

    // 刷新数据
    async refreshData() {
      if (this.loading.refreshing) return;

      this.loading.refreshing = true;
      this.page = 1;

      try {
        await this.loadData();
      } catch (error) {
        console.error('刷新数据失败:', error);
      } finally {
        this.loading.refreshing = false;
        uni.stopPullDownRefresh();
      }
    },

    // 强制刷新当前标签数据
    async forceRefreshCurrentTab() {
      console.log('强制刷新当前标签:', this.barList[this.barIdx]);

      // 重置状态
      this.page = 1;
      this.list = [];
      this.isEmpty = true;
      this.loadStatus = 'loading';

      // 重新加载数据
      await this.loadDynamicList();
    },

    // 优化的用户信息加载
    async loadUserInfo() {
      if (this.loading.userInfo || !this.isLogin) return;

      this.loading.userInfo = true;

      try {
        // 先检查缓存是否有效
        const cachedUserInfo = this.userStore.userInfo;
        const now = Date.now();
        const cacheAge = now - (cachedUserInfo.lastUpdateTime || 0);

        // 如果缓存有效且数据完整，直接使用缓存
        if (cacheAge < this.cacheTimeout && cachedUserInfo.uid && cachedUserInfo.nickname) {
          console.log('使用缓存的用户信息');
          this.userInfo = { ...this.userInfo, ...cachedUserInfo };
          this.userClick();
          return;
        }

        const res = await getUserSocialInfo();

        if (res.status === 200 || res.code === 200) {
          const userData = res.data;

          // 优化数据处理
          this.processUserData(userData);

          // 统一通过Pinia更新用户信息，确保数据一致性
          userData.lastUpdateTime = now;
          this.userStore.updateUserInfo(userData);

          // 同步到本地userInfo（从Pinia获取，确保一致）
          this.userInfo = { ...this.userInfo, ...this.userStore.userInfo };
          this.currentMsg = userData.service_num || 0;

          // 更新界面
          this.userClick();
        } else {
          console.warn('获取用户信息失败:', res);
        }
      } catch (error) {
        console.error('加载用户信息失败:', error);
        if (!this.handleApiError(error)) {
          this.showErrorToast('加载用户信息失败');
        }
      } finally {
        this.loading.userInfo = false;
      }
    },

    // 新增：处理用户数据的方法
    processUserData(userData) {
      // 处理点赞数显示格式
      if (userData.like_count !== undefined) {
        userData.like_count_str = this.formatCountHelper(userData.like_count);
      }

      // 处理其他数字格式
      ['follow_count', 'fans_count', 'visitor_count'].forEach(field => {
        if (userData[field] !== undefined) {
          userData[`${field}_str`] = this.formatCountHelper(userData[field]);
        }
      });

      // 确保必要字段存在
      userData.interest_tags = userData.interest_tags || [];
      userData.about_me = userData.about_me || '';

      return userData;
    },

    // 新增：统一的数字格式化方法
    formatCountHelper(count) {
      if (count >= 10000) {
        return (count / 10000).toFixed(1) + 'w';
      } else if (count >= 1000) {
        return (count / 1000).toFixed(1) + 'k';
      }
      return count.toString();
    },

    // 新增：时间格式化方法
    formatTime(timestamp) {
      if (!timestamp) return '';

      const now = Date.now();
      const time = new Date(timestamp * 1000);
      const diff = now - time.getTime();

      // 1分钟内
      if (diff < 60000) {
        return '刚刚';
      }

      // 1小时内
      if (diff < 3600000) {
        return Math.floor(diff / 60000) + '分钟前';
      }

      // 24小时内
      if (diff < 86400000) {
        return Math.floor(diff / 3600000) + '小时前';
      }

      // 7天内
      if (diff < 604800000) {
        return Math.floor(diff / 86400000) + '天前';
      }

      // 超过7天显示具体日期
      return time.toLocaleDateString();
    },

    // 优化的动态列表加载
    async loadDynamicList() {
      const isLoggedIn = this.isLogin && this.userStore.uid && this.userStore.token;

      if (this.loading.dynamicList || !isLoggedIn) {
        console.log('loadDynamicList被阻止:', {
          loading: this.loading.dynamicList,
          isLogin: this.isLogin,
          isLoggedIn: isLoggedIn,
          uid: this.userStore.uid,
          hasToken: !!this.userStore.token
        });
        return;
      }

      this.loading.dynamicList = true;

      try {
        const userId = this.userStore.uid;
        const cacheType = 'personal';
        const subType = this.barIdx === 0 ? 'notes' : 'likes';

        console.log('开始加载动态列表:', {
          barIdx: this.barIdx,
          page: this.page,
          userId: userId,
          tabName: subType,
          cacheType: `${cacheType}.${subType}`
        });

        if (!userId) {
          throw new Error('用户ID无效');
        }

        // 首页加载时先尝试从缓存获取
        if (this.page === 1) {
          console.log('检查缓存:', { cacheType, subType });
          const cachedData = this.tryLoadFromCache(cacheType, subType);
          if (cachedData) {
            console.log('使用缓存数据，跳过API调用');
            return;
          } else {
            console.log('缓存无效或为空，继续API调用');
          }
        }

        // 构建API请求
        const apiParams = {
          page: this.page,
          limit: 10
        };

        let apiCall;
        if (this.barIdx === 0) {
          console.log('调用我的笔记API:', apiParams);
          apiCall = getMyDynamicList(apiParams);
        } else {
          console.log('调用点赞列表API:', { userId, ...apiParams });
          apiCall = getLikeDynamicList(userId, apiParams);
        }

        console.log('开始API调用...');
        const res = await apiCall;
        console.log('API响应:', res);

        if (res.status === 200 && res.data) {
          const newList = res.data.list || [];

          // 数据预处理
          const processedList = this.processDynamicList(newList);

          console.log('处理数据:', {
            newListLength: processedList.length,
            totalCount: res.data.count,
            currentPage: this.page
          });

          // 更新列表数据
          if (this.page === 1) {
            this.list = processedList;
            // 缓存首页数据到Pinia
            this.socialStore.setDynamicCache(cacheType, subType, processedList, res.data.count);
          } else {
            this.list.push(...processedList);
          }

          this.totalCount = res.data.count || 0;
          this.isEmpty = this.list.length === 0;
          this.loadStatus = newList.length < 10 ? 'noMore' : 'more';

          console.log('数据更新完成:', {
            listLength: this.list.length,
            isEmpty: this.isEmpty,
            loadStatus: this.loadStatus
          });
        } else {
          console.warn('API响应异常:', res);
          this.handleEmptyResponse();
        }
      } catch (error) {
        console.error('加载动态列表失败:', error);
        this.handleLoadError(error);
      } finally {
        this.loading.dynamicList = false;
      }
    },

    // 新增：尝试从缓存加载数据
    tryLoadFromCache(cacheType, subType) {
      console.log('检查缓存有效性:', { cacheType, subType });

      const isValid = this.socialStore.isCacheValid(cacheType, subType);
      console.log('缓存是否有效:', isValid);

      if (isValid) {
        const cache = this.socialStore.getDynamicCache(cacheType, subType);
        console.log('获取到的缓存数据:', cache);

        this.list = [...cache.data];
        this.totalCount = cache.totalCount;
        this.isEmpty = this.list.length === 0;
        this.loadStatus = this.list.length >= this.totalCount ? 'noMore' : 'more';

        console.log('从缓存加载数据:', {
          类型: `${cacheType}.${subType}`,
          数据量: this.list.length,
          缓存时间: new Date(cache.timestamp).toLocaleString()
        });

        return true;
      } else {
        console.log('缓存无效，原因可能是：过期、无数据或未初始化');
      }
      return false;
    },

    // 新增：处理动态列表数据
    processDynamicList(list) {
      return list.map(item => {
        // 确保必要字段存在
        item.content = item.content || '';
        item.images = item.images || [];
        item.like_count = item.like_count || 0;
        item.comment_count = item.comment_count || 0;

        // 处理时间格式
        if (item.create_time) {
          item.formatted_time = this.formatTime(item.create_time);
        }

        return item;
      });
    },

    // 新增：处理空响应
    handleEmptyResponse() {
      if (this.page === 1) {
        this.isEmpty = true;
        this.list = [];
      }
      this.loadStatus = 'more';
    },

    // 新增：处理加载错误
    handleLoadError(error) {
      if (this.page === 1) {
        this.isEmpty = true;
        this.list = [];
      }
      this.loadStatus = 'more';

      if (!this.handleApiError(error)) {
        this.showErrorToast('加载动态列表失败');
      }
    },

    // 优化的侧边栏菜单加载
    async loadSidebarMenu() {
      try {
        // 检查缓存
        const cachedMenu = uni.getStorageSync('SIDEBAR_MENU');
        const cacheTime = uni.getStorageSync('SIDEBAR_MENU_TIME');
        const now = Date.now();

        // 如果缓存有效（5分钟内），直接使用缓存
        if (cachedMenu && cacheTime && (now - cacheTime < 300000)) {
          this.sidebarMenu = cachedMenu;
          console.log('使用缓存的侧边栏菜单');
          return;
        }

        const res = await getMenuList();
        console.log('菜单接口返回数据:', res);

        if (res.status === 200 && res.data) {
          const processedMenu = this.processSidebarMenuData(res.data);
          this.sidebarMenu = processedMenu;

          // 缓存菜单数据
          try {
            uni.setStorageSync('SIDEBAR_MENU', processedMenu);
            uni.setStorageSync('SIDEBAR_MENU_TIME', now);
          } catch (error) {
            console.warn('缓存侧边栏菜单失败:', error);
          }

          console.log('侧边栏菜单加载成功:', this.sidebarMenu);
        }
      } catch (error) {
        console.warn('加载侧边栏菜单失败:', error);
        // 如果加载失败，尝试使用缓存
        const cachedMenu = uni.getStorageSync('SIDEBAR_MENU');
        if (cachedMenu) {
          this.sidebarMenu = cachedMenu;
          console.log('使用缓存的侧边栏菜单（降级）');
        }
      }
    },

    // 新增：处理侧边栏菜单数据
    processSidebarMenuData(data) {
      // 检查菜单状态是否启用
      const diyData = data.diy_data || {};
      const menuStatus = diyData.my_menus_status;

      console.log('菜单状态:', menuStatus);

      // 只有当菜单状态为启用(1)时才显示菜单
      if (menuStatus !== 1) {
        console.log('菜单状态未启用，不显示菜单');
        return [];
      }

      // 处理菜单数据，将接口返回的字段映射到模板需要的字段
      const menuData = data.routine_my_menus || [];
      return menuData.map(item => ({
        id: item.id,
        name: item.name || '未知',
        icon: item.pic || '/static/img/default_menu.png', // 提供默认图标
        url: item.url || '',
        badge: item.badge || null,
        sort: item.sort || 0 // 添加排序字段
      })).sort((a, b) => a.sort - b.sort); // 按排序字段排序
    },

    // 通用错误处理方法
    handleApiError(error) {
      console.error('API错误:', error);

      // 处理存储空间不足
      if (error.name === 'QuotaExceededError') {
        this.showErrorToast('存储空间不足，正在清理缓存...');
        this.userStore.clearStorageCache();
        return true;
      }

      // 处理网络错误
      if (error.code === 'NETWORK_ERROR') {
        this.showErrorToast('网络连接失败，请检查网络');
        return true;
      }

      // 处理401未授权
      if (error.status === 401 || error.statusCode === 401) {
        this.userStore.logout();
        this.showErrorToast('登录已过期，请重新登录');
        return true;
      }

      return false;
    },

    // 验证缓存数据一致性（调试用）
    validateCacheConsistency() {
      const localUserInfo = this.userInfo;
      const storeUserInfo = this.userStore.userInfo;
      const cachedUserInfo = uni.getStorageSync('USER_INFO');

      // 检查登录状态相关数据
      const loginStatusCheck = {
        computed_isLogin: this.isLogin,
        store_isLogin: this.userStore.isLogin,
        store_isLoggedIn: this.userStore.isLoggedIn,
        store_token: this.userStore.token ? '有token' : '无token',
        store_tokenLength: this.userStore.token?.length || 0,
        cache_token: uni.getStorageSync('token') ? '有缓存token' : '无缓存token',
        cache_LOGIN_STATUS_TOKEN: uni.getStorageSync('LOGIN_STATUS_TOKEN') ? '有旧token' : '无旧token'
      };

      console.log('登录状态详细检查:', loginStatusCheck);

      console.log('缓存数据一致性检查:', {
        local: {
          uid: localUserInfo.uid,
          nickname: localUserInfo.nickname,
          hasData: !!localUserInfo.uid
        },
        store: {
          uid: storeUserInfo.uid,
          nickname: storeUserInfo.nickname,
          hasData: !!storeUserInfo.uid
        },
        cache: {
          uid: cachedUserInfo?.uid,
          nickname: cachedUserInfo?.nickname,
          hasData: !!cachedUserInfo?.uid,
          type: typeof cachedUserInfo
        },
        isConsistent: localUserInfo.uid === storeUserInfo.uid &&
                     storeUserInfo.uid === cachedUserInfo?.uid
      });
    },

    // Pinia使用示例 - 缓存动态数据
    cacheDynamicDataToPinia() {
      if (this.list.length > 0) {
        const cacheType = 'personal'
        const subType = this.barIdx === 0 ? 'notes' : 'likes'

        // 缓存当前tab的数据到Pinia
        this.socialStore.setDynamicCache(cacheType, subType, this.list, this.totalCount)

        console.log('动态数据已缓存到Pinia:', {
          类型: `${cacheType}.${subType}`,
          数据量: this.list.length,
          总数: this.totalCount
        })
      }
    },

    // Pinia使用示例 - 从缓存加载数据
    loadDataFromPiniaCache() {
      const cacheType = 'personal'
      const subType = this.barIdx === 0 ? 'notes' : 'likes'

      // 检查缓存是否有效
      if (this.socialStore.isCacheValid(cacheType, subType)) {
        const cache = this.socialStore.getDynamicCache(cacheType, subType)

        this.list = [...cache.data]
        this.totalCount = cache.totalCount
        this.isEmpty = this.list.length === 0
        this.loadStatus = this.list.length >= this.totalCount ? 'noMore' : 'more'

        console.log('从Pinia缓存加载数据:', {
          类型: `${cacheType}.${subType}`,
          数据量: this.list.length,
          缓存时间: new Date(cache.timestamp).toLocaleString()
        })

        return true
      }

      return false
    },

    // 检查登录状态
    checkLoginStatus(redirectToLogin = false) {
      const isLoggedIn = this.isLogin && this.userStore.token;

      // #ifdef H5 || APP-PLUS
      if (!isLoggedIn && redirectToLogin) {
        toLogin();
        return false;
      }
      // #endif

      return isLoggedIn;
    },
    
    // 清除定时器
    clearAllTimers() {
      if (this.refreshTimer) {
        clearTimeout(this.refreshTimer);
        this.refreshTimer = null;
      }
    },





    showErrorToast(message, duration = 2000) {
      uni.showToast({
        title: message,
        icon: 'none',
        duration: duration
      });
    },
    
    loadUserFromCache() {
      try {
        // 优先从Pinia store获取用户信息
        if (this.userStore.userInfo && this.userStore.userInfo.uid) {
          this.userInfo = { ...this.userInfo, ...this.userStore.userInfo };
          this.userClick();
          return;
        }

        // 如果Pinia中没有，再从本地存储获取
        const cachedUserInfo = uni.getStorageSync('USER_INFO');
        if (cachedUserInfo) {
          let parsedInfo = cachedUserInfo;

          // 处理字符串格式的缓存数据
          if (typeof cachedUserInfo === 'string') {
            try {
              parsedInfo = JSON.parse(cachedUserInfo);
            } catch (e) {
              console.error('解析缓存用户信息失败:', e);
              return;
            }
          }

          // 验证数据完整性
          if (parsedInfo && typeof parsedInfo === 'object' && parsedInfo.uid) {
            // 统一通过Pinia更新，避免数据不一致
            this.userStore.updateUserInfo(parsedInfo);
            this.userInfo = { ...this.userInfo, ...parsedInfo };
            this.userClick();
          } else {
            console.warn('缓存的用户信息数据不完整:', parsedInfo);
          }
        }
      } catch (e) {
        console.error('读取缓存用户信息失败:', e);
      }
    },


    

    
    handleLoginStateChanged(isLoggedIn) {
      console.log('登录状态变化:', isLoggedIn);
      if (isLoggedIn) {
        // 登录状态变更为已登录，强制刷新数据
        this.forceRefreshAfterLogin();
      } else {
        // 登录状态变更为未登录，重置用户信息
        this.resetUserInfo();
      }
    },

    // 登录后强制刷新数据
    async forceRefreshAfterLogin() {
      console.log('登录后强制刷新数据');

      // 重置所有状态
      this.page = 1;
      this.list = [];
      this.isEmpty = true;
      this.loadStatus = 'loading';
      this.lastRefreshTime = 0; // 清除缓存时间

      // 清除Pinia缓存
      this.socialStore.clearDynamicCache();

      try {
        // 强制重新加载数据
        await this.loadData();
        console.log('登录后数据加载完成');
      } catch (error) {
        console.error('登录后数据加载失败:', error);
      }
    },
    
    resetUserInfo() {
      this.userInfo = {
        avatar: '/static/img/avatar.png',
        nickname: '未登录',
        about_me: '', 
        follow_count: 0,
        fans_count: 0,
        like_count: 0,
        like_count_str: '0',
        visitor_count: 0,
        visitor_badge: 0
      };
      this.userStore.logout();
      
      // 重置数据状态
      this.list = [];
      this.isEmpty = true;
      this.loadStatus = 'more';
      this.page = 1;
      
      // 重置加载状态
      Object.keys(this.loading).forEach(key => {
        this.loading[key] = false;
      });

      // 重置刷新时间
      this.lastRefreshTime = 0;
    },
    
    handleApiError(err) {
      if (err && (err.statusCode === 401 || err.code === 401 || err.status === 401)) {
        uni.showToast({
          title: '登录信息已过期，请重新登录',
          icon: 'none',
          duration: 2000
        });
        this.resetUserInfo();
        setTimeout(() => toLogin(), 1500);
        return true;
      }
      return false;
    },
    

    

    
    updateStoreState() {
      // 使用Pinia更新状态
      try {
        // 更新应用状态
        this.appStore.setCurrentMsg(true);
      } catch (error) {
        console.warn('更新 store 状态失败:', error.message);
      }
    },
    
    // 优化的标签切换
    barClick(e) {
      if (this.loading.dynamicList) return;

      const newBarIdx = parseInt(e.currentTarget.dataset.idx);
      if (newBarIdx === this.barIdx) return;

      console.log('切换标签:', {
        from: this.barIdx,
        to: newBarIdx,
        fromName: this.barList[this.barIdx],
        toName: this.barList[newBarIdx]
      });

      // 保存当前标签的滚动位置
      this.saveScrollPosition();

      this.barIdx = newBarIdx;
      this.page = 1;

      // 重置状态
      this.list = [];
      this.isEmpty = true;
      this.loadStatus = 'loading';

      // 加载新标签的数据
      this.loadDynamicList().then(() => {
        // 恢复滚动位置（如果有缓存）
        this.$nextTick(() => {
          this.restoreScrollPosition();
        });
      });
    },

    // 新增：保存滚动位置
    saveScrollPosition() {
      const scrollKey = `scroll_position_${this.barIdx}`;
      try {
        uni.setStorageSync(scrollKey, this.scrollTop);
      } catch (error) {
        console.warn('保存滚动位置失败:', error);
      }
    },

    // 新增：恢复滚动位置
    restoreScrollPosition() {
      const scrollKey = `scroll_position_${this.barIdx}`;
      try {
        const savedPosition = uni.getStorageSync(scrollKey);
        if (savedPosition) {
          uni.pageScrollTo({
            scrollTop: savedPosition,
            duration: 0
          });
        }
      } catch (error) {
        console.warn('恢复滚动位置失败:', error);
      }
    },
    
    delClick(e) {
      uni.showModal({
        content: '确定删除该笔记吗？',
        confirmColor: '#FA5150',
        success: (res) => {
          if (res.confirm) {
            deleteDynamic(this.list[e.idx].id).then(res => {
              if (res.status == 200) {
                this.list.splice(e.idx, 1);
                if (this.list.length <= 0) {
                  this.isEmpty = true;
                }
                uni.showToast({title: '删除成功', icon: 'success'});
              } else {
                uni.showToast({title: res.msg || '删除失败', icon: 'none'});
              }
            }).catch(() => {
              uni.showToast({title: '删除失败，请重试', icon: 'none'});
            });
          }
        }
      });
    },
    
    likePopupClick(open) {
      if (open) {
        this.$refs.likePopup.open();
      } else {
        this.$refs.likePopup.close();
      }
    },
    
    navigateToFun(e) {
      // 统一登录状态检查
      if (!this.checkLoginStatus(true)) {
        return;
      }
      
      if (this.showSidebar) {
        this.showSidebar = false;
      }
      
      const url = e.currentTarget.dataset.url;
      if (url === 'center/visitor') {
        this.getVisitorList();
      }
      
      uni.navigateTo({url: '/pages/' + url});
    },
    
    toFollowList(type) {
      // 统一登录状态检查
      if (!this.checkLoginStatus(true)) {
        return;
      }
      
      uni.navigateTo({
        url: `/pages/center/follow?type=${type}&id=${this.userInfo.uid}&name=${this.userInfo.nickname}`
      });
    },
    
    userClick() {
      this.blockList[0].img = this.userInfo.circle_img || '';
      this.blockList[0].count = this.userInfo.circle_count || 0;
      this.blockList[1].img = this.userInfo.cart_img || '';
      this.blockList[1].count = this.userInfo.cart_count || 0;
      this.blockList[2].img = this.userInfo.order_img || '';
      this.blockList[2].count = this.userInfo.order_count || 0;
    },
    
    navigationBarColor(status) {
      uni.setNavigationBarColor({
        frontColor: status ? '#000000' : '#ffffff',
        backgroundColor: 'transparent',
        animation: {duration: 300, timingFunc: 'easeIn'}
      });
    },
    


    toggleSidebar() {
      this.showSidebar = !this.showSidebar;
      // 设置滚动状态（如果需要的话可以通过Pinia管理）
      
      // #ifdef H5
      document.body.style.overflow = this.showSidebar ? 'hidden' : '';
      // #endif
    },

    getVisitorList() {
      // 统一通过Pinia更新访客信息
      const updatedInfo = {
        ...this.userStore.userInfo,
        visitor_badge: 0
      };
      this.userStore.updateUserInfo(updatedInfo);
      this.userInfo = { ...this.userInfo, ...updatedInfo };

      getVisitorDetails({page: 1, limit: 20, type: 0}).then((res) => {
        const emptyData = {visitors: [], total: 0, has_more: false};

        if (res.status === 200 || res.code === 200) {
          const resData = res.data || {};
          const visitorInfo = {
            ...this.userStore.userInfo,
            visitor_count: resData.total || 0,
            visitor_badge: 0
          };
          this.userStore.updateUserInfo(visitorInfo);
          this.userInfo = { ...this.userInfo, ...visitorInfo };

          // 计算是否还有更多数据
          const hasMore = resData.page * resData.limit < resData.total;

          uni.$emit('updateVisitorList', {
            visitors: resData.list || [],
            total: resData.total || 0,
            has_more: hasMore,
            page: resData.page || 1,
            limit: resData.limit || 20
          });
        } else {
          this.userInfo.visitor_count = 0;
          this.userInfo.visitor_badge = 0;
          this.$store.commit("UPDATE_USERINFO", this.userInfo);
          uni.$emit('updateVisitorList', emptyData);
        }
      }).catch((error) => {
        console.error('获取访客列表失败:', error);
        const errorInfo = {
          ...this.userStore.userInfo,
          visitor_count: 0,
          visitor_badge: 0
        };
        this.userStore.updateUserInfo(errorInfo);
        this.userInfo = { ...this.userInfo, ...errorInfo };
        uni.$emit('updateVisitorList', {visitors: [], total: 0, has_more: false});
      });
    },

    setVisit() {
      setVisit({url: '/pages/tabbar/center'}).catch(() => {});
    },

    formatDate(timestamp) {
      if (!timestamp) return '';
      const date = new Date(timestamp * 1000);
      return `${date.getFullYear()}-${('0' + (date.getMonth() + 1)).slice(-2)}-${('0' + date.getDate()).slice(-2)}`;
    },
    
    goToVipPage() {
      // 统一登录状态检查
      if (!this.checkLoginStatus(true)) {
        return;
      }
      uni.navigateTo({url: '/pages/annex/vip_paid/index'});
    },

    handleUserInfoUpdate() {
      // 直接从Pinia store同步最新的用户信息
      if (this.userStore.userInfo && this.userStore.userInfo.uid) {
        this.userInfo = { ...this.userInfo, ...this.userStore.userInfo };
        this.userClick();
      } else {
        // 如果Pinia中没有数据，再从缓存加载
        this.loadUserFromCache();
      }
    },

    handleBottomNav(type) {
      // 统一登录状态检查
      if (!this.checkLoginStatus(true)) {
        return;
      }

      this.showSidebar = false;

      switch(type) {
        case 'scan':
          // #ifdef APP-PLUS || MP-WEIXIN
          uni.scanCode({
            success: (res) => {
              if(res.result) {
                uni.showToast({title: '扫码成功', icon: 'success'});
              }
            },
            fail: () => {
              uni.showToast({title: '扫码失败', icon: 'none'});
            }
          });
          // #endif
          
          // #ifdef H5
          uni.showToast({title: 'H5环境不支持扫码功能', icon: 'none'});
          // #endif
          break;
          
        case 'help':
          uni.navigateTo({url: '/pages/setting/service'});
          break;
          
        case 'setting':
          uni.navigateTo({url: '/pages/setting/index'});
          break;
      }
    },



    // 新增：防抖处理
    debounce(func, wait) {
      let timeout;
      return function executedFunction(...args) {
        const later = () => {
          clearTimeout(timeout);
          func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
      };
    },

    // 新增：节流处理
    throttle(func, limit) {
      let inThrottle;
      return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
          func.apply(context, args);
          inThrottle = true;
          setTimeout(() => inThrottle = false, limit);
        }
      };
    },

    handleLogin() {
      toLogin();
    },

    // 调试方法：手动触发数据加载
    async debugLoadData() {
      console.log('手动触发数据加载');
      console.log('当前状态:', {
        isLogin: this.isLogin,
        uid: this.userStore.uid,
        token: this.userStore.token ? '有token' : '无token',
        listLength: this.list.length,
        socialStore: this.socialStore ? '已初始化' : '未初始化',
        dynamicCache: this.socialStore?.dynamicCache
      });

      // 测试 store 方法
      try {
        console.log('测试缓存方法...');
        const testValid = this.socialStore.isCacheValid('personal', 'notes');
        console.log('缓存有效性测试结果:', testValid);

        const testCache = this.socialStore.getDynamicCache('personal', 'notes');
        console.log('获取缓存测试结果:', testCache);
      } catch (error) {
        console.error('Store方法测试失败:', error);
      }

      if (this.isLogin) {
        await this.forceRefreshAfterLogin();
      } else {
        console.log('用户未登录，无法加载数据');
      }
    },

    // 实际的滚动处理逻辑
    handlePageScroll(e) {
      if (this.showSidebar) {
        return;
      }

      this.scrollTop = e.scrollTop;

      const threshold = this.statusBarHeight + this.titleBarHeight + 80;

      // 优化导航栏透明度计算
      if (this.scrollTop <= threshold) {
        this.navbarTrans = Math.min(this.scrollTop / threshold, 1);
      } else {
        this.navbarTrans = 1;
      }

      // 优化导航栏颜色切换，避免频繁调用
      const shouldShowDark = this.scrollTop > threshold;
      if (this.lastNavColorState !== shouldShowDark) {
        this.lastNavColorState = shouldShowDark;
        this.navigationBarColor(shouldShowDark ? 1 : 0);
      }
    }
  }
}
</script>

<style>
.nav-box{
  position:fixed;
  z-index:99;
  top:0;
  left:0;
  width:100%;
  box-sizing:border-box;
  transition:all .3s ease-in-out
}
.nav-box .nav-item{
  position: relative;
  width: 100%;
  justify-content: center;
  align-items: center;
  height: 100%;
}
.nav-box .nav-item .ohto{
  max-width: 420rpx;
  font-size: 26rpx;
  font-weight: 700;
  transition: all 0.3s ease-in-out;
  align-items: center;
  justify-content: center;
  height: 100%;
}
.nav-user-avatar {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  margin-right: 12rpx;
}
.user-box{
  width:calc(100% - 60rpx);
  padding:60rpx 30rpx;
  color:#fff;
  position:relative;
  overflow:hidden
}
.user-box .user-img,
.user-box .user-bg{
  position:absolute;
  top:0;
  left:0;
  width:100%;
  height:100%
}
.user-box .user-bg{
  z-index:-1;
  /* 优化性能：条件编译backdrop-filter */
  /* #ifndef APP-PLUS */
  -webkit-backdrop-filter:saturate(150%) blur(25px);
  backdrop-filter:saturate(150%) blur(25px);
  /* #endif */
  background:rgba(0,0,0,.6)
}
.user-box .user-top{
  width:100%;
  justify-content:space-between
}
.user-top .avatar{
  width:140rpx;
  height:140rpx;
  border-radius:50%;
  background:#fff;
  border:2px solid #f5f5f5;
  overflow:hidden
}
.user-box .user-name{
  margin:20rpx 0 10rpx;
  width:100%;
  font-size:34rpx;
  font-weight:700
}
.user-box .user-intro{
  width:100%;
  word-break:break-word;
  white-space:pre-line
}
.user-box .user-intro text{
  color:#ccc;
  font-size:24rpx;
  font-weight:400
}
.user-box .user-tag{
  margin:20rpx 0;
  width:100%
}
.user-tag .tag-item{
  margin-right:16rpx;
  height:44rpx;
  padding:0 14rpx;
  border-radius:8rpx;
  background:rgba(255,255,255,.15);
  font-weight:500;
  font-size:20rpx;
  justify-content:center
}
.user-tag .tag-item image{
  width:24rpx;
  height:24rpx
}
.user-num-wrap {
  width: 100%;
  justify-content: space-between;
  align-items: center;
  margin-top: 20rpx;
}
.user-num {
  flex: 1;
}
.user-num .num-item {
  margin-right: 30rpx;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.user-num .num-item .t1 {
  color: #fff;
  font-size: 32rpx;
  font-weight: 700;
  margin-bottom: 8rpx;
}
.user-num .num-item .t2 {
  font-size: 20rpx;
  font-weight: 300;
  color: #ccc;
}
.visitor-item {
  position: relative;
}
.visitor-item .badge {
  position: absolute;
  top: -12rpx;
  right: -28rpx;
  min-width: 32rpx;
  height: 32rpx;
  padding: 0 6rpx;
  background: #ff3a3a;
  color: #fff;
  border-radius: 16rpx;
  font-size: 20rpx;
  text-align: center;
  line-height: 32rpx;
  z-index: 2;
  box-shadow: 0 2rpx 4rpx rgba(0,0,0,0.2);
}
.user-actions {
  align-items: center;
}
.btn-item {
  padding: 0 30rpx;
  height: 64rpx;
  line-height: 64rpx;
  border-radius: 8rpx;
  font-size: 20rpx;
  font-weight: 700;
}
.bg1 {
  color: #fff;
  background: rgba(255,255,255,.15);
}
.btn-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 8rpx;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 20rpx;
}
.btn-icon image {
  width: 32rpx;
  height: 32rpx;
}
.user-block{
  margin-top:-30rpx;
  width:100%;
  white-space:nowrap;
  background:#fff;
  border-radius:30rpx 30rpx 0 0
}
.user-block .block-box{
  width:100%;
  padding:30rpx 15rpx;
  display:flex
}
.block-box .block-item{
  flex-shrink:0;
  margin:0 15rpx;
  padding:24rpx;
  background:#f8f8f8;
  border-radius:16rpx;
  justify-content:space-between;
  position:relative
}
.block-item .block-title .t1{
  font-size:26rpx;
  font-weight:700
}
.block-item .block-title .t2{
  margin-top:4rpx;
  color:#999;
  font-size:16rpx;
  font-weight:300
}
.block-item .cu-group{
  position:relative;
  right:38rpx
}
.cu-group .cu-item{
  z-index:3;
  width:68rpx;
  height:68rpx;
  border-radius:8rpx;
  overflow:hidden
}
.cu-group .cu-item .icon{
  margin:18rpx;
  width:32rpx;
  height:32rpx
}
.cu-group .cu-item .img{
  width:100%;
  height:100%
}
.cu-group .cu-lump2{
  position:absolute;
  z-index:2;
  left:18rpx;
  width:58rpx;
  height:58rpx;
  border-radius:8rpx;
  background:#dbdbdb
}
.cu-group .cu-lump1{
  position:absolute;
  z-index:1;
  left:38rpx;
  width:48rpx;
  height:48rpx;
  border-radius:8rpx;
  background:#eaeaea
}
.block-item .block-icon{
  position:absolute;
  right:12rpx;
  width:20rpx;
  height:20rpx;
  transform:rotate(-90deg)
}
.bar-box{
  position:sticky;
  left:0;
  z-index:99;
  margin-top:-1px;
  width:100%;
  height:80rpx;
  background:#fff
}
.bar-box .bar-item{
  padding:0 30rpx;
  height:100%;
  flex-direction:column;
  justify-content:center;
  position:relative
}
.bar-box .bar-item text{
  font-weight:700;
  transition:all .3s ease-in-out
}
.bar-item .bar-line{
  position:absolute;
  bottom:12rpx;
  width:18rpx;
  height:6rpx;
  border-radius:6rpx;
  background:#000;
  transition:opacity .3s ease-in-out
}
/* 优化的CSS类名 */
.content-container{
  padding-bottom:180rpx;
  /* 优化渲染性能 */
  contain: layout style paint;
}

/* 动态列表优化 */
.dynamic-list {
  /* 启用硬件加速 */
  transform: translateZ(0);
  /* 优化滚动性能 */
  -webkit-overflow-scrolling: touch;
}

/* 加载更多容器 */
.load-more-container {
  padding: 20rpx 0;
  text-align: center;
}
.like-popup{
  background:#fff;
  width:400rpx;
  padding:30rpx;
  border-radius:30rpx;
  overflow:hidden
}
.like-popup .like-img{
  margin:0 40rpx;
  width:320rpx;
  height:200rpx
}
.like-popup .like-content{
  margin:20rpx 0 40rpx;
  width:100%;
  color:#333;
  font-size:26rpx;
  text-align:center
}
.like-popup .like-btn{
  width:100%;
  height:80rpx;
  line-height:80rpx;
  text-align:center;
  font-size:24rpx;
  font-weight:700;
  color:#fff;
  background:#000;
  border-radius:16rpx
}
.df {
  display: flex;
  align-items: center;
}
.ohto {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.empty-state {
  flex-direction: column;
  padding: 120rpx 0;
}
.empty-state image {
  width: 280rpx;
  height: 280rpx;
}
.empty-state .empty-title {
  margin-top: 40rpx;
  font-size: 32rpx;
  font-weight: bold;
}
.empty-state .empty-subtitle {
  margin-top: 20rpx;
  font-size: 24rpx;
  color: #999;
}

/* 重试按钮样式已删除 */

.loading-state {
  flex-direction: column;
  padding: 120rpx 0;
}

.loading-text {
  margin-top: 20rpx;
  font-size: 24rpx;
  color: #999;
}
.loading-indicator {
  justify-content: center;
}
.nav-menu-btn {
  position: absolute;
  left: 30rpx;
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
  -webkit-tap-highlight-color: transparent;
}
.nav-menu-btn image {
  width: 24rpx;
  height: 24rpx;
}
.nav-menu-btn:active {
  background: rgba(0,0,0,0.5);
}
.sidebar-menu {
  position: fixed;
  top: 0;
  left: -75%;
  width: 75%;
  height: 100%;
  max-height: 100vh;
  background: #fff;
  z-index: 999;
  box-shadow: 2rpx 0 10rpx rgba(0,0,0,0.1);
  transform: translateX(0);
  transition: transform 0.3s ease-in-out;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.sidebar-menu.active {
  transform: translateX(100%);
}

.sidebar-header {
  flex-shrink: 0;
  padding: 30rpx;
  border-bottom: 1px solid #f5f5f5;
  position: relative; /* 为关闭按钮提供定位基准 */
}

.sidebar-user-info {
  display: flex;
  align-items: center;
}

.sidebar-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.sidebar-user-details {
  flex: 1;
  overflow: hidden;
}

.sidebar-user-name {
  font-size: 30rpx;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.user-status {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.status-item {
  display: flex;
  align-items: center;
  padding: 4rpx 10rpx;
  border-radius: 20rpx;
  margin-right: 12rpx;
  font-size: 20rpx;
  margin-top: 4rpx;
}

.status-icon {
  width: 80rpx;
  height: 40rpx;
  margin-right: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-icon image {
  width: 80rpx;
  height: 36rpx;
  display: block; /* 确保图片正确显示 */
}


.member-card {
  flex-shrink: 0;
  margin: 0 30rpx 20rpx;
  padding: 30rpx 20rpx;
  background: #2c2c2c;
  border-radius: 16rpx;
  color: #fff;
}

.member-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.member-label {
  font-size: 30rpx;
  font-weight: bold;
}

.member-price {
  padding: 6rpx 20rpx;
  background: #fff;
  color: #333;
  border-radius: 30rpx;
  font-size: 22rpx;
}

.member-benefits {
  margin-bottom: 10rpx;
}

.member-rights {
  font-size: 22rpx;
  color: rgba(255,255,255,0.8);
}

.member-desc {
  margin-top: 16rpx;
  font-size: 22rpx;
  color: rgba(255,255,255,0.7);
}

.sidebar-scroll {
  flex: 1;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
}

.sidebar-content {
  padding: 16rpx 0 200rpx;
  background-color: #f7f7f7;
}

/* 菜单部分样式 */
.menu-section {
  margin: 20rpx;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
}

.section-title {
  padding: 20rpx 30rpx 10rpx;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  border-bottom: 1px solid #f5f5f5;
}

/* 菜单宫格样式 */
.menu-grid {
  display: flex;
  flex-wrap: wrap;
  padding: 10rpx;
  background-color: #fff;
}

.grid-item {
  width: 33.33%;
  text-align: center;
  padding: 20rpx 0;
  position: relative;
}

.grid-item:active {
  background-color: #f8f8f8;
}

.grid-icon-wrapper {
  position: relative;
  margin: 0 auto 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.grid-icon {
  width: 50rpx;
  height: 50rpx;
}

.grid-badge {
  position: absolute;
  top: -6rpx;
  right: -6rpx;
  min-width: 32rpx;
  height: 32rpx;
  padding: 0 6rpx;
  background: #ff3a3a;
  color: #fff;
  border-radius: 16rpx;
  font-size: 20rpx;
  text-align: center;
  line-height: 32rpx;
  z-index: 2;
  box-shadow: 0 2rpx 4rpx rgba(0,0,0,0.2);
}

.grid-text {
  font-size: 24rpx;
  color: #333;
  display: block;
  padding: 0 10rpx;
}

.sidebar-footer {
  flex-shrink: 0;
  background: #fff;
}

.bottom-nav {
  width: 100%;
  height: 120rpx;
  justify-content: space-around;
  padding: 0;
  background-color: #f7f7f7;
}

.bottom-nav-item {
  flex: 1;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.nav-icon-box {
  width: 50rpx;
  height: 50rpx;
  justify-content: center;
  align-items: center;
}

.nav-icon {
  width: 44rpx;
  height: 44rpx;
}

.nav-text {
  font-size: 22rpx;
  color: #666;
  margin-top: 8rpx;
}

.copyright-text {
  text-align: center;
  color: #999;
  font-size: 20rpx;
  padding: 10rpx 0 30rpx;
}

.sidebar-mask {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: rgba(0,0,0,0.5);
  z-index: 998;
  touch-action: none;
}

.no-scroll {
  overflow: hidden !important;
}

.container.no-scroll,
.container[style*="position: fixed"] {
  position: fixed !important;
  left: 0 !important;
  width: 100% !important;
  overflow: hidden !important;
  touch-action: none !important;
  height: 100vh !important;
}

.container[style*="position: fixed"] .user-box,
.container[style*="position: fixed"] .nav-box {
  transform: none !important;
}

@media screen and (max-height: 667px) {
  .sidebar-scroll {
    height: calc(100vh - 350rpx - 170rpx);
  }
}

.sidebar-item:active {
  background-color: #f8f8f8;
}

.menu-group {
  margin-bottom: 0;
  margin: 0 20rpx;
  border-radius: 16rpx;
  background-color: #fff;
  overflow: hidden;
}

.menu-divider {
  height: 16rpx;
  background-color: #f7f7f7;
  margin: 0;
  width: 100%;
}

.close-btn {
  position: absolute;
  right: 30rpx;
  top: 50%;
  transform: translateY(-50%) rotate(45deg);
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: #f8f8f8;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

button, 
.btn, 
.nav-menu-btn, 
view[role="button"] {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}

.user-top {
  width: 100%;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.avatar-wrapper {
  position: relative;
  margin-right: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.avatar {
  width: 140rpx;
  height: 140rpx;
  border-radius: 50%;
  background: #fff;
  border: 2px solid #f5f5f5;
  overflow: hidden;
  position: relative;
}

.profile-percent {
  position: absolute;
  left: 50%;
  top: 0;
  transform: translateX(-50%);
  background: #ff6600;
  border-radius: 12rpx;
  padding: 4rpx 10rpx;
  display: flex;
  align-items: center;
  font-size: 20rpx;
  color: #fff;
  z-index: 2;
  box-shadow: 0 2rpx 4rpx rgba(0,0,0,0.2);
}

.edit-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 4rpx;
}

.user-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding-top: 20rpx;
}

.user-name-row {
  align-items: center;
  margin-bottom: 10rpx;
}

.user-name-text {
  font-size: 34rpx;
  font-weight: 700;
  color: #fff;
  margin-right: 12rpx;
}

.status-icon {
  width: 80rpx;
  height: 40rpx;
  margin-right: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-icon image {
  width: 80rpx;
  height: 36rpx;
  display: block; /* 确保图片正确显示 */
}

.user-id {
  font-size: 22rpx;
  color: rgba(255,255,255,0.6);
}

.user-id-row {
  align-items: center;
  margin-top: 8rpx;
}

.sex-icon {
  width: 32rpx;
  height: 32rpx;
  border-radius: 6rpx;
  background: rgba(255,255,255,0.15);
  align-items: center;
  justify-content: center;
  margin-right: 12rpx;
}

.sex-icon image {
  width: 20rpx;
  height: 20rpx;
}

.vip-icon {
  border-radius: 6rpx;
  padding: 2rpx;
}

.verified-icon {
  border-radius: 6rpx;
  padding: 2rpx;
}

.tag-wrapper {
  position: relative;
  width: 100%;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}

.tag-scroll-view {
  width: calc(100% - 70rpx);
  white-space: nowrap;
}

.tag-add-btn {
  position: absolute;
  right: 0rpx;
  width: 46rpx;
  height: 46rpx;
  border-radius: 46rpx;
  background: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tag-add-btn text {
  font-size: 40rpx;
  color: #666;
  font-weight: 300;
}

.tag-add-empty {
  width: 100%;
  height: 60rpx;
  background: #f8f8f8;
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tag-add-empty text {
  color: #999;
  font-size: 26rpx;
}

.tag-empty {
  flex: 1;
  height: 60rpx;
  background: rgba(255,255,255,.15);
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.tag-empty text {
  color: rgba(255,255,255,0.6);
  font-size: 24rpx;
}

.user-tag {
  flex-wrap: nowrap;
  padding: 0;
  margin-top: 0;
}

.tag-wrapper .user-tag {
  display: inline-flex;
  padding-right: 20rpx;
}

.tag-wrapper .tag-item {
  margin-right: 20rpx;
  flex-shrink: 0;
}

.menu-grid {
  display: flex;
  flex-wrap: wrap;
  padding: 20rpx 10rpx;
  background-color: #fff;
}

.grid-item {
  width: 33.33%;
  text-align: center;
  padding: 20rpx 0;
  position: relative;
}

.grid-icon {
  width: 50rpx;
  height: 50rpx;
}

.grid-badge {
  position: absolute;
  top: -6rpx;
  right: -6rpx;
  min-width: 32rpx;
  height: 32rpx;
  padding: 0 6rpx;
  background: #ff3a3a;
  color: #fff;
  border-radius: 16rpx;
  font-size: 20rpx;
  text-align: center;
  line-height: 32rpx;
  z-index: 2;
  box-shadow: 0 2rpx 4rpx rgba(0,0,0,0.2);
}

.grid-text {
  font-size: 24rpx;
  color: #333;
  display: block;
  padding: 0 10rpx;
}

.profile-progress {
  position: absolute;
  top: -8rpx;
  left: -8rpx;
  right: -8rpx;
  bottom: -8rpx;
  z-index: 10;
}

.progress-circle {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.progress-inner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0,0,0,0.6);
  border-radius: 20rpx;
  padding: 4rpx 8rpx;
  backdrop-filter: blur(10rpx);
}

.progress-text {
  color: #fff;
  font-size: 20rpx;
  font-weight: 700;
  line-height: 1;
}

.avatar {
  position: relative;
}

.user-box .user-intro{
  width:100%;
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}
.user-box .user-intro .intro-text{
  color:#ccc;
  font-size:24rpx;
  font-weight:400;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
  word-break: break-all;
  flex: 1;
}
.user-box .user-intro .more-btn{
  position: absolute;
  right: 0;
  top: 0;
  width: 50rpx;
  height: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.user-box .user-intro .more-btn image{
  width: 32rpx;
  height: 32rpx;
}

.user-box .user-top{
  width:100%;
  justify-content:space-between;
  position: relative;
  padding-right: 40rpx;
}
.user-box .user-top .right-arrow {
  position: absolute;
  right: 0;
  top: 50%;
  display: flex;
  -webkit-transform: translateY(-50%) rotate(270deg);
  transform: translateY(-50%) rotate(270deg)
}
.user-box .user-top .right-arrow image {
  width: 140rpx;
  height: 40rpx;
}

.stat-box {
  display: flex;
  justify-content: space-around;
  align-items: center;
  width: calc(100% - 60rpx);
  padding: 20rpx 0;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 20rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);
  margin: 20rpx auto;
  position: relative;
}

.stat-divider {
  width: 1px;
  height: 60rpx;
  background-color: rgba(255, 255, 255, 0.2);
}

.stat-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  text-align: center;
  padding: 10rpx 0;
}

.stat-icon {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 16rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.stat-icon .iconfont {
  font-size: 36rpx;
}

.stat-value {
  font-size: 36rpx;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 8rpx;
  line-height: 1;
}

.stat-label {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1;
}

.user-num-wrap {
  width: 100%;
}

.stat-badge {
  position: absolute;
  top: -8rpx;
  right: -10rpx;
  min-width: 30rpx;
  height: 30rpx;
  line-height: 30rpx;
  padding: 0 6rpx;
  background-color: #ff3a3a;
  color: #ffffff;
  font-size: 16rpx;
  border-radius: 15rpx;
  text-align: center;
}

.stat-icon .icon-like {
  color: #ff7ca8;
  font-size: 42rpx;
}

.stat-icon .icon-eye {
  color: #e3d6ff;
  font-size: 42rpx;
}

.stat-icon .icon-heart {
  color: #e3d6ff;
  font-size: 42rpx;
}

.stat-like-text {
  color: #ff7ca8;
  font-size: 42rpx;
}

.stat-eye-text {
  color: #e3d6ff;
  font-size: 42rpx;
}

/* 空菜单提示样式 */
.empty-menu-tip {
  padding: 60rpx 0;
  text-align: center;
  color: #999;
  font-size: 24rpx;
}

.stat-heart-text {
  color: #e3d6ff;
  font-size: 42rpx;
}
</style>