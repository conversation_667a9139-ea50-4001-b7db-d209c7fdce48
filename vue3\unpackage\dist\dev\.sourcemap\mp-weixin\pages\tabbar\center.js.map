{"version": 3, "file": "center.js", "sources": ["pages/tabbar/center.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvdGFiYmFyL2NlbnRlci52dWU"], "sourcesContent": ["<template>\n  <view class=\"container\" :class=\"{'no-scroll': showSidebar}\">\n    <view class=\"nav-box\" :style=\"{'padding-top': statusBarHeight + 'px', 'background': 'rgba(255,255,255,'+ navbarTrans +')'}\">\n      <view class=\"nav-item df\" :style=\"{'height': titleBarHeight + 'px'}\">\n        <view class=\"nav-menu-btn\" @tap=\"toggleSidebar\">\n          <image src=\"/static/img/menu.png\"></image>\n        </view>\n        <view class=\"ohto df\" :style=\"{'transform': 'translateY('+ ((1-navbarTrans) * 30) +'px)', 'opacity': navbarTrans}\">\n          <image :src=\"userInfo.avatar\" mode=\"aspectFill\" class=\"nav-user-avatar\"></image>\n          <text>{{userInfo.nickname}}</text>\n        </view>\n      </view>\n    </view>\n    <view class=\"user-box\" :style=\"{'padding-top': statusBarHeight + titleBarHeight + 'px'}\">\n      <view class=\"user-bg\"></view>\n      <view class=\"user-img\" style=\"z-index:-2\">\n        <lazy-image :src=\"userInfo.avatar || '/static/img/avatar.png'\" mode=\"aspectFill\"></lazy-image>\n      </view>\n      <view class=\"user-top df\" data-url=\"center/means\" @tap=\"navigateToFun\" style=\"position: relative; padding-right: 40rpx; padding-top: 40rpx;\">\n        <view class=\"avatar-wrapper\">\n          <view class=\"avatar\">\n            <lazy-image :src=\"userInfo.avatar\"></lazy-image>\n\n          </view>\n        </view>\n        <view class=\"user-info\">\n          <view class=\"user-name-row df\">\n            <text class=\"user-name-text\">{{userInfo.nickname}}</text>\n            <view class=\"status-icon vip-icon\" v-if=\"userInfo.is_money_level> 0 && userInfo.svip_open\">\n              <image src=\"/static/img/svip.gif\"></image>\n            </view>\n            <view class=\"status-icon verified-icon\" v-if=\"userInfo.is_verified\">\n              <image src=\"/static/img/rz.png\"></image>\n            </view>\n          </view>\n          <view class=\"user-id-row df\">\n            <view v-if=\"userInfo.sex != 2\" class=\"sex-icon df\">\n              <image :src=\"userInfo.sex == 1 ? '/static/img/nan.png' : '/static/img/nv.png'\"></image>\n            </view>\n            <text class=\"user-id\">ID: {{userInfo.user_id_number}}</text>\n          </view>\n        </view>\n        <view class=\"right-arrow\">\n          <image src=\"/static/img/x.png\" mode=\"aspectFit\"></image>\n        </view>\n      </view>\n\n      <view class=\"user-num-wrap df\">\n        <view class=\"user-num df\">\n          <view class=\"num-item df\" @tap=\"toFollowList(0)\">\n            <text class=\"t1\">{{userInfo.follow_count}}</text>\n            <text class=\"t2\">关注</text>\n          </view>\n          <view class=\"num-item df\" @tap=\"toFollowList(1)\">\n            <text class=\"t1\">{{userInfo.fans_count}}</text>\n            <text class=\"t2\">粉丝</text>\n          </view>\n          <view class=\"num-item df\" @tap=\"likePopupClick(true)\">\n            <text class=\"t1\">{{userInfo.like_count_str}}</text>\n            <text class=\"t2\">获赞</text>\n          </view>\n          <view class=\"num-item df visitor-item\" data-url=\"center/visitor\" @tap=\"navigateToFun\">\n            <text class=\"t1\">{{userInfo.visitor_count}}</text>\n            <text class=\"t2\">访客</text>\n            <view v-if=\"userInfo.visitor_badge\" class=\"badge\">+{{userInfo.visitor_badge > 99 ? '99+' : userInfo.visitor_badge}}</view>\n          </view>\n        </view>\n      </view>\n      <view class=\"tag-wrapper\">\n        <view v-if=\"userInfo.interest_tags && userInfo.interest_tags.length\" class=\"user-tag df\">\n          <view v-for=\"(tag, index) in userInfo.interest_tags\" :key=\"index\" class=\"tag-item df\">\n            <text>{{tag}}</text>\n          </view>\n        </view>\n        <view v-else class=\"tag-empty\">\n          <text>添加兴趣标签，让大家更了解你</text>\n        </view>\n        <view class=\"user-actions df\">\n          <view class=\"btn-icon\" data-url=\"setting/index\" @tap=\"navigateToFun\">\n            <image src=\"/static/img/setting/104.png\"></image>\n          </view>\n        </view>\n      </view>\n      <view class=\"user-intro\" data-url=\"center/means\" @tap=\"navigateToFun\">\n        <text class=\"intro-text\" user-select=\"true\">{{userInfo.about_me ? userInfo.about_me : \"添加个人简介，让大家认识你...\"}}</text>\n        <view class=\"more-btn\">\n          <image src=\"/static/img/more.png\" mode=\"aspectFit\"></image>\n        </view>\n      </view>\n      \n\n    </view>\n    <scroll-view scroll-x=\"true\" class=\"user-block\">\n      <view class=\"block-box\">\n        <view v-if=\"userInfo.activity_count\" class=\"block-item df\" data-url=\"activity/index?type=1\" @tap=\"navigateToFun\">\n          <view class=\"block-title\" style=\"margin-right:68rpx\">\n            <view class=\"t1\">活动</view>\n            <view class=\"t2\">{{userInfo.activity_count ? '共' + userInfo.activity_count + '个活动' : '没有参加活动'}}</view>\n          </view>\n          <view class=\"cu-group df\">\n            <view class=\"cu-item\" :style=\"{'background': userInfo.activity_img ? '#CECECE' : '#000'}\">\n              <image v-if=\"!userInfo.activity_img\" class=\"icon\" src=\"/static/img/hd.png\"></image>\n              <image v-else class=\"img\" :src=\"userInfo.activity_img\" mode=\"aspectFill\"></image>\n            </view>\n            <view class=\"cu-lump1\"></view>\n            <view class=\"cu-lump2\"></view>\n          </view>\n          <image class=\"block-icon\" src=\"/static/img/x.png\"></image>\n        </view>\n        <view v-for=\"(item, index) in blockList\" :key=\"index\" class=\"block-item df\" :data-url=\"item.url\" @tap=\"navigateToFun\">\n          <view class=\"block-title\" style=\"margin-right:68rpx\">\n            <view class=\"t1\">{{item.name}}</view>\n            <view v-if=\"item.count\" class=\"t2\">\n              共{{item.count}}{{index == 0 ? '个圈子' : index == 1 ? '件商品' : '笔订单'}}\n            </view>\n            <view v-else class=\"t2\">\n              {{index == 0 ? '没有加入圈子' : index == 1 ? '购物车空空的' : '订单空空的'}}\n            </view>\n          </view>\n          <view class=\"cu-group df\">\n            <view class=\"cu-item\" :style=\"{'background': item.img ? '#CECECE' : '#000'}\">\n              <image v-if=\"!item.img\" class=\"icon\" :src=\"item.icon\"></image>\n              <image v-else class=\"img\" :src=\"item.img\" mode=\"aspectFill\"></image>\n            </view>\n            <view class=\"cu-lump1\"></view>\n            <view class=\"cu-lump2\"></view>\n          </view>\n          <image class=\"block-icon\" src=\"/static/img/x.png\"></image>\n        </view>\n        <view v-if=\"appCard || userInfo.card_count\" class=\"block-item df\" data-url=\"center/card?type=1\" @tap=\"navigateToFun\">\n          <view class=\"block-title\" style=\"margin-right:68rpx\">\n            <view class=\"t1\">卡券</view>\n            <view class=\"t2\">{{userInfo.card_count ? '共' + userInfo.card_count + '张卡券' : '暂无可用卡券'}}</view>\n          </view>\n          <view class=\"cu-group df\">\n            <view class=\"cu-item\" style=\"background:#000\">\n              <image class=\"icon\" src=\"/static/img/kq.png\"></image>\n            </view>\n            <view class=\"cu-lump1\"></view>\n            <view class=\"cu-lump2\"></view>\n          </view>\n          <image class=\"block-icon\" src=\"/static/img/x.png\"></image>\n        </view>\n        <view style=\"flex-shrink:0;width:15rpx;height:15rpx\"></view>\n      </view>\n    </scroll-view>\n    <view class=\"bar-box df\" :style=\"{'top': statusBarHeight + titleBarHeight - 1 + 'px'}\">\n      <view v-for=\"(item, index) in barList\" :key=\"index\" class=\"bar-item df\" @tap=\"barClick\" :data-idx=\"index\">\n        <text :style=\"{'color': index == barIdx ? '#000' : '#999', 'font-size': index == barIdx ? '28rpx' : '26rpx'}\">{{item}}</text>\n        <view :style=\"{'opacity': index == barIdx ? 1 : 0}\" class=\"bar-line\"></view>\n      </view>\n    </view>\n    <!-- 加载状态显示 -->\n    <view class=\"loading-indicator df\" :style=\"{'height': (loading.refreshing && list.length === 0) ? '60rpx' : '0px'}\">\n      <uni-load-more v-if=\"loading.refreshing && list.length === 0\" :status=\"'loading'\"></uni-load-more>\n    </view>\n\n    <view class=\"content-container\">\n      <!-- 未登录状态 -->\n      <view v-if=\"!isLogin\" class=\"empty-state df\">\n        <image src=\"/static/img/login.png\"/>\n        <view class=\"empty-title\">您还未登录</view>\n        <view class=\"empty-subtitle\">登录后查看您的笔记和点赞内容</view>\n        <!-- #ifdef MP-WEIXIN -->\n        <view class=\"login-btn\" @tap=\"handleLogin\">立即登录</view>\n        <!-- #endif -->\n      </view>\n\n      <!-- 已登录但数据为空状态 -->\n      <view v-else-if=\"isEmpty && !loading.dynamicList\" class=\"empty-state df\">\n        <image src=\"/static/img/empty.png\"/>\n        <view class=\"empty-title\">{{getEmptyTitle()}}</view>\n        <view class=\"empty-subtitle\">{{getEmptySubtitle()}}</view>\n      </view>\n\n      <!-- 加载中状态 -->\n      <view v-else-if=\"loading.dynamicList && list.length === 0\" class=\"loading-state df\">\n        <uni-load-more :status=\"'loading'\"></uni-load-more>\n        <view class=\"loading-text\">正在加载内容...</view>\n      </view>\n\n      <!-- 内容列表 -->\n      <block v-else>\n        <block v-for=\"(item, index) in list\" :key=\"item.id || index\">\n          <card-gg v-if=\"barIdx == 1\" :item=\"item\" :idx=\"index\"></card-gg>\n          <card-wd v-else :item=\"item\" :idx=\"index\" :bar=\"barIdx\" @delback=\"delClick\"></card-wd>\n        </block>\n      </block>\n\n      <!-- 底部加载更多状态 -->\n      <uni-load-more v-if=\"list.length > 0\" :status=\"loadStatus\"></uni-load-more>\n    </view>\n    <uni-popup ref=\"likePopup\" class=\"r\">\n      <view class=\"like-popup\">\n        <image class=\"like-img\" src=\"/static/img/inset/like.png\" mode=\"aspectFill\"></image>\n        <view class=\"like-content\"><text>\"</text>{{userInfo.nickname}}<text>\"</text>共获得 {{userInfo.like_count || 0}} 个赞 </view>\n        <view class=\"like-btn\" @tap=\"likePopupClick(false)\">确认</view>\n      </view>\n    </uni-popup>\n    <tabbar :currentPage=\"4\" :currentMsg=\"currentMsg\" :userAvatar=\"userInfo.avatar\"></tabbar>\n    <view class=\"sidebar-menu\" :class=\"{'active': showSidebar}\">\n      <view class=\"sidebar-header\" :style=\"{'padding-top': statusBarHeight + 10 + 'px'}\">\n        <view class=\"sidebar-user-info\">\n          <image :src=\"userInfo.avatar || '/static/img/avatar.png'\" mode=\"aspectFill\" class=\"sidebar-avatar\"></image>\n          <view class=\"sidebar-user-details\">\n            <view class=\"sidebar-user-name\">{{userInfo.nickname}}</view>\n            <view class=\"user-status\">\n              <view class=\"status-item\" v-if=\"userInfo.is_money_level> 0 && userInfo.svip_open\">\n                <image src=\"/static/img/svip.gif\" class=\"status-icon\"></image>\n              </view>\n              <view class=\"status-item verified-tag\" v-if=\"userInfo.is_verified\">\n                <image src=\"/static/img/rz.png\" class=\"status-icon\"></image>\n                <text>已认证</text>\n              </view>\n            </view>\n          </view>\n        </view>\n        <view class=\"close-btn df\" @tap=\"toggleSidebar\">\n          <image src=\"/static/img/tabbar/3.png\" style=\"width:20rpx;height:20rpx;\"></image>\n        </view>\n      </view>\n      <view class=\"member-card\">\n        <view class=\"member-status\">\n          <view v-if=\"userInfo.vip_status == 1\" class=\"member-label\">永久会员</view>\n          <view v-else-if=\"userInfo.vip_status == 3\" class=\"member-label\">\n            会员到期：{{ formatDate(userInfo.overdue_time) }}\n          </view>\n          <view v-else-if=\"userInfo.vip_status == -1\" class=\"member-label\">会员已过期</view>\n          <view v-else-if=\"userInfo.vip_status == 2\" class=\"member-label\">未开通会员</view>\n          <view v-if=\"userInfo.vip_status != 1\" class=\"member-price\" @tap=\"goToVipPage\">¥3.8续费</view>\n        </view>\n        <view class=\"member-benefits\">\n          <text class=\"member-rights\" @tap=\"goToVipPage\">会员权益 | 领取我的等级特权</text>\n        </view>\n        <view class=\"member-desc\">专属优惠，VIP低至¥88，畅听1年！</view>\n      </view>\n      <scroll-view scroll-y class=\"sidebar-scroll\">\n        <view class=\"sidebar-content\">\n          <view v-if=\"sidebarMenu && sidebarMenu.length > 0\" class=\"menu-section\">\n            <view class=\"section-title\">我的服务</view>\n            <view class=\"menu-grid\">\n            <view\n                v-for=\"(item, index) in sidebarMenu\"\n                :key=\"'menu-' + index\"\n                class=\"grid-item\"\n              :data-url=\"item.url\"\n              @tap=\"navigateToFun\">\n                <view class=\"grid-icon-wrapper\">\n                  <image :src=\"item.icon\" class=\"grid-icon\"></image>\n                  <view v-if=\"item.badge\" class=\"grid-badge\">{{item.badge}}</view>\n              </view>\n                <text class=\"grid-text\">{{item.name}}</text>\n              </view>\n            </view>\n          </view>\n          <!-- 当没有菜单数据时显示提示 -->\n          <view v-else class=\"menu-section\">\n            <view class=\"section-title\">我的服务</view>\n            <view class=\"empty-menu-tip\">\n              <text>暂无可用服务</text>\n            </view>\n          </view>\n        </view>\n      </scroll-view>\n      <view class=\"sidebar-footer\">\n        <view class=\"bottom-nav df\">\n          <view class=\"bottom-nav-item df\" @tap=\"handleBottomNav('scan')\">\n            <view class=\"nav-icon-box df\">\n              <image src=\"/static/img/scan.png\" class=\"nav-icon\"></image>\n            </view>\n            <text class=\"nav-text\">扫一扫</text>\n          </view>\n          <view class=\"bottom-nav-item df\" @tap=\"handleBottomNav('help')\">\n            <view class=\"nav-icon-box df\">\n              <image src=\"/static/img/kf.png\" class=\"nav-icon\"></image>\n            </view>\n            <text class=\"nav-text\">帮助与客服</text>\n          </view>\n          <view class=\"bottom-nav-item df\" @tap=\"handleBottomNav('setting')\">\n            <view class=\"nav-icon-box df\">\n              <image src=\"/static/img/setting/104.png\" class=\"nav-icon\"></image>\n            </view>\n            <text class=\"nav-text\">设置</text>\n          </view>\n        </view>\n        <view class=\"copyright-text\">© {{new Date().getFullYear()}} 个人中心</view>\n      </view>\n    </view>\n    <view class=\"sidebar-mask\" v-if=\"showSidebar\" @tap=\"toggleSidebar\" @touchmove.stop.prevent @touchstart.stop @touchend.stop></view>\n  </view>\n</template>\n\n<script>\nimport lazyImage from '@/components/lazyImage/lazyImage'\nimport uniLoadMore from '@/uni_modules/uni-load-more/components/uni-load-more/uni-load-more'\nimport cardWd from '@/components/card-wd/card-wd'\nimport cardGg from '@/components/card-gg/card-gg'\nimport tabbar from '@/components/tabbar/tabbar'\nimport uniPopup from '@/uni_modules/uni-popup/components/uni-popup/uni-popup.vue'\nimport { getMenuList, setVisit } from '@/api/user.js'\nimport { getMyDynamicList, deleteDynamic, getUserSocialInfo, getLikeDynamicList, getVisitorDetails } from '@/api/social.js'\nimport { toLogin } from '@/libs/login.js'\n// 引入Pinia stores\nimport { useUserStore } from '@/stores/user.js'\nimport { useAppStore } from '@/stores/app.js'\nimport { useSocialStore } from '@/stores/social.js'\n\nexport default {\n  components: {\n    lazyImage,\n    uniLoadMore,\n    cardWd,\n    cardGg,\n    tabbar,\n    uniPopup\n  },\n  computed: {\n    userStore() {\n      return useUserStore();\n    },\n    appStore() {\n      return useAppStore();\n    },\n    socialStore() {\n      return useSocialStore();\n    },\n    isLogin() {\n      // 简化登录状态检查，避免复杂的store引用\n      try {\n        const token = uni.getStorageSync('token') || uni.getStorageSync('LOGIN_STATUS_TOKEN');\n        const userInfo = uni.getStorageSync('USER_INFO');\n        const isLoggedIn = !!(token && userInfo && userInfo.uid);\n\n        // 调试信息\n        console.log('登录状态检查详情:', {\n          token: token ? '有token' : '无token',\n          tokenLength: token ? token.length : 0,\n          userInfo: userInfo ? '有用户信息' : '无用户信息',\n          userInfoType: typeof userInfo,\n          uid: userInfo ? userInfo.uid : '无uid',\n          isLoggedIn\n        });\n\n        return isLoggedIn;\n      } catch (error) {\n        console.warn('检查登录状态失败:', error);\n        return false;\n      }\n    }\n  },\n  data() {\n    return {\n      statusBarHeight: 20,\n      titleBarHeight: 44,\n      currentMsg: 0,\n      scrollTop: 0,\n      navbarTrans: 0,\n      userInfo: {\n        avatar: \"\",\n        nickname: \"您还未登录哦~\",\n        follow_count: 0,\n        fans_count: 0,\n        like_count: 0,\n        like_count_str: \"0\",\n        visitor_count: 0,\n        visitor_badge: 0,\n        is_verified: 0,\n        is_money_level: 0,\n        svip_open: 0,\n        sex: 0,\n        user_id_number: \"\",\n        about_me: \"\",\n        interest_tags: [],\n        vip_status: 0,\n        overdue_time: \"\",\n        card_count: 0,\n        activity_count: 0,\n        activity_img: \"\"\n      },\n      blockList: [\n        {name: '圈子', img: '', icon: '/static/img/qz.png', url: 'center/circle?type=1', count: 0},\n        {name: '购物车', img: '', icon: '/static/img/gwc.png', url: 'goods/cart', count: 0},\n        {name: '订单', img: '', icon: '/static/img/dd.png', url: 'order/index', count: 0}\n      ],\n      barList: ['笔记', '赞过'],\n      barIdx: 0,\n      list: [],\n      page: 1,\n      totalCount: 0,\n      isEmpty: true, // 初始状态应该是空的\n      loadStatus: 'more',\n      showSidebar: false,\n      sidebarMenu: [],\n      isLoading: false,\n      isThrottling: false,\n      appCard: true,\n\n      // 简化的加载状态管理\n      loading: {\n        userInfo: false,\n        dynamicList: false,\n        refreshing: false\n      },\n\n      // 简化的缓存管理\n      lastRefreshTime: 0,\n      cacheTimeout: 300000, // 5分钟缓存\n\n      // 首次加载标记\n      isFirstLoad: true\n    }\n  },\n  onPullDownRefresh() {\n    if (!this.isLogin) {\n      uni.stopPullDownRefresh();\n      console.log('未登录，停止下拉刷新');\n      return;\n    }\n    this.refreshData();\n  },\n  onLoad() {\n    // 初始化基础设置\n    this.initBasicSettings();\n\n    // 初始化数据\n    this.initData();\n  },\n  onUnload() {\n    uni.$off('userInfoUpdated', this.handleUserInfoUpdate);\n    uni.$off('loginStateChanged', this.handleLoginStateChanged);\n    \n    // 清除所有定时器\n    this.clearAllTimers();\n  },\n  onShow() {\n    this.navigationBarColor(0);\n\n    // 避免首次加载时的重复调用\n    if (this.isFirstLoad) {\n      this.isFirstLoad = false;\n      return;\n    }\n\n    console.log('onShow - 页面显示:', {\n      isLogin: this.isLogin,\n      listLength: this.list.length,\n      isEmpty: this.isEmpty,\n      currentTab: this.barList[this.barIdx]\n    });\n\n    // 延迟检查登录状态，确保从登录页面跳转过来时数据已保存\n    setTimeout(() => {\n      this.checkAndRefreshLoginStatus();\n    }, 300);\n  },\n  methods: {\n    // 初始化基础设置\n    initBasicSettings() {\n      this.navigationBarColor(0);\n\n      // 设置系统信息（使用简单的默认值）\n      this.statusBarHeight = 20;\n      this.titleBarHeight = 44;\n\n      // 注册事件监听\n      uni.$on('loginStateChanged', this.handleLoginStateChanged);\n      uni.$on('userInfoUpdated', this.handleUserInfoUpdate);\n    },\n\n    // 初始化数据\n    async initData() {\n      console.log('初始化数据开始');\n\n      // 简单检查登录状态\n      const isLoggedIn = this.checkLoginStatus();\n\n      console.log('登录状态检查结果:', isLoggedIn);\n\n      if (isLoggedIn) {\n        // 已登录，加载数据\n        await this.loadData();\n      } else {\n        // 未登录处理\n        this.handleNotLoggedIn();\n      }\n    },\n\n    // 检查并刷新登录状态\n    checkAndRefreshLoginStatus() {\n      console.log('检查并刷新登录状态');\n\n      // 重新检查登录状态\n      const currentLoginStatus = this.isLogin;\n\n      console.log('当前登录状态:', currentLoginStatus);\n\n      if (currentLoginStatus) {\n        // 如果已登录，重新加载用户信息\n        this.loadUserFromCache();\n\n        // 强制触发响应式更新\n        this.$forceUpdate();\n\n        // 如果当前没有数据，强制加载\n        if (this.list.length === 0 && !this.loading.dynamicList) {\n          console.log('检测到无数据，强制刷新');\n          this.forceRefreshCurrentTab();\n        } else if (this.shouldRefreshData()) {\n          this.refreshData();\n        }\n      } else {\n        // 如果未登录，重置状态\n        console.log('未登录状态，重置用户信息');\n        this.resetUserInfo();\n      }\n    },\n\n    // 简化的登录状态检查\n    checkLoginStatus() {\n      try {\n        // 使用与 isLogin computed 相同的逻辑\n        const token = uni.getStorageSync('token') || uni.getStorageSync('LOGIN_STATUS_TOKEN');\n        const userInfo = uni.getStorageSync('USER_INFO');\n        const isLoggedIn = !!(token && userInfo && userInfo.uid);\n\n        console.log('登录状态检查:', {\n          hasToken: !!token,\n          hasUserInfo: !!userInfo,\n          hasUid: !!(userInfo && userInfo.uid),\n          isLoggedIn\n        });\n\n        return isLoggedIn;\n      } catch (error) {\n        console.warn('检查登录状态失败:', error);\n        return false;\n      }\n    },\n\n    // 处理未登录状态\n    handleNotLoggedIn() {\n      // 设置空状态\n      this.list = [];\n      this.isEmpty = true;\n      this.loadStatus = 'more';\n\n      // 重置用户信息为未登录状态\n      this.resetUserInfo();\n\n      console.log('未登录，设置空状态');\n\n      // #ifdef H5 || APP-PLUS\n      // 在 H5 和 APP 中，未登录直接跳转到登录页\n      setTimeout(() => {\n        toLogin();\n      }, 1000);\n      // #endif\n\n      // #ifdef MP-WEIXIN\n      // 小程序中显示未登录状态，不跳转\n      console.log('小程序环境，显示未登录状态');\n      // #endif\n    },\n\n    // 统一的数据加载方法\n    async loadData() {\n      try {\n        // 并行加载用户信息和动态列表\n        await Promise.allSettled([\n          this.loadUserInfo(),\n          this.loadDynamicList()\n        ]);\n\n        // 异步加载侧边栏菜单\n        this.loadSidebarMenu();\n\n        // 记录访问\n        this.setVisit();\n\n        this.lastRefreshTime = Date.now();\n      } catch (error) {\n        console.error('数据加载失败:', error);\n        this.showErrorToast('加载失败，请稍后重试');\n      }\n    },\n\n    // 检查是否需要刷新数据\n    shouldRefreshData() {\n      const now = Date.now();\n      return (now - this.lastRefreshTime) > this.cacheTimeout;\n    },\n\n    // 刷新数据\n    async refreshData() {\n      if (this.loading.refreshing) return;\n\n      this.loading.refreshing = true;\n      this.page = 1;\n\n      try {\n        await this.loadData();\n      } catch (error) {\n        console.error('刷新数据失败:', error);\n      } finally {\n        this.loading.refreshing = false;\n        uni.stopPullDownRefresh();\n      }\n    },\n\n    // 强制刷新当前标签数据\n    async forceRefreshCurrentTab() {\n      console.log('强制刷新当前标签:', this.barList[this.barIdx]);\n\n      // 重置状态\n      this.page = 1;\n      this.list = [];\n      this.isEmpty = true;\n      this.loadStatus = 'loading';\n\n      // 重新加载数据\n      await this.loadDynamicList();\n    },\n\n    // 简化的用户信息加载\n    async loadUserInfo() {\n      if (this.loading.userInfo || !this.isLogin) {\n        console.log('loadUserInfo被阻止:', {\n          loading: this.loading.userInfo,\n          isLogin: this.isLogin\n        });\n        return;\n      }\n\n      this.loading.userInfo = true;\n\n      try {\n        const res = await getUserSocialInfo();\n\n        if (res.status === 200 || res.code === 200) {\n          const userData = res.data;\n\n          // 处理数据格式\n          if (userData.like_count !== undefined) {\n            userData.like_count_str = userData.like_count > 999\n              ? (userData.like_count / 1000).toFixed(1) + 'k'\n              : userData.like_count.toString();\n          }\n\n          // 统一通过Pinia更新用户信息，确保数据一致性\n          this.userStore.updateUserInfo(userData);\n\n          // 同步到本地userInfo（从Pinia获取，确保一致）\n          this.userInfo = { ...this.userInfo, ...this.userStore.userInfo };\n          this.currentMsg = userData.service_num || 0;\n\n          // 更新界面\n          this.userClick();\n        } else {\n          console.warn('获取用户信息失败:', res);\n        }\n      } catch (error) {\n        console.error('加载用户信息失败:', error);\n        if (!this.handleApiError(error)) {\n          this.showErrorToast('加载用户信息失败');\n        }\n      } finally {\n        this.loading.userInfo = false;\n      }\n    },\n\n    // 简化的动态列表加载\n    async loadDynamicList() {\n      if (this.loading.dynamicList || !this.isLogin) {\n        console.log('loadDynamicList被阻止:', {\n          loading: this.loading.dynamicList,\n          isLogin: this.isLogin\n        });\n        return;\n      }\n\n      this.loading.dynamicList = true;\n\n      try {\n        const userId = this.userStore.uid;\n        console.log('开始加载动态列表:', {\n          barIdx: this.barIdx,\n          page: this.page,\n          userId: userId,\n          tabName: this.barIdx === 0 ? '笔记' : '赞过'\n        });\n\n        if (!userId) {\n          throw new Error('用户ID无效');\n        }\n\n        let apiCall;\n        if (this.barIdx === 0) {\n          // 笔记列表\n          apiCall = getMyDynamicList({\n            page: this.page,\n            limit: 10\n          });\n        } else {\n          // 点赞列表\n          apiCall = getLikeDynamicList(userId, {\n            page: this.page,\n            limit: 10\n          });\n        }\n\n        const res = await apiCall;\n        console.log('API响应:', res);\n\n        if (res.status === 200 && res.data) {\n          const newList = res.data.list || [];\n\n          console.log('处理数据:', {\n            newListLength: newList.length,\n            totalCount: res.data.count,\n            currentPage: this.page\n          });\n\n          if (this.page === 1) {\n            this.list = newList;\n          } else {\n            this.list.push(...newList);\n          }\n\n          this.totalCount = res.data.count || 0;\n          this.isEmpty = this.list.length === 0;\n          this.loadStatus = newList.length < 10 ? 'noMore' : 'more';\n\n          console.log('数据更新完成:', {\n            listLength: this.list.length,\n            isEmpty: this.isEmpty,\n            loadStatus: this.loadStatus\n          });\n        } else {\n          console.warn('API响应异常:', res);\n          if (this.page === 1) {\n            this.isEmpty = true;\n            this.list = [];\n          }\n        }\n      } catch (error) {\n        console.error('加载动态列表失败:', error);\n        if (this.page === 1) {\n          this.isEmpty = true;\n          this.list = [];\n        }\n        this.loadStatus = 'more';\n\n        if (!this.handleApiError(error)) {\n          this.showErrorToast('加载动态列表失败');\n        }\n      } finally {\n        this.loading.dynamicList = false;\n      }\n    },\n\n    // 简化的侧边栏菜单加载\n    async loadSidebarMenu() {\n      try {\n        const res = await getMenuList();\n        console.log('菜单接口返回数据:', res);\n\n        if (res.status === 200 && res.data) {\n          // 检查菜单状态是否启用\n          const diyData = res.data.diy_data || {};\n          const menuStatus = diyData.my_menus_status;\n\n          console.log('菜单状态:', menuStatus);\n\n          // 只有当菜单状态为启用(1)时才显示菜单\n          if (menuStatus === 1) {\n            // 处理菜单数据，将接口返回的字段映射到模板需要的字段\n            const menuData = res.data.routine_my_menus || [];\n            this.sidebarMenu = menuData.map(item => ({\n              id: item.id,\n              name: item.name,\n              icon: item.pic, // 将 pic 字段映射为 icon\n              url: item.url,\n              badge: item.badge || null // 如果有徽章数据的话\n            }));\n\n            console.log('侧边栏菜单加载成功:', this.sidebarMenu);\n          } else {\n            console.log('菜单状态未启用，不显示菜单');\n            this.sidebarMenu = [];\n          }\n        }\n      } catch (error) {\n        console.warn('加载侧边栏菜单失败:', error);\n      }\n    },\n\n    // 通用错误处理方法\n    handleApiError(error) {\n      console.error('API错误:', error);\n\n      // 处理存储空间不足\n      if (error.name === 'QuotaExceededError') {\n        this.showErrorToast('存储空间不足，正在清理缓存...');\n        try {\n          this.userStore.clearStorageCache();\n        } catch (e) {\n          console.warn('清理缓存失败:', e);\n        }\n        return true;\n      }\n\n      // 处理网络错误\n      if (error.code === 'NETWORK_ERROR') {\n        this.showErrorToast('网络连接失败，请检查网络');\n        return true;\n      }\n\n      // 处理401未授权\n      if (error.status === 401 || error.statusCode === 401) {\n        try {\n          this.userStore.logout();\n        } catch (e) {\n          console.warn('登出失败:', e);\n        }\n        this.showErrorToast('登录已过期，请重新登录');\n        this.handleNotLoggedIn();\n        return true;\n      }\n\n      return false;\n    },\n\n\n\n\n\n\n\n    // 检查登录状态（简化版）\n    checkLoginStatus(redirectToLogin = false) {\n      try {\n        // 简单检查本地存储的token\n        const token = uni.getStorageSync('token') || uni.getStorageSync('LOGIN_STATUS_TOKEN');\n        const userInfo = uni.getStorageSync('USER_INFO');\n        const isLoggedIn = !!(token && userInfo && userInfo.uid);\n\n        console.log('登录状态检查:', { hasToken: !!token, hasUserInfo: !!userInfo, isLoggedIn });\n\n        // #ifdef H5 || APP-PLUS\n        if (!isLoggedIn && redirectToLogin) {\n          toLogin();\n          return false;\n        }\n        // #endif\n\n        return isLoggedIn;\n      } catch (error) {\n        console.warn('检查登录状态失败:', error);\n        return false;\n      }\n    },\n    \n    // 清除定时器\n    clearAllTimers() {\n      if (this.refreshTimer) {\n        clearTimeout(this.refreshTimer);\n        this.refreshTimer = null;\n      }\n    },\n\n\n\n\n\n    showErrorToast(message, duration = 2000) {\n      uni.showToast({\n        title: message,\n        icon: 'none',\n        duration: duration\n      });\n    },\n    \n    loadUserFromCache() {\n      try {\n        // 简化：只从本地存储获取用户信息，避免复杂的store交互\n        const cachedUserInfo = uni.getStorageSync('USER_INFO');\n\n        console.log('本地缓存用户信息:', cachedUserInfo);\n\n        if (cachedUserInfo) {\n          let parsedInfo = cachedUserInfo;\n\n          // 处理字符串格式的缓存数据\n          if (typeof cachedUserInfo === 'string') {\n            try {\n              parsedInfo = JSON.parse(cachedUserInfo);\n            } catch (e) {\n              console.error('解析缓存用户信息失败:', e);\n              return;\n            }\n          }\n\n          // 验证数据完整性\n          if (parsedInfo && typeof parsedInfo === 'object' && parsedInfo.uid) {\n            // 直接设置到本地，避免store循环引用\n            this.userInfo = { ...this.userInfo, ...parsedInfo };\n            console.log('用户信息加载成功:', parsedInfo);\n\n            // 用户信息加载成功后，检查是否需要加载动态数据\n            if (this.list.length === 0 && !this.loading.dynamicList) {\n              console.log('用户信息加载成功，开始加载动态数据');\n              this.loadData();\n            }\n          } else {\n            console.warn('缓存的用户信息数据不完整:', parsedInfo);\n            this.resetUserInfo();\n          }\n        } else {\n          console.log('没有本地缓存用户信息');\n          this.resetUserInfo();\n        }\n      } catch (e) {\n        console.error('读取缓存用户信息失败:', e);\n        this.resetUserInfo();\n      }\n    },\n\n\n    \n\n    \n    handleLoginStateChanged(isLoggedIn) {\n      if (isLoggedIn) {\n        // 登录状态变更为已登录，刷新数据\n        this.loadData();\n      } else {\n        // 登录状态变更为未登录，重置用户信息\n        this.resetUserInfo();\n      }\n    },\n    \n    resetUserInfo() {\n      this.userInfo = {\n        avatar: '/static/img/avatar.png',\n        nickname: '您还未登录哦~',\n        about_me: '登录后查看更多内容',\n        follow_count: 0,\n        fans_count: 0,\n        like_count: 0,\n        like_count_str: '0',\n        visitor_count: 0,\n        visitor_badge: 0,\n        is_verified: 0,\n        is_money_level: 0,\n        svip_open: 0,\n        sex: 0,\n        user_id_number: '',\n        interest_tags: [],\n        vip_status: 0,\n        overdue_time: '',\n        card_count: 0,\n        activity_count: 0,\n        activity_img: ''\n      };\n\n      // 重置数据状态\n      this.list = [];\n      this.isEmpty = true;\n      this.loadStatus = 'more';\n      this.page = 1;\n\n      // 重置加载状态\n      Object.keys(this.loading).forEach(key => {\n        this.loading[key] = false;\n      });\n\n      // 重置刷新时间\n      this.lastRefreshTime = 0;\n\n      // 重置侧边栏菜单\n      this.sidebarMenu = [];\n    },\n    \n    handleApiError(err) {\n      if (err && (err.statusCode === 401 || err.code === 401 || err.status === 401)) {\n        uni.showToast({\n          title: '登录信息已过期，请重新登录',\n          icon: 'none',\n          duration: 2000\n        });\n        this.resetUserInfo();\n        setTimeout(() => toLogin(), 1500);\n        return true;\n      }\n      return false;\n    },\n    \n\n    \n\n    \n    updateStoreState() {\n      // 使用Pinia更新状态\n      try {\n        // 更新应用状态\n        this.appStore.setCurrentMsg(true);\n      } catch (error) {\n        console.warn('更新 store 状态失败:', error.message);\n      }\n    },\n    \n    barClick(e) {\n      if (this.loading.dynamicList) return;\n\n      const newBarIdx = parseInt(e.currentTarget.dataset.idx);\n      if (newBarIdx === this.barIdx) return;\n\n      console.log('切换标签:', {\n        from: this.barIdx,\n        to: newBarIdx,\n        fromName: this.barList[this.barIdx],\n        toName: this.barList[newBarIdx]\n      });\n\n      this.barIdx = newBarIdx;\n      this.page = 1;\n\n      // 重置状态\n      this.list = [];\n      this.isEmpty = true;\n      this.loadStatus = 'loading';\n\n      // 加载新标签的数据\n      this.loadDynamicList();\n    },\n    \n    delClick(e) {\n      uni.showModal({\n        content: '确定删除该笔记吗？',\n        confirmColor: '#FA5150',\n        success: (res) => {\n          if (res.confirm) {\n            deleteDynamic(this.list[e.idx].id).then(res => {\n              if (res.status == 200) {\n                this.list.splice(e.idx, 1);\n                if (this.list.length <= 0) {\n                  this.isEmpty = true;\n                }\n                uni.showToast({title: '删除成功', icon: 'success'});\n              } else {\n                uni.showToast({title: res.msg || '删除失败', icon: 'none'});\n              }\n            }).catch(() => {\n              uni.showToast({title: '删除失败，请重试', icon: 'none'});\n            });\n          }\n        }\n      });\n    },\n    \n    likePopupClick(open) {\n      if (open) {\n        this.$refs.likePopup.open();\n      } else {\n        this.$refs.likePopup.close();\n      }\n    },\n    \n    navigateToFun(e) {\n      // 统一登录状态检查\n      if (!this.checkLoginStatus(true)) {\n        return;\n      }\n      \n      if (this.showSidebar) {\n        this.showSidebar = false;\n      }\n      \n      const url = e.currentTarget.dataset.url;\n      if (url === 'center/visitor') {\n        this.getVisitorList();\n      }\n      \n      uni.navigateTo({url: '/pages/' + url});\n    },\n    \n    toFollowList(type) {\n      // 统一登录状态检查\n      if (!this.checkLoginStatus(true)) {\n        return;\n      }\n      \n      uni.navigateTo({\n        url: `/pages/center/follow?type=${type}&id=${this.userInfo.uid}&name=${this.userInfo.nickname}`\n      });\n    },\n    \n    userClick() {\n      this.blockList[0].img = this.userInfo.circle_img || '';\n      this.blockList[0].count = this.userInfo.circle_count || 0;\n      this.blockList[1].img = this.userInfo.cart_img || '';\n      this.blockList[1].count = this.userInfo.cart_count || 0;\n      this.blockList[2].img = this.userInfo.order_img || '';\n      this.blockList[2].count = this.userInfo.order_count || 0;\n    },\n    \n    navigationBarColor(status) {\n      uni.setNavigationBarColor({\n        frontColor: status ? '#000000' : '#ffffff',\n        backgroundColor: 'transparent',\n        animation: {duration: 300, timingFunc: 'easeIn'}\n      });\n    },\n    \n\n\n    toggleSidebar() {\n      this.showSidebar = !this.showSidebar;\n      // 设置滚动状态（如果需要的话可以通过Pinia管理）\n      \n      // #ifdef H5\n      document.body.style.overflow = this.showSidebar ? 'hidden' : '';\n      // #endif\n    },\n\n    getVisitorList() {\n      // 统一通过Pinia更新访客信息\n      const updatedInfo = {\n        ...this.userStore.userInfo,\n        visitor_badge: 0\n      };\n      this.userStore.updateUserInfo(updatedInfo);\n      this.userInfo = { ...this.userInfo, ...updatedInfo };\n\n      getVisitorDetails({page: 1, limit: 20, type: 0}).then((res) => {\n        const emptyData = {visitors: [], total: 0, has_more: false};\n\n        if (res.status === 200 || res.code === 200) {\n          const resData = res.data || {};\n          const visitorInfo = {\n            ...this.userStore.userInfo,\n            visitor_count: resData.total || 0,\n            visitor_badge: 0\n          };\n          this.userStore.updateUserInfo(visitorInfo);\n          this.userInfo = { ...this.userInfo, ...visitorInfo };\n\n          // 计算是否还有更多数据\n          const hasMore = resData.page * resData.limit < resData.total;\n\n          uni.$emit('updateVisitorList', {\n            visitors: resData.list || [],\n            total: resData.total || 0,\n            has_more: hasMore,\n            page: resData.page || 1,\n            limit: resData.limit || 20\n          });\n        } else {\n          this.userInfo.visitor_count = 0;\n          this.userInfo.visitor_badge = 0;\n          this.$store.commit(\"UPDATE_USERINFO\", this.userInfo);\n          uni.$emit('updateVisitorList', emptyData);\n        }\n      }).catch((error) => {\n        console.error('获取访客列表失败:', error);\n        const errorInfo = {\n          ...this.userStore.userInfo,\n          visitor_count: 0,\n          visitor_badge: 0\n        };\n        this.userStore.updateUserInfo(errorInfo);\n        this.userInfo = { ...this.userInfo, ...errorInfo };\n        uni.$emit('updateVisitorList', {visitors: [], total: 0, has_more: false});\n      });\n    },\n\n    setVisit() {\n      setVisit({url: '/pages/tabbar/center'}).catch(() => {});\n    },\n\n    formatDate(timestamp) {\n      if (!timestamp) return '';\n      const date = new Date(timestamp * 1000);\n      return `${date.getFullYear()}-${('0' + (date.getMonth() + 1)).slice(-2)}-${('0' + date.getDate()).slice(-2)}`;\n    },\n    \n    goToVipPage() {\n      // 统一登录状态检查\n      if (!this.checkLoginStatus(true)) {\n        return;\n      }\n      uni.navigateTo({url: '/pages/annex/vip_paid/index'});\n    },\n\n    handleUserInfoUpdate() {\n      // 直接从Pinia store同步最新的用户信息\n      if (this.userStore.userInfo && this.userStore.userInfo.uid) {\n        this.userInfo = { ...this.userInfo, ...this.userStore.userInfo };\n        this.userClick();\n      } else {\n        // 如果Pinia中没有数据，再从缓存加载\n        this.loadUserFromCache();\n      }\n    },\n\n    handleBottomNav(type) {\n      // 统一登录状态检查\n      if (!this.checkLoginStatus(true)) {\n        return;\n      }\n\n      this.showSidebar = false;\n\n      switch(type) {\n        case 'scan':\n          // #ifdef APP-PLUS || MP-WEIXIN\n          uni.scanCode({\n            success: (res) => {\n              if(res.result) {\n                uni.showToast({title: '扫码成功', icon: 'success'});\n              }\n            },\n            fail: () => {\n              uni.showToast({title: '扫码失败', icon: 'none'});\n            }\n          });\n          // #endif\n          \n          // #ifdef H5\n          uni.showToast({title: 'H5环境不支持扫码功能', icon: 'none'});\n          // #endif\n          break;\n          \n        case 'help':\n          uni.navigateTo({url: '/pages/setting/service'});\n          break;\n          \n        case 'setting':\n          uni.navigateTo({url: '/pages/setting/index'});\n          break;\n      }\n    },\n\n    // 模板辅助方法\n    getEmptyTitle() {\n      const titles = ['暂无笔记内容', '暂无喜欢的内容'];\n      return titles[this.barIdx] || '暂无内容';\n    },\n\n    getEmptySubtitle() {\n      if (this.barIdx === 1) {\n        return '快在推荐中寻找更多笔记吧';\n      }\n      return '发笔记，记录灵感日常';\n    },\n\n    handleLogin() {\n      toLogin();\n    }\n  },\n  onReachBottom() {\n    // 未登录时不加载更多\n    if (!this.isLogin) {\n      console.log('未登录，停止加载更多');\n      return;\n    }\n\n    // 防止重复加载或已经加载完所有数据\n    if (this.loading.dynamicList || this.loadStatus === 'noMore') {\n      return;\n    }\n\n    // 检查是否还有更多数据\n    if (this.list.length < this.totalCount) {\n      this.page++;\n      this.loadDynamicList();\n    }\n  },\n  onPageScroll(e) {\n    if (this.showSidebar) {\n      return;\n    }\n    \n    this.scrollTop = e.scrollTop;\n    \n    const threshold = this.statusBarHeight + this.titleBarHeight + 80;\n    if (this.scrollTop <= threshold) {\n      this.navbarTrans = Math.min(this.scrollTop / threshold, 1);\n    } else {\n      this.navbarTrans = 1;\n    }\n    \n    if (this.scrollTop > threshold) {\n      this.navigationBarColor(1);\n    } else {\n      this.navigationBarColor(0);\n    }\n  }\n}\n</script>\n\n<style>\n.nav-box{\n  position:fixed;\n  z-index:99;\n  top:0;\n  left:0;\n  width:100%;\n  box-sizing:border-box;\n  transition:all .3s ease-in-out\n}\n.nav-box .nav-item{\n  position: relative;\n  width: 100%;\n  justify-content: center;\n  align-items: center;\n  height: 100%;\n}\n.nav-box .nav-item .ohto{\n  max-width: 420rpx;\n  font-size: 26rpx;\n  font-weight: 700;\n  transition: all 0.3s ease-in-out;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n}\n.nav-user-avatar {\n  width: 48rpx;\n  height: 48rpx;\n  border-radius: 50%;\n  margin-right: 12rpx;\n}\n.user-box{\n  width:calc(100% - 60rpx);\n  padding:60rpx 30rpx;\n  color:#fff;\n  position:relative;\n  overflow:hidden\n}\n.user-box .user-img,\n.user-box .user-bg{\n  position:absolute;\n  top:0;\n  left:0;\n  width:100%;\n  height:100%\n}\n.user-box .user-bg{\n  z-index:-1;\n  /* 优化性能：条件编译backdrop-filter */\n  /* #ifndef APP-PLUS */\n  -webkit-backdrop-filter:saturate(150%) blur(25px);\n  backdrop-filter:saturate(150%) blur(25px);\n  /* #endif */\n  background:rgba(0,0,0,.6)\n}\n.user-box .user-top{\n  width:100%;\n  justify-content:space-between\n}\n.user-top .avatar{\n  width:140rpx;\n  height:140rpx;\n  border-radius:50%;\n  background:#fff;\n  border:2px solid #f5f5f5;\n  overflow:hidden\n}\n.user-box .user-name{\n  margin:20rpx 0 10rpx;\n  width:100%;\n  font-size:34rpx;\n  font-weight:700\n}\n.user-box .user-intro{\n  width:100%;\n  word-break:break-word;\n  white-space:pre-line\n}\n.user-box .user-intro text{\n  color:#ccc;\n  font-size:24rpx;\n  font-weight:400\n}\n.user-box .user-tag{\n  margin:20rpx 0;\n  width:100%\n}\n.user-tag .tag-item{\n  margin-right:16rpx;\n  height:44rpx;\n  padding:0 14rpx;\n  border-radius:8rpx;\n  background:rgba(255,255,255,.15);\n  font-weight:500;\n  font-size:20rpx;\n  justify-content:center\n}\n.user-tag .tag-item image{\n  width:24rpx;\n  height:24rpx\n}\n.user-num-wrap {\n  width: 100%;\n  justify-content: space-between;\n  align-items: center;\n  margin-top: 20rpx;\n}\n.user-num {\n  flex: 1;\n}\n.user-num .num-item {\n  margin-right: 30rpx;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n}\n.user-num .num-item .t1 {\n  color: #fff;\n  font-size: 32rpx;\n  font-weight: 700;\n  margin-bottom: 8rpx;\n}\n.user-num .num-item .t2 {\n  font-size: 20rpx;\n  font-weight: 300;\n  color: #ccc;\n}\n.visitor-item {\n  position: relative;\n}\n.visitor-item .badge {\n  position: absolute;\n  top: -12rpx;\n  right: -28rpx;\n  min-width: 32rpx;\n  height: 32rpx;\n  padding: 0 6rpx;\n  background: #ff3a3a;\n  color: #fff;\n  border-radius: 16rpx;\n  font-size: 20rpx;\n  text-align: center;\n  line-height: 32rpx;\n  z-index: 2;\n  box-shadow: 0 2rpx 4rpx rgba(0,0,0,0.2);\n}\n.user-actions {\n  align-items: center;\n}\n.btn-item {\n  padding: 0 30rpx;\n  height: 64rpx;\n  line-height: 64rpx;\n  border-radius: 8rpx;\n  font-size: 20rpx;\n  font-weight: 700;\n}\n.bg1 {\n  color: #fff;\n  background: rgba(255,255,255,.15);\n}\n.btn-icon {\n  width: 64rpx;\n  height: 64rpx;\n  border-radius: 8rpx;\n  background: #fff;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-left: 20rpx;\n}\n.btn-icon image {\n  width: 32rpx;\n  height: 32rpx;\n}\n.user-block{\n  margin-top:-30rpx;\n  width:100%;\n  white-space:nowrap;\n  background:#fff;\n  border-radius:30rpx 30rpx 0 0\n}\n.user-block .block-box{\n  width:100%;\n  padding:30rpx 15rpx;\n  display:flex\n}\n.block-box .block-item{\n  flex-shrink:0;\n  margin:0 15rpx;\n  padding:24rpx;\n  background:#f8f8f8;\n  border-radius:16rpx;\n  justify-content:space-between;\n  position:relative\n}\n.block-item .block-title .t1{\n  font-size:26rpx;\n  font-weight:700\n}\n.block-item .block-title .t2{\n  margin-top:4rpx;\n  color:#999;\n  font-size:16rpx;\n  font-weight:300\n}\n.block-item .cu-group{\n  position:relative;\n  right:38rpx\n}\n.cu-group .cu-item{\n  z-index:3;\n  width:68rpx;\n  height:68rpx;\n  border-radius:8rpx;\n  overflow:hidden\n}\n.cu-group .cu-item .icon{\n  margin:18rpx;\n  width:32rpx;\n  height:32rpx\n}\n.cu-group .cu-item .img{\n  width:100%;\n  height:100%\n}\n.cu-group .cu-lump2{\n  position:absolute;\n  z-index:2;\n  left:18rpx;\n  width:58rpx;\n  height:58rpx;\n  border-radius:8rpx;\n  background:#dbdbdb\n}\n.cu-group .cu-lump1{\n  position:absolute;\n  z-index:1;\n  left:38rpx;\n  width:48rpx;\n  height:48rpx;\n  border-radius:8rpx;\n  background:#eaeaea\n}\n.block-item .block-icon{\n  position:absolute;\n  right:12rpx;\n  width:20rpx;\n  height:20rpx;\n  transform:rotate(-90deg)\n}\n.bar-box{\n  position:sticky;\n  left:0;\n  z-index:99;\n  margin-top:-1px;\n  width:100%;\n  height:80rpx;\n  background:#fff\n}\n.bar-box .bar-item{\n  padding:0 30rpx;\n  height:100%;\n  flex-direction:column;\n  justify-content:center;\n  position:relative\n}\n.bar-box .bar-item text{\n  font-weight:700;\n  transition:all .3s ease-in-out\n}\n.bar-item .bar-line{\n  position:absolute;\n  bottom:12rpx;\n  width:18rpx;\n  height:6rpx;\n  border-radius:6rpx;\n  background:#000;\n  transition:opacity .3s ease-in-out\n}\n/* 优化的CSS类名 */\n.content-container{\n  padding-bottom:180rpx\n}\n.like-popup{\n  background:#fff;\n  width:400rpx;\n  padding:30rpx;\n  border-radius:30rpx;\n  overflow:hidden\n}\n.like-popup .like-img{\n  margin:0 40rpx;\n  width:320rpx;\n  height:200rpx\n}\n.like-popup .like-content{\n  margin:20rpx 0 40rpx;\n  width:100%;\n  color:#333;\n  font-size:26rpx;\n  text-align:center\n}\n.like-popup .like-btn{\n  width:100%;\n  height:80rpx;\n  line-height:80rpx;\n  text-align:center;\n  font-size:24rpx;\n  font-weight:700;\n  color:#fff;\n  background:#000;\n  border-radius:16rpx\n}\n.df {\n  display: flex;\n  align-items: center;\n}\n.ohto {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n.empty-state {\n  flex-direction: column;\n  padding: 120rpx 0;\n}\n.empty-state image {\n  width: 280rpx;\n  height: 280rpx;\n}\n.empty-state .empty-title {\n  margin-top: 40rpx;\n  font-size: 32rpx;\n  font-weight: bold;\n}\n.empty-state .empty-subtitle {\n  margin-top: 20rpx;\n  font-size: 24rpx;\n  color: #999;\n}\n\n.login-btn {\n  margin-top: 40rpx;\n  padding: 24rpx 48rpx;\n  background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);\n  border-radius: 48rpx;\n  color: #fff;\n  font-size: 28rpx;\n  font-weight: 500;\n  box-shadow: 0 4rpx 16rpx rgba(139, 92, 246, 0.3);\n}\n\n/* 重试按钮样式已删除 */\n\n.loading-state {\n  flex-direction: column;\n  padding: 120rpx 0;\n}\n\n.loading-text {\n  margin-top: 20rpx;\n  font-size: 24rpx;\n  color: #999;\n}\n.loading-indicator {\n  justify-content: center;\n}\n.nav-menu-btn {\n  position: absolute;\n  left: 30rpx;\n  width: 48rpx;\n  height: 48rpx;\n  border-radius: 50%;\n  background: rgba(0,0,0,0.5);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 100;\n  -webkit-tap-highlight-color: transparent;\n}\n.nav-menu-btn image {\n  width: 24rpx;\n  height: 24rpx;\n}\n.nav-menu-btn:active {\n  background: rgba(0,0,0,0.5);\n}\n.sidebar-menu {\n  position: fixed;\n  top: 0;\n  left: -75%;\n  width: 75%;\n  height: 100%;\n  max-height: 100vh;\n  background: #fff;\n  z-index: 999;\n  box-shadow: 2rpx 0 10rpx rgba(0,0,0,0.1);\n  transform: translateX(0);\n  transition: transform 0.3s ease-in-out;\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n}\n\n.sidebar-menu.active {\n  transform: translateX(100%);\n}\n\n.sidebar-header {\n  flex-shrink: 0;\n  padding: 30rpx;\n  border-bottom: 1px solid #f5f5f5;\n  position: relative; /* 为关闭按钮提供定位基准 */\n}\n\n.sidebar-user-info {\n  display: flex;\n  align-items: center;\n}\n\n.sidebar-avatar {\n  width: 80rpx;\n  height: 80rpx;\n  border-radius: 50%;\n  margin-right: 20rpx;\n}\n\n.sidebar-user-details {\n  flex: 1;\n  overflow: hidden;\n}\n\n.sidebar-user-name {\n  font-size: 30rpx;\n  font-weight: 500;\n  margin-bottom: 8rpx;\n}\n\n.user-status {\n  display: flex;\n  align-items: center;\n  flex-wrap: wrap;\n}\n\n.status-item {\n  display: flex;\n  align-items: center;\n  padding: 4rpx 10rpx;\n  border-radius: 20rpx;\n  margin-right: 12rpx;\n  font-size: 20rpx;\n  margin-top: 4rpx;\n}\n\n.status-icon {\n  width: 80rpx;\n  height: 40rpx;\n  margin-right: 8rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.status-icon image {\n  width: 80rpx;\n  height: 36rpx;\n  display: block; /* 确保图片正确显示 */\n}\n\n\n.member-card {\n  flex-shrink: 0;\n  margin: 0 30rpx 20rpx;\n  padding: 30rpx 20rpx;\n  background: #2c2c2c;\n  border-radius: 16rpx;\n  color: #fff;\n}\n\n.member-status {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16rpx;\n}\n\n.member-label {\n  font-size: 30rpx;\n  font-weight: bold;\n}\n\n.member-price {\n  padding: 6rpx 20rpx;\n  background: #fff;\n  color: #333;\n  border-radius: 30rpx;\n  font-size: 22rpx;\n}\n\n.member-benefits {\n  margin-bottom: 10rpx;\n}\n\n.member-rights {\n  font-size: 22rpx;\n  color: rgba(255,255,255,0.8);\n}\n\n.member-desc {\n  margin-top: 16rpx;\n  font-size: 22rpx;\n  color: rgba(255,255,255,0.7);\n}\n\n.sidebar-scroll {\n  flex: 1;\n  overflow-y: auto;\n  -webkit-overflow-scrolling: touch;\n  overscroll-behavior: contain;\n}\n\n.sidebar-content {\n  padding: 16rpx 0 200rpx;\n  background-color: #f7f7f7;\n}\n\n/* 菜单部分样式 */\n.menu-section {\n  margin: 20rpx;\n  background-color: #fff;\n  border-radius: 16rpx;\n  overflow: hidden;\n  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);\n}\n\n.section-title {\n  padding: 20rpx 30rpx 10rpx;\n  font-size: 28rpx;\n  font-weight: bold;\n  color: #333;\n  border-bottom: 1px solid #f5f5f5;\n}\n\n/* 菜单宫格样式 */\n.menu-grid {\n  display: flex;\n  flex-wrap: wrap;\n  padding: 10rpx;\n  background-color: #fff;\n}\n\n.grid-item {\n  width: 33.33%;\n  text-align: center;\n  padding: 20rpx 0;\n  position: relative;\n}\n\n.grid-item:active {\n  background-color: #f8f8f8;\n}\n\n.grid-icon-wrapper {\n  position: relative;\n  margin: 0 auto 10rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.grid-icon {\n  width: 50rpx;\n  height: 50rpx;\n}\n\n.grid-badge {\n  position: absolute;\n  top: -6rpx;\n  right: -6rpx;\n  min-width: 32rpx;\n  height: 32rpx;\n  padding: 0 6rpx;\n  background: #ff3a3a;\n  color: #fff;\n  border-radius: 16rpx;\n  font-size: 20rpx;\n  text-align: center;\n  line-height: 32rpx;\n  z-index: 2;\n  box-shadow: 0 2rpx 4rpx rgba(0,0,0,0.2);\n}\n\n.grid-text {\n  font-size: 24rpx;\n  color: #333;\n  display: block;\n  padding: 0 10rpx;\n}\n\n.sidebar-footer {\n  flex-shrink: 0;\n  background: #fff;\n}\n\n.bottom-nav {\n  width: 100%;\n  height: 120rpx;\n  justify-content: space-around;\n  padding: 0;\n  background-color: #f7f7f7;\n}\n\n.bottom-nav-item {\n  flex: 1;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  height: 100%;\n}\n\n.nav-icon-box {\n  width: 50rpx;\n  height: 50rpx;\n  justify-content: center;\n  align-items: center;\n}\n\n.nav-icon {\n  width: 44rpx;\n  height: 44rpx;\n}\n\n.nav-text {\n  font-size: 22rpx;\n  color: #666;\n  margin-top: 8rpx;\n}\n\n.copyright-text {\n  text-align: center;\n  color: #999;\n  font-size: 20rpx;\n  padding: 10rpx 0 30rpx;\n}\n\n.sidebar-mask {\n  position: fixed;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  background: rgba(0,0,0,0.5);\n  z-index: 998;\n  touch-action: none;\n}\n\n.no-scroll {\n  overflow: hidden !important;\n}\n\n.container.no-scroll,\n.container[style*=\"position: fixed\"] {\n  position: fixed !important;\n  left: 0 !important;\n  width: 100% !important;\n  overflow: hidden !important;\n  touch-action: none !important;\n  height: 100vh !important;\n}\n\n.container[style*=\"position: fixed\"] .user-box,\n.container[style*=\"position: fixed\"] .nav-box {\n  transform: none !important;\n}\n\n@media screen and (max-height: 667px) {\n  .sidebar-scroll {\n    height: calc(100vh - 350rpx - 170rpx);\n  }\n}\n\n.sidebar-item:active {\n  background-color: #f8f8f8;\n}\n\n.menu-group {\n  margin-bottom: 0;\n  margin: 0 20rpx;\n  border-radius: 16rpx;\n  background-color: #fff;\n  overflow: hidden;\n}\n\n.menu-divider {\n  height: 16rpx;\n  background-color: #f7f7f7;\n  margin: 0;\n  width: 100%;\n}\n\n.close-btn {\n  position: absolute;\n  right: 30rpx;\n  top: 50%;\n  transform: translateY(-50%) rotate(45deg);\n  width: 48rpx;\n  height: 48rpx;\n  border-radius: 50%;\n  background: #f8f8f8;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 10;\n}\n\nbutton, \n.btn, \n.nav-menu-btn, \nview[role=\"button\"] {\n  -webkit-touch-callout: none;\n  -webkit-user-select: none;\n  -khtml-user-select: none;\n  -moz-user-select: none;\n  -ms-user-select: none;\n  user-select: none;\n  -webkit-tap-highlight-color: transparent;\n}\n\n.user-top {\n  width: 100%;\n  align-items: flex-start;\n  margin-bottom: 20rpx;\n}\n\n.avatar-wrapper {\n  position: relative;\n  margin-right: 20rpx;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.avatar {\n  width: 140rpx;\n  height: 140rpx;\n  border-radius: 50%;\n  background: #fff;\n  border: 2px solid #f5f5f5;\n  overflow: hidden;\n  position: relative;\n}\n\n.profile-percent {\n  position: absolute;\n  left: 50%;\n  top: 0;\n  transform: translateX(-50%);\n  background: #ff6600;\n  border-radius: 12rpx;\n  padding: 4rpx 10rpx;\n  display: flex;\n  align-items: center;\n  font-size: 20rpx;\n  color: #fff;\n  z-index: 2;\n  box-shadow: 0 2rpx 4rpx rgba(0,0,0,0.2);\n}\n\n.edit-icon {\n  width: 24rpx;\n  height: 24rpx;\n  margin-right: 4rpx;\n}\n\n.user-info {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  padding-top: 20rpx;\n}\n\n.user-name-row {\n  align-items: center;\n  margin-bottom: 10rpx;\n}\n\n.user-name-text {\n  font-size: 34rpx;\n  font-weight: 700;\n  color: #fff;\n  margin-right: 12rpx;\n}\n\n.status-icon {\n  width: 80rpx;\n  height: 40rpx;\n  margin-right: 8rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.status-icon image {\n  width: 80rpx;\n  height: 36rpx;\n  display: block; /* 确保图片正确显示 */\n}\n\n.user-id {\n  font-size: 22rpx;\n  color: rgba(255,255,255,0.6);\n}\n\n.user-id-row {\n  align-items: center;\n  margin-top: 8rpx;\n}\n\n.sex-icon {\n  width: 32rpx;\n  height: 32rpx;\n  border-radius: 6rpx;\n  background: rgba(255,255,255,0.15);\n  align-items: center;\n  justify-content: center;\n  margin-right: 12rpx;\n}\n\n.sex-icon image {\n  width: 20rpx;\n  height: 20rpx;\n}\n\n.vip-icon {\n  border-radius: 6rpx;\n  padding: 2rpx;\n}\n\n.verified-icon {\n  border-radius: 6rpx;\n  padding: 2rpx;\n}\n\n.tag-wrapper {\n  position: relative;\n  width: 100%;\n  box-sizing: border-box;\n  display: flex;\n  align-items: center;\n}\n\n.tag-scroll-view {\n  width: calc(100% - 70rpx);\n  white-space: nowrap;\n}\n\n.tag-add-btn {\n  position: absolute;\n  right: 0rpx;\n  width: 46rpx;\n  height: 46rpx;\n  border-radius: 46rpx;\n  background: #f0f0f0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.tag-add-btn text {\n  font-size: 40rpx;\n  color: #666;\n  font-weight: 300;\n}\n\n.tag-add-empty {\n  width: 100%;\n  height: 60rpx;\n  background: #f8f8f8;\n  border-radius: 30rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.tag-add-empty text {\n  color: #999;\n  font-size: 26rpx;\n}\n\n.tag-empty {\n  flex: 1;\n  height: 60rpx;\n  background: rgba(255,255,255,.15);\n  border-radius: 30rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 20rpx;\n}\n\n.tag-empty text {\n  color: rgba(255,255,255,0.6);\n  font-size: 24rpx;\n}\n\n.user-tag {\n  flex-wrap: nowrap;\n  padding: 0;\n  margin-top: 0;\n}\n\n.tag-wrapper .user-tag {\n  display: inline-flex;\n  padding-right: 20rpx;\n}\n\n.tag-wrapper .tag-item {\n  margin-right: 20rpx;\n  flex-shrink: 0;\n}\n\n.menu-grid {\n  display: flex;\n  flex-wrap: wrap;\n  padding: 20rpx 10rpx;\n  background-color: #fff;\n}\n\n.grid-item {\n  width: 33.33%;\n  text-align: center;\n  padding: 20rpx 0;\n  position: relative;\n}\n\n.grid-icon {\n  width: 50rpx;\n  height: 50rpx;\n}\n\n.grid-badge {\n  position: absolute;\n  top: -6rpx;\n  right: -6rpx;\n  min-width: 32rpx;\n  height: 32rpx;\n  padding: 0 6rpx;\n  background: #ff3a3a;\n  color: #fff;\n  border-radius: 16rpx;\n  font-size: 20rpx;\n  text-align: center;\n  line-height: 32rpx;\n  z-index: 2;\n  box-shadow: 0 2rpx 4rpx rgba(0,0,0,0.2);\n}\n\n.grid-text {\n  font-size: 24rpx;\n  color: #333;\n  display: block;\n  padding: 0 10rpx;\n}\n\n.profile-progress {\n  position: absolute;\n  top: -8rpx;\n  left: -8rpx;\n  right: -8rpx;\n  bottom: -8rpx;\n  z-index: 10;\n}\n\n.progress-circle {\n  width: 100%;\n  height: 100%;\n  border-radius: 50%;\n  position: relative;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.progress-inner {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  background: rgba(0,0,0,0.6);\n  border-radius: 20rpx;\n  padding: 4rpx 8rpx;\n  backdrop-filter: blur(10rpx);\n}\n\n.progress-text {\n  color: #fff;\n  font-size: 20rpx;\n  font-weight: 700;\n  line-height: 1;\n}\n\n.avatar {\n  position: relative;\n}\n\n.user-box .user-intro{\n  width:100%;\n  position: relative;\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n}\n.user-box .user-intro .intro-text{\n  color:#ccc;\n  font-size:24rpx;\n  font-weight:400;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 2;\n  line-clamp: 2;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: normal;\n  word-break: break-all;\n  flex: 1;\n}\n.user-box .user-intro .more-btn{\n  position: absolute;\n  right: 0;\n  top: 0;\n  width: 50rpx;\n  height: 50rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n.user-box .user-intro .more-btn image{\n  width: 32rpx;\n  height: 32rpx;\n}\n\n.user-box .user-top{\n  width:100%;\n  justify-content:space-between;\n  position: relative;\n  padding-right: 40rpx;\n}\n.user-box .user-top .right-arrow {\n  position: absolute;\n  right: 0;\n  top: 50%;\n  display: flex;\n  -webkit-transform: translateY(-50%) rotate(270deg);\n  transform: translateY(-50%) rotate(270deg)\n}\n.user-box .user-top .right-arrow image {\n  width: 140rpx;\n  height: 40rpx;\n}\n\n.stat-box {\n  display: flex;\n  justify-content: space-around;\n  align-items: center;\n  width: calc(100% - 60rpx);\n  padding: 20rpx 0;\n  background-color: rgba(0, 0, 0, 0.6);\n  border-radius: 20rpx;\n  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);\n  margin: 20rpx auto;\n  position: relative;\n}\n\n.stat-divider {\n  width: 1px;\n  height: 60rpx;\n  background-color: rgba(255, 255, 255, 0.2);\n}\n\n.stat-item {\n  position: relative;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  flex: 1;\n  text-align: center;\n  padding: 10rpx 0;\n}\n\n.stat-icon {\n  width: 40rpx;\n  height: 40rpx;\n  margin-bottom: 16rpx;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.stat-icon .iconfont {\n  font-size: 36rpx;\n}\n\n.stat-value {\n  font-size: 36rpx;\n  font-weight: 700;\n  color: #ffffff;\n  margin-bottom: 8rpx;\n  line-height: 1;\n}\n\n.stat-label {\n  font-size: 22rpx;\n  color: rgba(255, 255, 255, 0.7);\n  line-height: 1;\n}\n\n.user-num-wrap {\n  width: 100%;\n}\n\n.stat-badge {\n  position: absolute;\n  top: -8rpx;\n  right: -10rpx;\n  min-width: 30rpx;\n  height: 30rpx;\n  line-height: 30rpx;\n  padding: 0 6rpx;\n  background-color: #ff3a3a;\n  color: #ffffff;\n  font-size: 16rpx;\n  border-radius: 15rpx;\n  text-align: center;\n}\n\n.stat-icon .icon-like {\n  color: #ff7ca8;\n  font-size: 42rpx;\n}\n\n.stat-icon .icon-eye {\n  color: #e3d6ff;\n  font-size: 42rpx;\n}\n\n.stat-icon .icon-heart {\n  color: #e3d6ff;\n  font-size: 42rpx;\n}\n\n.stat-like-text {\n  color: #ff7ca8;\n  font-size: 42rpx;\n}\n\n.stat-eye-text {\n  color: #e3d6ff;\n  font-size: 42rpx;\n}\n\n/* 空菜单提示样式 */\n.empty-menu-tip {\n  padding: 60rpx 0;\n  text-align: center;\n  color: #999;\n  font-size: 24rpx;\n}\n\n.stat-heart-text {\n  color: #e3d6ff;\n  font-size: 42rpx;\n}\n</style>", "import MiniProgramPage from 'Z:/WWW/shejiao/vue3/pages/tabbar/center.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useUserStore", "useAppStore", "uni", "getUserSocialInfo", "getLikeDynamicList", "<PERSON><PERSON><PERSON><PERSON>", "deleteDynamic", "res"], "mappings": ";;;;;;;;;AAqSA,MAAA,YAAA,MAAA;AACA,MAAA,cAAA,MAAA;AACA,MAAA,SAAA,MAAA;AACA,MAAA,SAAA,MAAA;AACA,MAAA,SAAA,MAAA;AACA,MAAA,WAAA,MAAA;AASA,MAAA,YAAA;AAAA;IAEI;AAAA;IAEA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;EAEF,UAAA;AAAA;AAEI,aAAAA,YAAA,aAAA;AAAA;;AAGA,aAAAC,WAAA,YAAA;AAAA;;;;IAKF,UAAA;AAEE,UAAA;AACE,cAAA,QAAAC,cAAA,MAAA,eAAA,OAAA,KAAAA,oBAAA,eAAA,oBAAA;AACA,cAAA,WAAAA,cAAAA,MAAA,eAAA,WAAA;;;;UAME,aAAA,QAAA,MAAA,SAAA;AAAA,UACA,UAAA,WAAA,UAAA;AAAA;UAEA,KAAA,WAAA,SAAA,MAAA;AAAA,UACA;AAAA,QACF,CAAA;AAEA,eAAA;AAAA;AAEAA,sBAAA,MAAA,MAAA,QAAA,kCAAA,aAAA,KAAA;;MAEF;AAAA,IACF;AAAA;EAEF,OAAA;AACE,WAAA;AAAA,MACE,iBAAA;AAAA,MACA,gBAAA;AAAA;;;MAIA,UAAA;AAAA,QACE,QAAA;AAAA,QACA,UAAA;AAAA,QACA,cAAA;AAAA;;QAGA,gBAAA;AAAA,QACA,eAAA;AAAA,QACA,eAAA;AAAA;QAEA,gBAAA;AAAA;;QAGA,gBAAA;AAAA;QAEA,eAAA,CAAA;AAAA;QAEA,cAAA;AAAA;QAEA,gBAAA;AAAA,QACA,cAAA;AAAA;;QAGA,EAAA,MAAA,MAAA,KAAA,IAAA,MAAA,sBAAA,KAAA,wBAAA,OAAA,EAAA;AAAA,QACA,EAAA,MAAA,OAAA,KAAA,IAAA,MAAA,uBAAA,KAAA,cAAA,OAAA,EAAA;AAAA,QACA,EAAA,MAAA,MAAA,KAAA,IAAA,MAAA,sBAAA,KAAA,eAAA,OAAA,EAAA;AAAA;MAEF,SAAA,CAAA,MAAA,IAAA;AAAA,MACA,QAAA;AAAA,MACA,MAAA,CAAA;AAAA,MACA,MAAA;AAAA;;;MAGA,YAAA;AAAA,MACA,aAAA;AAAA,MACA,aAAA,CAAA;AAAA,MACA,WAAA;AAAA,MACA,cAAA;AAAA;;MAIA,SAAA;AAAA,QACE,UAAA;AAAA,QACA,aAAA;AAAA,QACA,YAAA;AAAA;;MAIF,iBAAA;AAAA;;;MAIA,aAAA;AAAA,IACF;AAAA;EAEF,oBAAA;AACE,QAAA,CAAA,KAAA,SAAA;;;AAGE;AAAA,IACF;AACA,SAAA,YAAA;AAAA;EAEF,SAAA;;AAKE,SAAA,SAAA;AAAA;;;;AAOA,SAAA,eAAA;AAAA;EAEF,SAAA;;AAIE,QAAA,KAAA,aAAA;;AAEE;AAAA,IACF;AAEAA,kBAAAA,MAAA,MAAA,OAAA,kCAAA,kBAAA;AAAA,MACE,SAAA,KAAA;AAAA;MAEA,SAAA,KAAA;AAAA,MACA,YAAA,KAAA,QAAA,KAAA,MAAA;AAAA,IACF,CAAA;AAGA,eAAA,MAAA;AACE,WAAA,2BAAA;AAAA,IACF,GAAA,GAAA;AAAA;EAEF,SAAA;AAAA;AAAA,IAEE,oBAAA;;;;;;;;IAaA,MAAA,WAAA;;AAIE,YAAA,aAAA,KAAA;AAEAA,oBAAA,MAAA,MAAA,OAAA,kCAAA,aAAA,UAAA;AAEA,UAAA,YAAA;AAEE,cAAA,KAAA;;;MAIF;AAAA;;;;AAQA,YAAA,qBAAA,KAAA;AAEAA,oBAAA,MAAA,MAAA,OAAA,kCAAA,WAAA,kBAAA;;;AAOE,aAAA,aAAA;;AAIEA,wBAAAA,MAAA,MAAA,OAAA,kCAAA,aAAA;;QAEF,WAAA,KAAA,qBAAA;AACE,eAAA,YAAA;AAAA,QACF;AAAA;AAGAA,sBAAAA,MAAA,MAAA,OAAA,kCAAA,cAAA;AACA,aAAA,cAAA;AAAA,MACF;AAAA;;IAIF,mBAAA;AACE,UAAA;AAEE,cAAA,QAAAA,cAAA,MAAA,eAAA,OAAA,KAAAA,oBAAA,eAAA,oBAAA;AACA,cAAA,WAAAA,cAAAA,MAAA,eAAA,WAAA;;;UAIE,UAAA,CAAA,CAAA;AAAA;UAEA,QAAA,CAAA,EAAA,YAAA,SAAA;AAAA,UACA;AAAA,QACF,CAAA;AAEA,eAAA;AAAA;AAEAA,sBAAA,MAAA,MAAA,QAAA,kCAAA,aAAA,KAAA;;MAEF;AAAA;;IAIF,oBAAA;;AAGE,WAAA,UAAA;;AAIA,WAAA,cAAA;;AAaAA,oBAAAA,MAAA,MAAA,OAAA,kCAAA,eAAA;AAAA;;IAKF,MAAA,WAAA;AACE,UAAA;;UAGI,KAAA,aAAA;AAAA,UACA,KAAA,gBAAA;AAAA,QACF,CAAA;;AAMA,aAAA,SAAA;AAEA,aAAA,kBAAA,KAAA;;AAEAA,sBAAA,MAAA,MAAA,SAAA,kCAAA,WAAA,KAAA;AACA,aAAA,eAAA,YAAA;AAAA,MACF;AAAA;;IAIF,oBAAA;AACE,YAAA,MAAA,KAAA;;;;IAKF,MAAA,cAAA;AACE,UAAA,KAAA,QAAA;AAAA;;;AAKA,UAAA;AACE,cAAA,KAAA;;AAEAA,sBAAA,MAAA,MAAA,SAAA,kCAAA,WAAA,KAAA;AAAA,MACF,UAAA;;;MAGA;AAAA;;;AAKAA,0BAAA,MAAA,OAAA,kCAAA,aAAA,KAAA,QAAA,KAAA,MAAA,CAAA;;;AAKA,WAAA,UAAA;;;;;IAQF,MAAA,eAAA;AACE,UAAA,KAAA,QAAA,YAAA,CAAA,KAAA,SAAA;AACEA,sBAAAA,MAAA,MAAA,OAAA,kCAAA,oBAAA;AAAA;UAEE,SAAA,KAAA;AAAA,QACF,CAAA;AACA;AAAA,MACF;;AAIA,UAAA;AACE,cAAA,MAAA,MAAAC,WAAAA;AAEA,YAAA,IAAA,WAAA,OAAA,IAAA,SAAA,KAAA;;AAIE,cAAA,SAAA,eAAA,QAAA;mEAEI,SAAA,aAAA,KAAA,QAAA,CAAA,IAAA,MACA,SAAA,WAAA;UACJ;AAGA,eAAA,UAAA,eAAA,QAAA;;AAIA,eAAA,aAAA,SAAA,eAAA;AAGA,eAAA,UAAA;AAAA;AAEAD,wBAAA,MAAA,MAAA,QAAA,kCAAA,aAAA,GAAA;AAAA,QACF;AAAA;AAEAA,sBAAA,MAAA,MAAA,SAAA,kCAAA,aAAA,KAAA;AACA,YAAA,CAAA,KAAA,eAAA,KAAA,GAAA;AACE,eAAA,eAAA,UAAA;AAAA,QACF;AAAA,MACF,UAAA;;MAEA;AAAA;;;AAKA,UAAA,KAAA,QAAA,eAAA,CAAA,KAAA,SAAA;AACEA,sBAAAA,MAAA,MAAA,OAAA,kCAAA,uBAAA;AAAA,UACE,SAAA,KAAA,QAAA;AAAA,UACA,SAAA,KAAA;AAAA,QACF,CAAA;AACA;AAAA,MACF;;AAIA,UAAA;AACE,cAAA,SAAA,KAAA,UAAA;;UAEE,QAAA,KAAA;AAAA,UACA,MAAA,KAAA;AAAA;UAEA,SAAA,KAAA,WAAA,IAAA,OAAA;AAAA,QACF,CAAA;;;QAIA;;AAGA,YAAA,KAAA,WAAA,GAAA;;YAGI,MAAA,KAAA;AAAA,YACA,OAAA;AAAA,UACF,CAAA;AAAA;AAGA,oBAAAE,WAAA,mBAAA,QAAA;AAAA,YACE,MAAA,KAAA;AAAA,YACA,OAAA;AAAA,UACF,CAAA;AAAA,QACF;AAEA,cAAA,MAAA,MAAA;;AAGA,YAAA,IAAA,WAAA,OAAA,IAAA,MAAA;AACE,gBAAA,UAAA,IAAA,KAAA,QAAA,CAAA;;;;YAKE,aAAA,KAAA;AAAA,UACF,CAAA;AAEA,cAAA,KAAA,SAAA,GAAA;AACE,iBAAA,OAAA;AAAA;;UAGF;AAEA,eAAA,aAAA,IAAA,KAAA,SAAA;AACA,eAAA,UAAA,KAAA,KAAA,WAAA;;;;YAKE,SAAA,KAAA;AAAA;UAEF,CAAA;AAAA;AAEAF,wBAAA,MAAA,MAAA,QAAA,kCAAA,YAAA,GAAA;AACA,cAAA,KAAA,SAAA,GAAA;AACE,iBAAA,UAAA;;UAEF;AAAA,QACF;AAAA;AAEAA,sBAAA,MAAA,MAAA,SAAA,kCAAA,aAAA,KAAA;AACA,YAAA,KAAA,SAAA,GAAA;AACE,eAAA,UAAA;;QAEF;;AAGA,YAAA,CAAA,KAAA,eAAA,KAAA,GAAA;AACE,eAAA,eAAA,UAAA;AAAA,QACF;AAAA,MACF,UAAA;AACE,aAAA,QAAA,cAAA;AAAA,MACF;AAAA;;;AAKA,UAAA;;AAEEA,sBAAA,MAAA,MAAA,OAAA,kCAAA,aAAA,GAAA;AAEA,YAAA,IAAA,WAAA,OAAA,IAAA,MAAA;AAEE,gBAAA,UAAA,IAAA,KAAA,YAAA,CAAA;AACA,gBAAA,aAAA,QAAA;AAEAA,wBAAA,MAAA,MAAA,OAAA,kCAAA,SAAA,UAAA;AAGA,cAAA,eAAA,GAAA;AAEE,kBAAA,WAAA,IAAA,KAAA,oBAAA,CAAA;AACA,iBAAA,cAAA,SAAA,IAAA,WAAA;AAAA;cAEE,MAAA,KAAA;AAAA;;;cAGA,OAAA,KAAA,SAAA;AAAA;AAAA,YACF,EAAA;AAEAA,0BAAA,MAAA,MAAA,OAAA,kCAAA,cAAA,KAAA,WAAA;AAAA;AAEAA,0BAAAA,MAAA,MAAA,OAAA,kCAAA,eAAA;AACA,iBAAA,cAAA;UACF;AAAA,QACF;AAAA;AAEAA,sBAAA,MAAA,MAAA,QAAA,kCAAA,cAAA,KAAA;AAAA,MACF;AAAA;;IAIF,eAAA,OAAA;AACEA,oBAAA,MAAA,MAAA,SAAA,kCAAA,UAAA,KAAA;AAGA,UAAA,MAAA,SAAA,sBAAA;AACE,aAAA,eAAA,kBAAA;AACA,YAAA;AACE,eAAA,UAAA;QACF,SAAA,GAAA;;QAEA;;MAEF;AAGA,UAAA,MAAA,SAAA,iBAAA;AACE,aAAA,eAAA,cAAA;;MAEF;;AAIE,YAAA;;QAEA,SAAA,GAAA;;QAEA;AACA,aAAA,eAAA,aAAA;;;MAGF;;;;IAYF,iBAAA,kBAAA,OAAA;AACE,UAAA;AAEE,cAAA,QAAAA,cAAA,MAAA,eAAA,OAAA,KAAAA,oBAAA,eAAA,oBAAA;AACA,cAAA,WAAAA,cAAAA,MAAA,eAAA,WAAA;;;AAYA,eAAA;AAAA;AAEAA,sBAAA,MAAA,MAAA,QAAA,kCAAA,aAAA,KAAA;;MAEF;AAAA;;IAIF,iBAAA;AACE,UAAA,KAAA,cAAA;AACE,qBAAA,KAAA,YAAA;;MAEF;AAAA;IAOF,eAAA,SAAA,WAAA,KAAA;AACEA,oBAAAA,MAAA,UAAA;AAAA;;QAGE;AAAA,MACF,CAAA;AAAA;IAGF,oBAAA;AACE,UAAA;;AAIEA,sBAAA,MAAA,MAAA,OAAA,kCAAA,aAAA,cAAA;AAEA,YAAA,gBAAA;;AAIE,cAAA,OAAA,mBAAA,UAAA;AACE,gBAAA;AACE,2BAAA,KAAA,MAAA,cAAA;AAAA,YACF,SAAA,GAAA;AACEA,4BAAA,MAAA,MAAA,SAAA,kCAAA,eAAA,CAAA;AACA;AAAA,YACF;AAAA,UACF;;;AAMEA,0BAAA,MAAA,MAAA,OAAA,kCAAA,aAAA,UAAA;;AAIEA,4BAAAA,MAAA,MAAA,OAAA,kCAAA,mBAAA;AACA,mBAAA,SAAA;AAAA,YACF;AAAA;AAEAA,0BAAA,MAAA,MAAA,QAAA,kCAAA,iBAAA,UAAA;AACA,iBAAA,cAAA;AAAA,UACF;AAAA;;AAGA,eAAA,cAAA;AAAA,QACF;AAAA,MACF,SAAA,GAAA;AACEA,sBAAA,MAAA,MAAA,SAAA,kCAAA,eAAA,CAAA;AACA,aAAA,cAAA;AAAA,MACF;AAAA;IAOF,wBAAA,YAAA;AACE,UAAA,YAAA;AAEE,aAAA,SAAA;AAAA;AAGA,aAAA,cAAA;AAAA,MACF;AAAA;IAGF,gBAAA;AACE,WAAA,WAAA;AAAA,QACE,QAAA;AAAA,QACA,UAAA;AAAA,QACA,UAAA;AAAA,QACA,cAAA;AAAA;;QAGA,gBAAA;AAAA,QACA,eAAA;AAAA,QACA,eAAA;AAAA;QAEA,gBAAA;AAAA;;QAGA,gBAAA;AAAA,QACA,eAAA,CAAA;AAAA;QAEA,cAAA;AAAA;QAEA,gBAAA;AAAA,QACA,cAAA;AAAA;;AAKF,WAAA,UAAA;;;AAKA,aAAA,KAAA,KAAA,OAAA,EAAA,QAAA,SAAA;;MAEA,CAAA;;AAMA,WAAA,cAAA;;IAGF,eAAA,KAAA;AACE,UAAA,QAAA,IAAA,eAAA,OAAA,IAAA,SAAA,OAAA,IAAA,WAAA,MAAA;AACEA,sBAAAA,MAAA,UAAA;AAAA,UACE,OAAA;AAAA;;QAGF,CAAA;AACA,aAAA,cAAA;AACA,mBAAA,MAAAG,WAAAA,WAAA,IAAA;;MAEF;;;IAQF,mBAAA;AAEE,UAAA;AAEE,aAAA,SAAA,cAAA,IAAA;AAAA;AAEAH,sBAAA,MAAA,MAAA,QAAA,mCAAA,kBAAA,MAAA,OAAA;AAAA,MACF;AAAA;;AAIA,UAAA,KAAA,QAAA;AAAA;;AAGA,UAAA,cAAA,KAAA;AAAA;;QAGE,MAAA,KAAA;AAAA;QAEA,UAAA,KAAA,QAAA,KAAA,MAAA;AAAA;MAEF,CAAA;AAEA,WAAA,SAAA;;;AAKA,WAAA,UAAA;;;;;AAQAA,oBAAAA,MAAA,UAAA;AAAA,QACE,SAAA;AAAA;QAEA,SAAA,CAAA,QAAA;AACE,cAAA,IAAA,SAAA;AACEI,qCAAA,KAAA,KAAA,EAAA,GAAA,EAAA,EAAA,EAAA,KAAA,CAAAC,SAAA;AACE,kBAAAA,KAAA,UAAA,KAAA;;;AAGI,uBAAA,UAAA;AAAA,gBACF;AACAL,8BAAA,MAAA,UAAA,EAAA,OAAA,QAAA,MAAA,UAAA,CAAA;AAAA;;cAGF;AAAA;AAEAA,4BAAA,MAAA,UAAA,EAAA,OAAA,YAAA,MAAA,OAAA,CAAA;AAAA,YACF,CAAA;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAA;AAAA;IAGF,eAAA,MAAA;AACE,UAAA,MAAA;;;;MAIA;AAAA;IAGF,cAAA,GAAA;AAEE,UAAA,CAAA,KAAA,iBAAA,IAAA,GAAA;AACE;AAAA,MACF;AAEA,UAAA,KAAA,aAAA;;MAEA;AAEA,YAAA,MAAA,EAAA,cAAA,QAAA;;AAEE,aAAA,eAAA;AAAA,MACF;AAEAA,oBAAA,MAAA,WAAA,EAAA,KAAA,YAAA,IAAA,CAAA;AAAA;IAGF,aAAA,MAAA;AAEE,UAAA,CAAA,KAAA,iBAAA,IAAA,GAAA;AACE;AAAA,MACF;AAEAA,oBAAAA,MAAA,WAAA;AAAA,QACE,KAAA,6BAAA,IAAA,OAAA,KAAA,SAAA,GAAA,SAAA,KAAA,SAAA,QAAA;AAAA,MACF,CAAA;AAAA;;;;;;;;;;;QAcE,YAAA,SAAA,YAAA;AAAA;QAEA,WAAA,EAAA,UAAA,KAAA,YAAA,SAAA;AAAA,MACF,CAAA;AAAA;IAKF,gBAAA;AACE,WAAA,cAAA,CAAA,KAAA;AAAA;IAQF,iBAAA;AAEE,YAAA,cAAA;AAAA;QAEE,eAAA;AAAA;AAEF,WAAA,UAAA,eAAA,WAAA;;;;AAME,YAAA,IAAA,WAAA,OAAA,IAAA,SAAA,KAAA;;AAEE,gBAAA,cAAA;AAAA;YAEE,eAAA,QAAA,SAAA;AAAA,YACA,eAAA;AAAA;AAEF,eAAA,UAAA,eAAA,WAAA;;;AAMAA,wBAAA,MAAA,MAAA,qBAAA;AAAA;;YAGE,UAAA;AAAA,YACA,MAAA,QAAA,QAAA;AAAA;UAEF,CAAA;AAAA;;;;AAKAA,wBAAAA,MAAA,MAAA,qBAAA,SAAA;AAAA,QACF;AAAA,MACF,CAAA,EAAA,MAAA,CAAA,UAAA;AACEA,sBAAA,MAAA,MAAA,SAAA,mCAAA,aAAA,KAAA;AACA,cAAA,YAAA;AAAA;UAEE,eAAA;AAAA,UACA,eAAA;AAAA;AAEF,aAAA,UAAA,eAAA,SAAA;AACA,aAAA,WAAA,EAAA,GAAA,KAAA,UAAA,GAAA;AACAA,sBAAAA,MAAA,MAAA,qBAAA,EAAA,UAAA,CAAA,GAAA,OAAA,GAAA,UAAA,MAAA,CAAA;AAAA,MACF,CAAA;AAAA;;;;;IAOF,WAAA,WAAA;;;AAEE,YAAA,OAAA,IAAA,KAAA,YAAA,GAAA;;;;AAMA,UAAA,CAAA,KAAA,iBAAA,IAAA,GAAA;AACE;AAAA,MACF;;;;;;AAQE,aAAA,UAAA;AAAA;;MAIF;AAAA;IAGF,gBAAA,MAAA;AAEE,UAAA,CAAA,KAAA,iBAAA,IAAA,GAAA;AACE;AAAA,MACF;;;;;YAQM,SAAA,CAAA,QAAA;AACE,kBAAA,IAAA,QAAA;AACEA,8BAAA,MAAA,UAAA,EAAA,OAAA,QAAA,MAAA,UAAA,CAAA;AAAA,cACF;AAAA;YAEF,MAAA,MAAA;AACEA,4BAAA,MAAA,UAAA,EAAA,OAAA,QAAA,MAAA,OAAA,CAAA;AAAA,YACF;AAAA,UACF,CAAA;;;;;;AAaAA,wBAAAA,MAAA,WAAA,EAAA,KAAA,uBAAA,CAAA;;MAEJ;AAAA;;IAIF,gBAAA;AACE,YAAA,SAAA,CAAA,UAAA,SAAA;AACA,aAAA,OAAA,KAAA,MAAA,KAAA;AAAA;IAGF,mBAAA;AACE,UAAA,KAAA,WAAA,GAAA;AACE,eAAA;AAAA,MACF;AACA,aAAA;AAAA;;AAIAG,iBAAAA;IACF;AAAA;EAEF,gBAAA;AAEE,QAAA,CAAA,KAAA,SAAA;AACEH,oBAAAA,MAAA,MAAA,OAAA,mCAAA,YAAA;AACA;AAAA,IACF;;AAIE;AAAA,IACF;AAGA,QAAA,KAAA,KAAA,SAAA,KAAA,YAAA;;;IAGA;AAAA;EAEF,aAAA,GAAA;AACE,QAAA,KAAA,aAAA;AACE;AAAA,IACF;;;;;;AAQE,WAAA,cAAA;AAAA,IACF;;;;;IAMA;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjxCA,GAAG,WAAW,eAAe;"}